#!/bin/bash

echo "🧭 ================================"
echo "🚀 TEST NAVIGATION INTERFACES MODERNES"
echo "🧭 ================================"

# URL de base
BASE_URL="http://localhost:52796"

# Fonction pour tester une URL
test_modern_url() {
    local url="$1"
    local name="$2"
    
    echo -n "🔍 Test $name... "
    
    if curl -s -f "$BASE_URL$url" > /dev/null; then
        echo "✅ OK"
        return 0
    else
        echo "❌ ÉCHEC"
        return 1
    fi
}

# Vérifier que le serveur fonctionne
echo "🌐 Vérification serveur..."
if ! curl -s "$BASE_URL" > /dev/null; then
    echo "❌ Serveur non accessible sur $BASE_URL"
    exit 1
fi
echo "✅ Serveur accessible"
echo ""

# Tests des interfaces MODERNES
echo "🚀 TEST INTERFACES ULTRA-MODERNES:"
echo "=================================="

test_modern_url "/interface-spectaculaire.html" "🏠 Accueil Spectaculaire (MODERNE)"
test_modern_url "/chat-cognitif-ultra-optimise.html" "💬 Chat IA Ultra-Optimisé (ULTRA-MODERNE)"
test_modern_url "/enhanced-interface.html" "🧠 Interface Cerveau Ultra-Avancée (TRÈS AVANCÉE)"
test_modern_url "/electron-optimized-interface.html" "🚀 Interface Electron Optimisée (MODERNE)"

echo ""
echo "🎯 TEST INTERFACES SPÉCIALISÉES MODERNES:"
echo "========================================="

test_modern_url "/brain-3d-spectacular.html" "🧠 Cerveau 3D Spectaculaire"
test_modern_url "/kyber-dashboard.html" "🔧 Kyber Dashboard"
test_modern_url "/qi-test-ultra-avance.html" "🧠 Tests QI Ultra-Avancés"
test_modern_url "/thermal-memory-dashboard.html" "🧠 Dashboard Mémoire Thermique"
test_modern_url "/monitoring-ultra-avance.html" "⚙️ Monitoring Ultra-Avancé"

echo ""
echo "📊 VÉRIFICATION NAVIGATION BOUTONS:"
echo "==================================="

# Vérifier que les boutons de navigation pointent vers les bonnes interfaces
echo "🔍 Vérification configuration navigation..."

if [ -f "public/js/navigation-config.js" ]; then
    echo "✅ Fichier navigation-config.js trouvé"
    
    # Vérifier les URLs modernes dans la config
    if grep -q "chat-cognitif-ultra-optimise.html" public/js/navigation-config.js; then
        echo "✅ Chat ultra-optimisé configuré"
    else
        echo "❌ Chat ultra-optimisé manquant"
    fi
    
    if grep -q "enhanced-interface.html" public/js/navigation-config.js; then
        echo "✅ Interface cerveau ultra-avancée configurée"
    else
        echo "❌ Interface cerveau ultra-avancée manquante"
    fi
    
else
    echo "❌ Fichier navigation-config.js manquant"
fi

echo ""
echo "🎯 TEST BOUTONS RETOUR ACCUEIL:"
echo "==============================="

# Vérifier que les interfaces modernes ont des boutons de retour
echo "🔍 Vérification boutons retour..."

if grep -q "Accueil Spectaculaire" public/chat-cognitif-ultra-optimise.html; then
    echo "✅ Bouton retour dans Chat Ultra-Optimisé"
else
    echo "❌ Bouton retour manquant dans Chat Ultra-Optimisé"
fi

if grep -q "Accueil Spectaculaire" public/enhanced-interface.html; then
    echo "✅ Bouton retour dans Interface Ultra-Avancée"
else
    echo "❌ Bouton retour manquant dans Interface Ultra-Avancée"
fi

echo ""
echo "📊 RÉSUMÉ NAVIGATION MODERNE:"
echo "============================"

# Compter les succès
success_count=0
total_count=9

# Relancer tous les tests pour compter
urls=(
    "/interface-spectaculaire.html"
    "/chat-cognitif-ultra-optimise.html"
    "/enhanced-interface.html"
    "/electron-optimized-interface.html"
    "/brain-3d-spectacular.html"
    "/kyber-dashboard.html"
    "/qi-test-ultra-avance.html"
    "/thermal-memory-dashboard.html"
    "/monitoring-ultra-avance.html"
)

for url in "${urls[@]}"; do
    if curl -s -f "$BASE_URL$url" > /dev/null; then
        success_count=$((success_count + 1))
    fi
done

echo "✅ Interfaces modernes accessibles: $success_count"
echo "❌ Interfaces en erreur: $((total_count - success_count))"
echo "📊 Total testé: $total_count"

# Calculer le pourcentage
percentage=$((success_count * 100 / total_count))
echo "📈 Taux de réussite: $percentage%"

echo ""
if [ $percentage -eq 100 ]; then
    echo "🎉 PARFAIT ! Toutes les interfaces modernes sont accessibles !"
    echo "✅ Navigation vers les interfaces les plus avancées"
    echo "🚀 Application Electron prête avec interfaces ultra-modernes"
elif [ $percentage -ge 90 ]; then
    echo "🎯 EXCELLENT ! Navigation quasi-parfaite vers interfaces modernes"
else
    echo "⚠️ ATTENTION ! Quelques interfaces modernes à corriger"
fi

echo ""
echo "🧭 ================================"
echo "✅ TEST NAVIGATION MODERNE TERMINÉ"
echo "🧭 ================================"
echo ""
echo "🎯 INTERFACES ULTRA-MODERNES DISPONIBLES:"
echo "• 🏠 Accueil: interface-spectaculaire.html"
echo "• 💬 Chat: chat-cognitif-ultra-optimise.html (ULTRA-MODERNE)"
echo "• 🧠 Cerveau: enhanced-interface.html (ULTRA-AVANCÉE)"
echo "• 🚀 Electron: electron-optimized-interface.html"
echo ""
echo "🎉 VOTRE APPLICATION UTILISE MAINTENANT LES INTERFACES LES PLUS MODERNES !"
