#!/bin/bash

echo "🎯 ================================"
echo "✨ LOUNA AI SPECTACULAIRE"
echo "🎯 ================================"

# Arrêter tous les processus existants
echo "🛑 Arrêt des processus existants..."
pkill -f "server-master"
pkill -f "server-working"
pkill -f "electron"
pkill -f "node.*server"

# Attendre un peu
sleep 2

# Nettoyer les fichiers PID
echo "🧹 Nettoyage des fichiers PID..."
rm -f louna-master.pid
rm -f louna-server.pid

# Vérifier que Node.js et npm sont installés
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm n'est pas installé"
    exit 1
fi

# Installer les dépendances si nécessaire
if [ ! -d "node_modules" ]; then
    echo "📦 Installation des dépendances..."
    npm install
fi

echo "🎯 ================================"
echo "🚀 DÉMARRAGE LOUNA AI SPECTACULAIRE"
echo "🎯 ================================"
echo "📱 Interface: interface-spectaculaire.html"
echo "🌐 Serveur: server-master.js"
echo "🎯 Port: 52796"
echo "🎯 ================================"

# Démarrer l'application Electron avec l'interface spectaculaire
echo "🚀 Lancement de l'application Electron..."
npm start

echo "🎯 ================================"
echo "✅ LOUNA AI SPECTACULAIRE TERMINÉ"
echo "🎯 ================================"
