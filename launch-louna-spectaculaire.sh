#!/bin/bash

echo "🎯 ================================"
echo "✨ LOUNA AI SPECTACULAIRE - RANGEMENT COMPLET"
echo "🎯 ================================"

# ÉTAPE 1: NETTOYAGE COMPLET
echo "🧹 NETTOYAGE COMPLET..."
pkill -f "server-master" 2>/dev/null
pkill -f "server-working" 2>/dev/null
pkill -f "electron" 2>/dev/null
pkill -f "node.*server" 2>/dev/null
pkill -f "louna" 2>/dev/null

# Attendre le nettoyage
sleep 3

# Nettoyer les fichiers temporaires
echo "🗑️ Suppression fichiers temporaires..."
rm -f louna-master.pid 2>/dev/null
rm -f louna-server.pid 2>/dev/null
rm -f *.log 2>/dev/null

# ÉTAPE 2: VÉRIFICATIONS
echo "🔍 Vérifications système..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js manquant"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm manquant"
    exit 1
fi

# ÉTAPE 3: PRÉPARATION
echo "📦 Préparation environnement..."
if [ ! -d "node_modules" ]; then
    echo "📥 Installation dépendances..."
    npm install
fi

# ÉTAPE 4: DÉMARRAGE SERVEUR MASTER
echo "🎯 ================================"
echo "🚀 DÉMARRAGE SERVEUR MASTER"
echo "🎯 ================================"
echo "📱 Interface: interface-spectaculaire.html"
echo "🌐 Serveur: server-master.js"
echo "🎯 Port: 52796"

# Démarrer le serveur master en arrière-plan
echo "🔥 Lancement serveur master..."
node server-master.js &
SERVER_PID=$!
echo $SERVER_PID > louna-master.pid

# Attendre que le serveur soit prêt
echo "⏳ Attente serveur..."
sleep 5

# Vérifier que le serveur fonctionne
if curl -s http://localhost:52796 > /dev/null; then
    echo "✅ Serveur master opérationnel"
else
    echo "❌ Erreur serveur master"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

# ÉTAPE 5: LANCEMENT ELECTRON
echo "🎯 ================================"
echo "🚀 LANCEMENT APPLICATION ELECTRON"
echo "🎯 ================================"

# Forcer l'interface spectaculaire
export FORCE_SPECTACULAIRE=true
export SPECTACULAIRE_URL="http://localhost:52796/interface-spectaculaire.html"

echo "🚀 Démarrage Electron avec interface spectaculaire..."
npm start

# ÉTAPE 6: NETTOYAGE FINAL
echo "🎯 ================================"
echo "🧹 NETTOYAGE FINAL"
echo "🎯 ================================"

# Arrêter le serveur
if [ -f louna-master.pid ]; then
    kill $(cat louna-master.pid) 2>/dev/null
    rm -f louna-master.pid
fi

echo "✅ LOUNA AI SPECTACULAIRE TERMINÉ"
echo "🎯 ================================"
