#!/usr/bin/env node

/**
 * 🔍 VÉRIFICATEUR DE NAVIGATION LOUNA AI
 * Vérifie que toutes les pages existent et corrige les liens cassés
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 ================================');
console.log('🧭 VÉRIFICATION NAVIGATION LOUNA AI');
console.log('🔍 ================================');

// Pages définies dans la configuration de navigation
const EXPECTED_PAGES = {
    // 🏠 ACCUEIL
    'interface-spectaculaire.html': '🏠 Accueil Spectaculaire',
    
    // 💬 CHAT
    'chat-cognitif-complet.html': '💬 Chat IA Cognitif',
    'simple-chat.html': '💬 Chat Simple',
    'mcp-chat-interface.html': '💬 Chat MCP',
    'streaming-chat.html': '💬 Chat Streaming',
    'test-chat.html': '💬 Chat Test',
    
    // 🧠 CERVEAU
    'brain-monitoring-complete.html': '🧠 Monitoring Cerveau',
    'brain-visualization.html': '🧠 Visualisation Cerveau',
    'brain-3d-spectacular.html': '🧠 Cerveau 3D Spectaculaire',
    'brain-activity.html': '🧠 Activité Cerveau',
    'neural-interface.html': '🧠 Interface Neurale',
    
    // 🔧 DIAGNOSTIC
    'kyber-dashboard.html': '🔧 Kyber Dashboard',
    'monitoring-ultra-avance.html': '🔧 Monitoring Ultra-Avancé',
    'control-dashboard.html': '🔧 Dashboard Contrôle',
    'hardware-error.html': '🔧 Erreurs Hardware',
    
    // 🧠 TESTS QI
    'qi-test-ultra-avance.html': '🧠 Tests QI Ultra-Avancés',
    'qi-evolution-test.html': '🧠 Tests Évolution QI',
    'qi-manager.html': '🧠 Gestionnaire QI',
    'qi-test-simple.html': '🧠 Tests QI Simple',
    'biological-tests.html': '🧠 Tests Biologiques',
    
    // ✨ GÉNÉRATEURS
    'image-generator.html': '✨ Générateur Images',
    'virtual-code-studio.html': '✨ Studio Code Virtuel',
    'advanced-voice-system.html': '✨ Système Vocal Avancé',
    'face-recognition.html': '✨ Reconnaissance Faciale',
    
    // 🧠 MÉMOIRE THERMIQUE
    'thermal-memory-dashboard.html': '🧠 Dashboard Mémoire Thermique',
    'thermal-paradigm-explorer.html': '🧠 Explorateur Paradigme Thermique',
    'neuron-recovery-emergency.html': '🧠 Récupération Neurones Urgence',
    
    // ⚙️ SYSTÈME
    'documentation.html': '⚙️ Documentation',
    'privacy-control.html': '⚙️ Contrôle Confidentialité',
    'secure-lock.html': '⚙️ Verrouillage Sécurisé',
    
    // 🎓 FORMATION
    'training-interface.html': '🎓 Interface Formation',
    'training-recovery-interface.html': '🎓 Interface Récupération Formation',
    'language-training.html': '🎓 Formation Langage',
    'learning-dashboard.html': '🎓 Dashboard Apprentissage',
    
    // 🔬 TESTS
    'agent-test-suite.html': '🔬 Suite Tests Agent',
    'quick-test.html': '🔬 Test Rapide',
    'test-interface.html': '🔬 Interface Test',
    'mcp-test-interface.html': '🔬 Interface Test MCP'
};

const publicDir = path.join(__dirname, 'public');

console.log(`📁 Répertoire public: ${publicDir}`);
console.log('');

// Vérifier l'existence des pages
console.log('🔍 VÉRIFICATION EXISTENCE DES PAGES:');
console.log('=====================================');

const existingPages = [];
const missingPages = [];

Object.keys(EXPECTED_PAGES).forEach(page => {
    const filePath = path.join(publicDir, page);
    const exists = fs.existsSync(filePath);
    
    if (exists) {
        existingPages.push(page);
        console.log(`✅ ${EXPECTED_PAGES[page]}: ${page}`);
    } else {
        missingPages.push(page);
        console.log(`❌ ${EXPECTED_PAGES[page]}: ${page} - MANQUANT`);
    }
});

console.log('');
console.log('📊 RÉSUMÉ:');
console.log(`✅ Pages existantes: ${existingPages.length}`);
console.log(`❌ Pages manquantes: ${missingPages.length}`);
console.log(`📊 Total attendu: ${Object.keys(EXPECTED_PAGES).length}`);

// Créer des pages de redirection pour les pages manquantes
if (missingPages.length > 0) {
    console.log('');
    console.log('🔧 CRÉATION PAGES DE REDIRECTION:');
    console.log('==================================');
    
    missingPages.forEach(page => {
        const filePath = path.join(publicDir, page);
        const pageTitle = EXPECTED_PAGES[page];
        
        // Déterminer la page de redirection appropriée
        let redirectTo = '/interface-spectaculaire.html';
        
        if (page.includes('chat')) redirectTo = '/chat-cognitif-complet.html';
        else if (page.includes('brain') || page.includes('neural')) redirectTo = '/brain-monitoring-complete.html';
        else if (page.includes('qi') || page.includes('test')) redirectTo = '/qi-test-ultra-avance.html';
        else if (page.includes('thermal') || page.includes('memory')) redirectTo = '/thermal-memory-dashboard.html';
        else if (page.includes('kyber') || page.includes('monitoring')) redirectTo = '/monitoring-ultra-avance.html';
        
        const redirectHTML = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle} - Redirection</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            text-align: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
        }
        h1 {
            background: linear-gradient(135deg, #ff6b9d, #00d4aa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>${pageTitle}</h1>
        <p>Cette page est en cours de développement.</p>
        <p>Redirection automatique vers une page similaire...</p>
        <a href="${redirectTo}" class="btn">🏠 Continuer</a>
        <a href="/interface-spectaculaire.html" class="btn">🏠 Accueil</a>
    </div>
    
    <script>
        // Redirection automatique après 3 secondes
        setTimeout(() => {
            window.location.href = '${redirectTo}';
        }, 3000);
    </script>
</body>
</html>`;

        try {
            fs.writeFileSync(filePath, redirectHTML);
            console.log(`✅ Créé: ${page} → ${redirectTo}`);
        } catch (error) {
            console.log(`❌ Erreur création ${page}: ${error.message}`);
        }
    });
}

// Vérifier les pages existantes dans le répertoire public
console.log('');
console.log('📁 PAGES DISPONIBLES DANS PUBLIC:');
console.log('==================================');

try {
    const files = fs.readdirSync(publicDir)
        .filter(file => file.endsWith('.html'))
        .sort();
    
    files.forEach(file => {
        const isExpected = EXPECTED_PAGES[file];
        const status = isExpected ? '✅' : '📄';
        console.log(`${status} ${file}${isExpected ? ` (${isExpected})` : ''}`);
    });
    
    console.log('');
    console.log(`📊 Total fichiers HTML: ${files.length}`);
    
} catch (error) {
    console.error('❌ Erreur lecture répertoire public:', error.message);
}

console.log('');
console.log('🔧 ================================');
console.log('✅ VÉRIFICATION TERMINÉE');
console.log('🔧 ================================');
console.log('');
console.log('🎯 Actions recommandées:');
console.log('• Redémarrer le serveur pour prendre en compte les nouvelles pages');
console.log('• Tester la navigation depuis l\'interface spectaculaire');
console.log('• Vérifier que tous les boutons fonctionnent correctement');
