#!/usr/bin/env node

/**
 * 🧪 TEST SYSTÉMATIQUE DE CHAQUE BOUTON
 * Teste CHAQUE bouton de CHAQUE interface pour vérifier qu'il fonctionne correctement
 */

const puppeteer = require('puppeteer');

console.log('🧪 ================================');
console.log('🔍 TEST SYSTÉMATIQUE CHAQUE BOUTON');
console.log('🧪 ================================');

const BASE_URL = 'http://localhost:52796';

// Liste des interfaces à tester
const INTERFACES_TO_TEST = [
    {
        name: '🏠 Interface Spectaculaire',
        url: '/interface-spectaculaire.html',
        buttons: [
            { selector: 'button[onclick="showDashboard()"]', name: '🏠 Tableau de bord' },
            { selector: 'button[onclick="showChat()"]', name: '💬 Chat IA' },
            { selector: 'button[onclick="showBrain()"]', name: '🧠 Cerveau artificiel' },
            { selector: 'button[onclick="showDiagnostic()"]', name: '🔧 Diagnostic' },
            { selector: 'button[onclick="showIQTests()"]', name: '🧠 Tests de QI' },
            { selector: 'button[onclick="showGenerators()"]', name: '✨ Générateurs' },
            { selector: 'button[onclick="showBrain3D()"]', name: '🧠 Cerveau 3D' },
            { selector: 'button[onclick="showThermalMemory()"]', name: '🧠 Mémoire thermique' },
            { selector: 'button[onclick="showInfo()"]', name: '⚙️ Infos' }
        ]
    },
    {
        name: '💬 Chat Ultra-Optimisé',
        url: '/chat-cognitif-ultra-optimise.html',
        buttons: [
            { selector: 'button[onclick="goHome()"]', name: '🏠 Accueil Spectaculaire' }
        ]
    },
    {
        name: '🧠 Interface Ultra-Avancée',
        url: '/enhanced-interface.html',
        buttons: [
            { selector: 'button[onclick="goHome()"]', name: '🏠 Accueil Spectaculaire' }
        ]
    },
    {
        name: '🧠 Cerveau 3D Spectaculaire',
        url: '/brain-3d-spectacular.html',
        buttons: [
            { selector: 'button[onclick*="interface-spectaculaire"]', name: '🏠 Retour Accueil' }
        ]
    },
    {
        name: '🔧 Kyber Dashboard',
        url: '/kyber-dashboard.html',
        buttons: [
            { selector: 'button[onclick*="interface-spectaculaire"]', name: '🏠 Retour Accueil' }
        ]
    },
    {
        name: '🧠 Tests QI Ultra-Avancés',
        url: '/qi-test-ultra-avance.html',
        buttons: [
            { selector: 'button[onclick*="interface-spectaculaire"]', name: '🏠 Retour Accueil' }
        ]
    },
    {
        name: '🧠 Dashboard Mémoire Thermique',
        url: '/thermal-memory-dashboard.html',
        buttons: [
            { selector: 'a[href="/interface-spectaculaire.html"]', name: '🏠 Accueil Spectaculaire' }
        ]
    }
];

async function testInterface(browser, interfaceConfig) {
    console.log(`\n🔍 TEST: ${interfaceConfig.name}`);
    console.log(`📄 URL: ${BASE_URL}${interfaceConfig.url}`);
    
    const page = await browser.newPage();
    
    try {
        // Charger la page
        await page.goto(`${BASE_URL}${interfaceConfig.url}`, { 
            waitUntil: 'networkidle0',
            timeout: 10000 
        });
        
        console.log(`✅ Page chargée: ${interfaceConfig.name}`);
        
        // Tester chaque bouton
        for (const button of interfaceConfig.buttons) {
            console.log(`\n  🔘 Test bouton: ${button.name}`);
            console.log(`     Sélecteur: ${button.selector}`);
            
            try {
                // Vérifier si le bouton existe
                const buttonExists = await page.$(button.selector);
                if (!buttonExists) {
                    console.log(`  ❌ BOUTON NON TROUVÉ: ${button.name}`);
                    continue;
                }
                
                console.log(`  ✅ Bouton trouvé: ${button.name}`);
                
                // Cliquer sur le bouton
                await page.click(button.selector);
                
                // Attendre un peu pour voir la navigation
                await page.waitForTimeout(2000);
                
                // Vérifier l'URL actuelle
                const currentUrl = page.url();
                console.log(`  🌐 URL après clic: ${currentUrl}`);
                
                // Vérifier si on est arrivé sur la bonne page
                if (currentUrl.includes('futuristic-interface') || currentUrl.includes('interface-futuriste')) {
                    console.log(`  ❌ PROBLÈME: Redirigé vers interface futuriste !`);
                    console.log(`     🚨 BOUTON DÉFAILLANT: ${button.name}`);
                } else if (currentUrl.includes('interface-spectaculaire')) {
                    console.log(`  ✅ CORRECT: Redirigé vers interface spectaculaire`);
                } else if (currentUrl !== `${BASE_URL}${interfaceConfig.url}`) {
                    console.log(`  ✅ NAVIGATION: Vers une autre interface moderne`);
                } else {
                    console.log(`  ⚠️ AUCUNE NAVIGATION: Bouton sans effet`);
                }
                
                // Revenir à la page d'origine pour tester le bouton suivant
                if (currentUrl !== `${BASE_URL}${interfaceConfig.url}`) {
                    await page.goto(`${BASE_URL}${interfaceConfig.url}`, { 
                        waitUntil: 'networkidle0',
                        timeout: 10000 
                    });
                }
                
            } catch (error) {
                console.log(`  ❌ ERREUR bouton ${button.name}: ${error.message}`);
            }
        }
        
    } catch (error) {
        console.log(`❌ ERREUR chargement ${interfaceConfig.name}: ${error.message}`);
    }
    
    await page.close();
}

async function runTests() {
    console.log('🚀 Démarrage des tests...');
    
    const browser = await puppeteer.launch({ 
        headless: false,  // Mode visible pour voir ce qui se passe
        defaultViewport: { width: 1200, height: 800 }
    });
    
    try {
        // Tester chaque interface
        for (const interfaceConfig of INTERFACES_TO_TEST) {
            await testInterface(browser, interfaceConfig);
        }
        
        console.log('\n🎯 ================================');
        console.log('📊 RÉSUMÉ DES TESTS');
        console.log('🎯 ================================');
        console.log('✅ Tests terminés pour toutes les interfaces');
        console.log('🔍 Vérifiez les logs ci-dessus pour les problèmes détectés');
        console.log('❌ Recherchez les mentions "PROBLÈME" et "BOUTON DÉFAILLANT"');
        
    } catch (error) {
        console.error('❌ Erreur générale:', error);
    } finally {
        await browser.close();
    }
}

// Vérifier que le serveur est accessible avant de commencer
async function checkServer() {
    try {
        const response = await fetch(BASE_URL);
        if (response.ok) {
            console.log('✅ Serveur accessible, démarrage des tests...');
            return true;
        } else {
            console.log('❌ Serveur non accessible');
            return false;
        }
    } catch (error) {
        console.log('❌ Impossible de contacter le serveur:', error.message);
        return false;
    }
}

// Démarrer les tests
async function main() {
    const serverOk = await checkServer();
    if (serverOk) {
        await runTests();
    } else {
        console.log('💡 Assurez-vous que le serveur fonctionne sur', BASE_URL);
        process.exit(1);
    }
}

main().catch(console.error);
