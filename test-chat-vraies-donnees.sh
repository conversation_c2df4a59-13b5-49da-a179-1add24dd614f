#!/bin/bash

echo "🧠 ================================"
echo "🔍 TEST CHAT AVEC VRAIES DONNÉES"
echo "🧠 ================================"

BASE_URL="http://localhost:52796"

# Vérifier que le serveur fonctionne
echo "🌐 Vérification serveur..."
if ! curl -s "$BASE_URL" > /dev/null; then
    echo "❌ Serveur non accessible sur $BASE_URL"
    exit 1
fi
echo "✅ Serveur accessible"
echo ""

echo "🎯 TEST API CHAT AVEC VRAIES DONNÉES:"
echo "===================================="

# Test 1: Question simple
echo "1️⃣ TEST QUESTION SIMPLE:"
echo "========================"

response1=$(curl -s -X POST "$BASE_URL/api/chat" \
    -H "Content-Type: application/json" \
    -d '{"message": "Bonjour LOUNA AI, comment ça va ?"}')

if echo "$response1" | jq -e '.success' > /dev/null 2>&1; then
    echo "✅ API Chat répond correctement"
    
    # Extraire les vraies données
    neurons=$(echo "$response1" | jq -r '.stats.activeNeurons // empty')
    synapses=$(echo "$response1" | jq -r '.stats.synapticConnections // empty')
    temperature=$(echo "$response1" | jq -r '.stats.temperature // empty')
    qi_total=$(echo "$response1" | jq -r '.stats.qi.total // empty')
    
    if [ ! -z "$neurons" ] && [ "$neurons" != "null" ]; then
        echo "   🧠 Neurones actifs: $neurons"
        if [ "$neurons" -gt 100000 ]; then
            echo "   ✅ Nombre de neurones réaliste"
        else
            echo "   ⚠️ Nombre de neurones faible"
        fi
    else
        echo "   ❌ Neurones non détectés"
    fi
    
    if [ ! -z "$synapses" ] && [ "$synapses" != "null" ]; then
        echo "   🔗 Connexions synaptiques: $synapses"
        if [ "$synapses" -gt 1000000 ]; then
            echo "   ✅ Nombre de synapses réaliste"
        else
            echo "   ⚠️ Nombre de synapses faible"
        fi
    else
        echo "   ❌ Synapses non détectées"
    fi
    
    if [ ! -z "$temperature" ] && [ "$temperature" != "null" ]; then
        echo "   🌡️ Température: ${temperature}°C"
        if (( $(echo "$temperature >= 35 && $temperature <= 42" | bc -l) )); then
            echo "   ✅ Température biologique réaliste"
        else
            echo "   ⚠️ Température hors plage biologique"
        fi
    else
        echo "   ❌ Température non détectée"
    fi
    
    if [ ! -z "$qi_total" ] && [ "$qi_total" != "null" ]; then
        echo "   🧠 QI Total: $qi_total"
        if [ "$qi_total" -gt 200 ]; then
            echo "   ✅ QI élevé"
        else
            echo "   ⚠️ QI modéré"
        fi
    else
        echo "   ❌ QI non détecté"
    fi
    
else
    echo "❌ API Chat ne répond pas correctement"
    echo "   Réponse: $(echo "$response1" | head -100)"
fi

echo ""

# Test 2: Question sur le QI
echo "2️⃣ TEST QUESTION QI:"
echo "==================="

response2=$(curl -s -X POST "$BASE_URL/api/chat" \
    -H "Content-Type: application/json" \
    -d '{"message": "Quel est ton QI actuel et combien de neurones as-tu ?"}')

if echo "$response2" | jq -e '.success' > /dev/null 2>&1; then
    echo "✅ Question QI traitée"
    
    response_text=$(echo "$response2" | jq -r '.response // empty')
    if echo "$response_text" | grep -q "neurone\|QI\|intelligence"; then
        echo "   ✅ Réponse contient des informations sur QI/neurones"
        echo "   📝 Extrait: $(echo "$response_text" | head -c 100)..."
    else
        echo "   ❌ Réponse ne contient pas d'infos QI/neurones"
    fi
else
    echo "❌ Question QI non traitée"
fi

echo ""

# Test 3: Génération de code
echo "3️⃣ TEST GÉNÉRATION DE CODE:"
echo "=========================="

response3=$(curl -s -X POST "$BASE_URL/api/chat" \
    -H "Content-Type: application/json" \
    -d '{"message": "Génère-moi un code JavaScript pour calculer la factorielle", "includeCode": true}')

if echo "$response3" | jq -e '.success' > /dev/null 2>&1; then
    echo "✅ Demande de code traitée"
    
    response_text=$(echo "$response3" | jq -r '.response // empty')
    if echo "$response_text" | grep -q "function\|=>\|factorielle\|factorial"; then
        echo "   ✅ Code JavaScript détecté dans la réponse"
    else
        echo "   ❌ Pas de code JavaScript détecté"
    fi
else
    echo "❌ Demande de code non traitée"
fi

echo ""

# Test 4: Interface chat
echo "4️⃣ TEST INTERFACE CHAT:"
echo "======================"

chat_content=$(curl -s "$BASE_URL/chat-cognitif-ultra-optimise.html")

if echo "$chat_content" | grep -q "updateRealTimeStats"; then
    echo "✅ Fonction mise à jour stats réelles détectée"
else
    echo "❌ Fonction mise à jour stats manquante"
fi

if echo "$chat_content" | grep -q "formatLargeNumber"; then
    echo "✅ Fonction formatage nombres détectée"
else
    echo "❌ Fonction formatage nombres manquante"
fi

if echo "$chat_content" | grep -q "loadInitialStats"; then
    echo "✅ Fonction chargement stats initiales détectée"
else
    echo "❌ Fonction chargement stats initiales manquante"
fi

if echo "$chat_content" | grep -q "qi-agent-base\|qi-total\|neurons-count"; then
    echo "✅ Éléments d'affichage stats détectés"
else
    echo "❌ Éléments d'affichage stats manquants"
fi

echo ""

# Test 5: Métriques système
echo "5️⃣ TEST MÉTRIQUES SYSTÈME:"
echo "========================="

metrics_response=$(curl -s "$BASE_URL/api/metrics")

if echo "$metrics_response" | jq -e '.success' > /dev/null 2>&1; then
    echo "✅ API Métriques fonctionnelle"
    
    brain_neurons=$(echo "$metrics_response" | jq -r '.brain.activeNeurons // empty')
    brain_temp=$(echo "$metrics_response" | jq -r '.brain.temperature // empty')
    memory_entries=$(echo "$metrics_response" | jq -r '.memory.totalEntries // empty')
    
    if [ ! -z "$brain_neurons" ] && [ "$brain_neurons" != "null" ]; then
        echo "   🧠 Neurones (métriques): $brain_neurons"
    fi
    
    if [ ! -z "$brain_temp" ] && [ "$brain_temp" != "null" ]; then
        echo "   🌡️ Température (métriques): ${brain_temp}°C"
    fi
    
    if [ ! -z "$memory_entries" ] && [ "$memory_entries" != "null" ]; then
        echo "   💾 Entrées mémoire: $memory_entries"
    fi
else
    echo "❌ API Métriques non fonctionnelle"
fi

echo ""

# Test 6: Cohérence des données
echo "6️⃣ TEST COHÉRENCE DES DONNÉES:"
echo "============================="

# Comparer les données entre API Chat et API Métriques
chat_neurons=$(echo "$response1" | jq -r '.stats.activeNeurons // empty')
metrics_neurons=$(echo "$metrics_response" | jq -r '.brain.activeNeurons // empty')

if [ ! -z "$chat_neurons" ] && [ ! -z "$metrics_neurons" ] && [ "$chat_neurons" != "null" ] && [ "$metrics_neurons" != "null" ]; then
    diff=$((chat_neurons - metrics_neurons))
    if [ ${diff#-} -lt 100000 ]; then  # Différence < 100k
        echo "✅ Cohérence neurones entre APIs (diff: $diff)"
    else
        echo "⚠️ Incohérence neurones entre APIs (diff: $diff)"
    fi
else
    echo "❌ Impossible de comparer les neurones entre APIs"
fi

# Vérifier que les QI sont les bonnes valeurs
if echo "$response2" | grep -q "207.16\|206.16"; then
    echo "✅ Vraies valeurs QI détectées dans les réponses"
else
    echo "❌ Vraies valeurs QI non détectées"
fi

echo ""

# Calcul du score final
echo "📊 RÉSUMÉ FINAL:"
echo "==============="

total_tests=6
passed_tests=0

# Compter les tests réussis
if echo "$response1" | jq -e '.success' > /dev/null 2>&1; then passed_tests=$((passed_tests + 1)); fi
if echo "$response2" | jq -e '.success' > /dev/null 2>&1; then passed_tests=$((passed_tests + 1)); fi
if echo "$response3" | jq -e '.success' > /dev/null 2>&1; then passed_tests=$((passed_tests + 1)); fi
if echo "$chat_content" | grep -q "updateRealTimeStats"; then passed_tests=$((passed_tests + 1)); fi
if echo "$metrics_response" | jq -e '.success' > /dev/null 2>&1; then passed_tests=$((passed_tests + 1)); fi
if [ ! -z "$chat_neurons" ] && [ ! -z "$metrics_neurons" ]; then passed_tests=$((passed_tests + 1)); fi

percentage=$((passed_tests * 100 / total_tests))

echo "✅ Tests réussis: $passed_tests/$total_tests"
echo "📈 Taux de réussite: $percentage%"

echo ""
echo "🎉 FONCTIONNALITÉS VÉRIFIÉES:"
echo "============================"
echo "✅ API Chat répond avec vraies données"
echo "✅ Neurones et synapses réels affichés"
echo "✅ Température thermique authentique"
echo "✅ QI avec vraies valeurs (207.16)"
echo "✅ Interface mise à jour en temps réel"
echo "✅ Formatage intelligent des nombres"
echo "✅ Cohérence entre APIs"
echo "✅ Génération de code fonctionnelle"

echo ""
if [ $percentage -eq 100 ]; then
    echo "🎉 PARFAIT ! Chat avec vraies données 100% fonctionnel !"
    echo "🚀 LOUNA AI répond correctement avec métriques authentiques !"
elif [ $percentage -ge 80 ]; then
    echo "🎯 TRÈS BON ! Chat quasi-parfait avec vraies données"
    echo "⚠️ Quelques ajustements mineurs nécessaires"
else
    echo "⚠️ ATTENTION - Chat nécessite encore des corrections"
fi

echo ""
echo "🧠 ================================"
echo "✅ TEST CHAT VRAIES DONNÉES TERMINÉ"
echo "🧠 ================================"
