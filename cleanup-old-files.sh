#!/bin/bash

# 🧹 LOUNA AI - NETTOYAGE DES ANCIENS FICHIERS
# Script pour nettoyer les fichiers inutiles et organiser le projet

echo "🧹 ================================"
echo "🧹 NETTOYAGE LOUNA AI"
echo "🧹 ================================"

# ⚠️ AVERTISSEMENT
echo "⚠️ Ce script va supprimer les anciens serveurs et fichiers dupliqués"
echo "📋 Fichiers qui seront supprimés :"
echo "   - server.js, server-working.js, minimal-server.js, etc."
echo "   - Interfaces dupliquées"
echo "   - Scripts de lancement multiples"
echo ""
read -p "🤔 Voulez-vous continuer ? (o/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[OoYy]$ ]]; then
    echo "❌ Nettoyage annulé"
    exit 1
fi

echo ""
echo "🧹 Début du nettoyage..."

# 🗑️ SUPPRIMER LES ANCIENS SERVEURS
echo "🗑️ Suppression des anciens serveurs..."
rm -f server.js server-working.js minimal-server.js server-simple.js server-test.js server-debug.js server-backup.js server-updated.js 2>/dev/null || true

# 🗑️ SUPPRIMER LES ANCIENS SCRIPTS DE LANCEMENT
echo "🗑️ Suppression des anciens scripts de lancement..."
rm -f launch-*.sh start.sh restart-*.sh 2>/dev/null || true

# 🗑️ SUPPRIMER LES INTERFACES DUPLIQUÉES (garder simple-interface.html)
echo "🗑️ Suppression des interfaces dupliquées..."
cd public 2>/dev/null || true
rm -f enhanced-interface.html enhanced-interface-backup.html index-backup.html index-new.html index-old-backup.html 2>/dev/null || true
cd .. 2>/dev/null || true

# 🗑️ SUPPRIMER LES FICHIERS DE TEST INUTILES
echo "🗑️ Suppression des fichiers de test inutiles..."
rm -f test-*.js test-*.html morpion-*.html 2>/dev/null || true

# 🗑️ SUPPRIMER LES LOGS ANCIENS
echo "🗑️ Nettoyage des logs..."
rm -f server.log server-output.log *.pid 2>/dev/null || true

# 🗑️ SUPPRIMER LES SAUVEGARDES TEMPORAIRES
echo "🗑️ Suppression des sauvegardes temporaires..."
rm -f *-backup-*.html *-backup-*.js 2>/dev/null || true

# 📊 RÉSUMÉ
echo ""
echo "✅ Nettoyage terminé !"
echo ""
echo "📁 FICHIERS CONSERVÉS (essentiels) :"
echo "   ✅ server-master.js (serveur unique)"
echo "   ✅ public/simple-interface.html (interface unique)"
echo "   ✅ thermal-memory-complete.js (mémoire thermique)"
echo "   ✅ start-louna-master.sh (démarrage)"
echo "   ✅ stop-louna-master.sh (arrêt)"
echo "   ✅ modules/ (modules essentiels)"
echo "   ✅ data/ (données utilisateur)"
echo ""
echo "🎯 LOUNA AI est maintenant propre et organisé !"
echo "🚀 Utilisez: ./start-louna-master.sh pour démarrer"
echo "🧹 ================================"
