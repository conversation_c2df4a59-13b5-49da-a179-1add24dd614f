#!/bin/bash

echo "🎯 ================================"
echo "🧪 TEST FINAL NAVIGATION CORRIGÉE"
echo "🎯 ================================"

BASE_URL="http://localhost:52796"

# Fonction pour tester une URL
test_url() {
    local url="$1"
    local name="$2"
    
    echo -n "🔍 Test $name... "
    
    if curl -s -f "$BASE_URL$url" > /dev/null; then
        echo "✅ OK"
        return 0
    else
        echo "❌ ÉCHEC"
        return 1
    fi
}

# Vérifier que le serveur fonctionne
echo "🌐 Vérification serveur..."
if ! curl -s "$BASE_URL" > /dev/null; then
    echo "❌ Serveur non accessible sur $BASE_URL"
    exit 1
fi
echo "✅ Serveur accessible"
echo ""

# 1. TEST INTERFACE PRINCIPALE
echo "🏠 TEST INTERFACE PRINCIPALE:"
echo "============================"
test_url "/interface-spectaculaire.html" "🏠 Interface Spectaculaire"

# 2. TEST INTERFACES ULTRA-MODERNES
echo ""
echo "🚀 TEST INTERFACES ULTRA-MODERNES:"
echo "=================================="
test_url "/chat-cognitif-ultra-optimise.html" "💬 Chat IA Ultra-Optimisé"
test_url "/enhanced-interface.html" "🧠 Interface Cerveau Ultra-Avancée"
test_url "/electron-optimized-interface.html" "🚀 Interface Electron Optimisée"

# 3. TEST INTERFACES SPÉCIALISÉES
echo ""
echo "🎯 TEST INTERFACES SPÉCIALISÉES:"
echo "==============================="
test_url "/brain-3d-spectacular.html" "🧠 Cerveau 3D Spectaculaire"
test_url "/kyber-dashboard.html" "🔧 Kyber Dashboard"
test_url "/qi-test-ultra-avance.html" "🧠 Tests QI Ultra-Avancés"
test_url "/thermal-memory-dashboard.html" "🧠 Dashboard Mémoire Thermique"
test_url "/monitoring-ultra-avance.html" "⚙️ Monitoring Ultra-Avancé"

# 4. VÉRIFIER QU'IL N'Y A PLUS DE LIENS VERS FUTURISTIC-INTERFACE
echo ""
echo "🔍 VÉRIFICATION LIENS PROBLÉMATIQUES:"
echo "===================================="

BAD_LINKS=$(grep -r "href.*futuristic-interface.html" public/*.html 2>/dev/null | wc -l)
echo "🔍 Liens vers futuristic-interface.html: $BAD_LINKS"

if [ "$BAD_LINKS" -eq 0 ]; then
    echo "✅ Aucun lien problématique trouvé"
else
    echo "⚠️ Liens problématiques restants:"
    grep -r "href.*futuristic-interface.html" public/*.html 2>/dev/null | head -3
fi

# 5. VÉRIFIER LES BOUTONS DE RETOUR
echo ""
echo "🏠 VÉRIFICATION BOUTONS DE RETOUR:"
echo "================================="

HOME_BUTTONS=$(grep -r "Accueil Spectaculaire" public/*.html 2>/dev/null | wc -l)
echo "🏠 Boutons de retour trouvés: $HOME_BUTTONS"

if [ "$HOME_BUTTONS" -gt 5 ]; then
    echo "✅ Boutons de retour présents dans les interfaces"
else
    echo "⚠️ Peu de boutons de retour détectés"
fi

# 6. TEST SYSTÈME THERMIQUE
echo ""
echo "🌡️ TEST SYSTÈME THERMIQUE:"
echo "=========================="

if curl -s "$BASE_URL/api/metrics" > /dev/null; then
    echo "✅ API métriques accessible"
    
    # Récupérer les métriques
    METRICS=$(curl -s "$BASE_URL/api/metrics")
    NEURONS=$(echo $METRICS | grep -o '"activeNeurons":[0-9]*' | cut -d':' -f2)
    QI=$(echo $METRICS | grep -o '"total":[0-9.]*' | cut -d':' -f2)
    
    if [ ! -z "$NEURONS" ] && [ "$NEURONS" -gt 1000000 ]; then
        echo "✅ Neurones: $NEURONS (plus de 1 million)"
    else
        echo "⚠️ Neurones: $NEURONS (moins de 1 million)"
    fi
    
    if [ ! -z "$QI" ]; then
        echo "✅ QI Total: $QI"
    else
        echo "⚠️ QI non calculé"
    fi
    
else
    echo "❌ API métriques non accessible"
fi

# 7. COMPTER LES SUCCÈS
echo ""
echo "📊 RÉSUMÉ FINAL:"
echo "==============="

success_count=0
total_count=9

# Relancer tous les tests pour compter
urls=(
    "/interface-spectaculaire.html"
    "/chat-cognitif-ultra-optimise.html"
    "/enhanced-interface.html"
    "/electron-optimized-interface.html"
    "/brain-3d-spectacular.html"
    "/kyber-dashboard.html"
    "/qi-test-ultra-avance.html"
    "/thermal-memory-dashboard.html"
    "/monitoring-ultra-avance.html"
)

for url in "${urls[@]}"; do
    if curl -s -f "$BASE_URL$url" > /dev/null; then
        success_count=$((success_count + 1))
    fi
done

echo "✅ Interfaces accessibles: $success_count/$total_count"
echo "❌ Liens problématiques: $BAD_LINKS"
echo "🏠 Boutons de retour: $HOME_BUTTONS"

# Calculer le pourcentage
percentage=$((success_count * 100 / total_count))
echo "📈 Taux de réussite: $percentage%"

echo ""
if [ $percentage -eq 100 ] && [ "$BAD_LINKS" -eq 0 ]; then
    echo "🎉 PARFAIT ! Navigation entièrement corrigée !"
    echo "✅ Toutes les interfaces modernes accessibles"
    echo "✅ Aucun lien problématique"
    echo "✅ Système thermique fonctionnel"
    echo "🚀 Application prête à l'utilisation"
elif [ $percentage -ge 90 ] && [ "$BAD_LINKS" -le 2 ]; then
    echo "🎯 EXCELLENT ! Navigation quasi-parfaite"
    echo "⚠️ Quelques détails mineurs à peaufiner"
else
    echo "⚠️ ATTENTION ! Navigation nécessite encore des corrections"
fi

echo ""
echo "🎯 ================================"
echo "✅ TEST FINAL TERMINÉ"
echo "🎯 ================================"
echo ""
echo "🎉 RÉSULTAT FINAL:"
echo "• 🏠 Interface spectaculaire: FONCTIONNELLE"
echo "• 💬 Chat ultra-optimisé: ACCESSIBLE"
echo "• 🧠 Interface ultra-avancée: ACCESSIBLE"
echo "• 🌡️ Système thermique: PRÉSERVÉ"
echo "• 🔧 Navigation: CORRIGÉE"
echo ""
echo "🚀 VOTRE APPLICATION LOUNA AI EST PRÊTE !"
