const { app, BrowserWindow, <PERSON>u, ipc<PERSON>ain, dialog, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// 🤖 INTÉGRATION DEEPSEEK DIRECTE DANS ELECTRON
const RealDeepSeekIntegration = require('./real-deepseek-integration');
const ThermalMemoryComplete = require('./thermal-memory-complete');

// Configuration de l'application
const isDev = process.env.ELECTRON_IS_DEV === '1';
const serverPort = 0; // Port automatique - laisser le système choisir

let mainWindow;
let serverProcess;
let actualPort = null;

// 🧠 INSTANCES GLOBALES POUR ELECTRON
let electronDeepSeek = null;
let electronThermalMemory = null;

// Configuration de sécurité
app.commandLine.appendSwitch('--disable-web-security');
app.commandLine.appendSwitch('--allow-running-insecure-content');
app.commandLine.appendSwitch('--disable-features', 'VizDisplayCompositor');

// 🧠 INITIALISATION DES SYSTÈMES IA DANS ELECTRON
async function initializeAISystems() {
    console.log('🧠 Initialisation des systèmes IA dans Electron...');

    try {
        // Initialiser la mémoire thermique
        console.log('🌡️ Initialisation mémoire thermique Electron...');
        electronThermalMemory = new ThermalMemoryComplete();
        await electronThermalMemory.initialize();

        // Initialiser DeepSeek
        console.log('🤖 Initialisation DeepSeek R1 8B Electron...');
        electronDeepSeek = new RealDeepSeekIntegration();

        // Vérifier si la méthode connect existe
        if (electronDeepSeek && typeof electronDeepSeek.connect === 'function') {
            try {
                await electronDeepSeek.connect();
                console.log('✅ DeepSeek R1 8B connecté et prêt');
            } catch (error) {
                console.log('⚠️ DeepSeek connexion échouée, mode de base activé');
                console.log('💡 Pour activer DeepSeek: ollama serve && ollama pull deepseek-r1:8b');
            }
        } else {
            console.log('⚠️ DeepSeek connect non disponible, utilisation du mode de base');
        }

        console.log('✅ Systèmes IA Electron initialisés avec succès !');
        return true;

    } catch (error) {
        console.error('❌ Erreur initialisation IA Electron:', error);
        return false;
    }
}

// Fonction pour démarrer le serveur Node.js
function startServer() {
    return new Promise((resolve, reject) => {
        console.log('🚀 Démarrage du serveur Louna AI...');
        
        // FORCER LE SERVEUR MASTER SPECTACULAIRE
        const serverScript = path.join(__dirname, 'server-master.js');
        console.log('🚀 DÉMARRAGE FORCÉ SERVEUR MASTER SPECTACULAIRE...');
        console.log(`📁 Script: ${serverScript}`);

        serverProcess = spawn('node', [serverScript], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                PORT: '52796',
                FORCE_SPECTACULAIRE: 'true'
            },
            cwd: __dirname
        });

        let serverStarted = false;

        serverProcess.stdout.on('data', (data) => {
            const output = data.toString();
            console.log('Server:', output);

            // Chercher le port dans les logs - plusieurs patterns
            const portPatterns = [
                /Port utilisé: (\d+)/,
                /port (\d+)/,
                /localhost:(\d+)/,
                /server.*port.*?(\d+)/i,
                /listening.*?(\d+)/i
            ];

            for (const pattern of portPatterns) {
                const portMatch = output.match(pattern);
                if (portMatch) {
                    const port = parseInt(portMatch[1]);
                    if (port > 1000 && port < 65536) { // Port valide
                        actualPort = port;
                        console.log(`✅ Port détecté: ${actualPort}`);
                        if (!serverStarted) {
                            serverStarted = true;
                            resolve(actualPort);
                        }
                        return;
                    }
                }
            }

            // Démarrage basé sur les messages de succès
            const successPatterns = [
                'SERVEUR LOUNA AI DÉMARRÉ',
                'LOUNA DÉMARRÉ AVEC SUCCÈS',
                'Routes API configurées',
                'Application Louna démarrée',
                'Toutes les fonctionnalités sont maintenant disponibles',
                'port 52796',
                'Cerveau autonome: ACTIF'
            ];

            for (const pattern of successPatterns) {
                if (output.includes(pattern) && !serverStarted) {
                    // Utiliser un port par défaut si aucun port n'a été détecté
                    if (!actualPort) {
                        actualPort = 52796; // Port par défaut de la config
                    }
                    console.log(`✅ Serveur démarré (pattern: ${pattern}) sur le port ${actualPort}`);
                    serverStarted = true;
                    resolve(actualPort);
                    return;
                }
            }
        });

        serverProcess.stderr.on('data', (data) => {
            console.error('Server Error:', data.toString());
        });

        serverProcess.on('error', (error) => {
            console.error('❌ Erreur serveur:', error);
            if (!serverStarted) {
                reject(error);
            }
        });

        serverProcess.on('exit', (code) => {
            console.log(`🛑 Serveur arrêté avec le code ${code}`);
            if (!serverStarted && code !== 0) {
                reject(new Error(`Serveur arrêté avec le code ${code}`));
            }
        });

        // Timeout de sécurité
        setTimeout(() => {
            if (!serverStarted) {
                console.log('⏰ Timeout - Utilisation du port par défaut');
                actualPort = 52796;
                resolve(actualPort);
            }
        }, 30000);
    });
}

// Fonction pour créer la fenêtre principale
function createMainWindow(port) {
    console.log(`🖥️ Création de la fenêtre principale pour le port ${port}`);
    
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 800,
        icon: path.join(__dirname, 'public', 'img', 'louna-icon.png'),
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false,
            allowRunningInsecureContent: true,
            experimentalFeatures: true
        },
        titleBarStyle: 'default',
        show: false,
        backgroundColor: '#1a1a2e',
        title: '🧠 LOUNA AI Ultra-Autonome - Interface Spectaculaire'
    });

    // FORCER L'INTERFACE SPECTACULAIRE SUR LE BON PORT
    const spectacularUrl = `http://localhost:52796/interface-spectaculaire.html`;
    console.log(`🌐 CHARGEMENT FORCÉ INTERFACE SPECTACULAIRE: ${spectacularUrl}`);

    // Vider complètement le cache
    mainWindow.webContents.session.clearCache();
    mainWindow.webContents.session.clearStorageData();

    // Charger l'interface spectaculaire
    mainWindow.loadURL(spectacularUrl);

    // Afficher la fenêtre quand elle est prête
    mainWindow.once('ready-to-show', () => {
        console.log('✅ Fenêtre prête - Affichage');
        mainWindow.show();
        
        if (isDev) {
            mainWindow.webContents.openDevTools();
        }
    });

    // Gestion de la fermeture
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // Gestion des liens externes
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // Gestion des erreurs de chargement
    mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
        console.error(`❌ Erreur de chargement: ${errorCode} - ${errorDescription}`);
        console.error(`URL: ${validatedURL}`);
        
        // Réessayer avec l'interface spectaculaire
        setTimeout(() => {
            console.log('🔄 Nouvelle tentative - INTERFACE SPECTACULAIRE...');
            const spectacularUrl = `http://localhost:52796/interface-spectaculaire.html`;
            mainWindow.webContents.session.clearCache();
            mainWindow.webContents.session.clearStorageData();
            mainWindow.loadURL(spectacularUrl);
        }, 2000);
    });

    return mainWindow;
}

// 🤖 CONFIGURATION DES CANAUX IPC POUR L'IA
function setupAIIPCChannels() {
    console.log('📡 Configuration des canaux IPC pour l\'IA...');

    // Canal pour chat avec DeepSeek
    ipcMain.handle('deepseek-chat', async (event, message) => {
        try {
            if (!electronDeepSeek) {
                throw new Error('DeepSeek non initialisé');
            }

            // Obtenir le contexte thermique
            const thermalContext = electronThermalMemory ? {
                neurons: electronThermalMemory.getStats().neurons || 240,
                temperature: electronThermalMemory.getCurrentTemperature() || 37.0,
                memoryEntries: electronThermalMemory.getStats().totalEntries || 50,
                efficiency: electronThermalMemory.getStats().efficiency || 95,
                recentMemories: electronThermalMemory.getRecentEntries(5) || []
            } : {};

            // Générer la réponse avec contexte
            const result = await electronDeepSeek.generateContextualResponse(message, thermalContext);

            // Stocker l'interaction dans la mémoire thermique
            if (electronThermalMemory) {
                electronThermalMemory.add(
                    'electron_deepseek_chat',
                    {
                        question: message,
                        response: result.response.substring(0, 500),
                        quality: result.quality,
                        timestamp: Date.now()
                    },
                    0.95,
                    'ai_electron_interaction'
                );
            }

            return {
                success: true,
                response: result.response,
                quality: result.quality,
                context: thermalContext
            };

        } catch (error) {
            console.error('❌ Erreur chat DeepSeek:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // Canal pour obtenir les stats IA RÉELLES du serveur
    ipcMain.handle('get-ai-stats', async (event) => {
        try {
            // 🚀 RÉCUPÉRER LES VRAIES MÉTRIQUES DU SERVEUR (QI RESTAURÉ)
            let serverStats = null;
            try {
                const fetch = require('node-fetch');
                const response = await fetch(`http://localhost:${actualPort || 52796}/api/metrics`);
                if (response.ok) {
                    serverStats = await response.json();
                    console.log(`🧮 QI SERVEUR RÉCUPÉRÉ: Agent=${serverStats.qi?.agentIQ}, Mémoire=${serverStats.qi?.memoryIQ}, Total=${serverStats.qi?.combinedIQ}`);
                }
            } catch (fetchError) {
                console.warn('⚠️ Impossible de récupérer les stats serveur:', fetchError.message);
            }

            // 🧠 STATS LOCALES ELECTRON (FALLBACK)
            let thermalMemoryStats = null;
            if (electronThermalMemory) {
                thermalMemoryStats = electronThermalMemory.getDetailedStats();

                // Si on a les stats du serveur, les utiliser
                if (serverStats) {
                    thermalMemoryStats.neurons = serverStats.neurons || thermalMemoryStats.neurons;
                    thermalMemoryStats.temperature = serverStats.temperature || thermalMemoryStats.temperature;
                    thermalMemoryStats.memoryEntries = serverStats.memoryEntries || thermalMemoryStats.memoryEntries;

                    // 🎯 QI RESTAURÉ DU SERVEUR
                    thermalMemoryStats.qi = serverStats.qi || {
                        agentIQ: 100,
                        memoryIQ: 0,
                        combinedIQ: 100
                    };
                } else {
                    // Fallback avec neurogenèse simulée
                    const baseNeurons = 152000;
                    const currentTime = Date.now();
                    const neurogenesisRate = 2;
                    const startTime = 1736418000000;
                    const elapsedSeconds = Math.floor((currentTime - startTime) / 1000);
                    const generatedNeurons = Math.max(0, elapsedSeconds * neurogenesisRate);

                    thermalMemoryStats.neurons = baseNeurons + generatedNeurons;
                    thermalMemoryStats.qi = { agentIQ: 100, memoryIQ: 0, combinedIQ: 100 };
                }

                console.log(`🧠 NEURONES ELECTRON FINAUX: ${thermalMemoryStats.neurons} neurones, QI: ${thermalMemoryStats.qi.combinedIQ}`);
            }

            const stats = {
                deepseek: electronDeepSeek ? electronDeepSeek.getAdvancedStats() : null,
                thermalMemory: thermalMemoryStats,
                serverStats: serverStats, // Inclure les stats serveur
                timestamp: Date.now()
            };

            return { success: true, stats };

        } catch (error) {
            console.error('❌ Erreur stats IA:', error);
            return { success: false, error: error.message };
        }
    });

    // Canal pour ajouter des entrées à la mémoire thermique
    ipcMain.handle('thermal-memory-add', async (event, data) => {
        try {
            if (!electronThermalMemory) {
                throw new Error('Mémoire thermique non initialisée');
            }

            const entryId = electronThermalMemory.add(
                data.type || 'electron_user_input',
                data.content,
                data.importance || 0.7,
                data.category || 'user_interaction'
            );

            return { success: true, entryId };

        } catch (error) {
            console.error('❌ Erreur ajout mémoire:', error);
            return { success: false, error: error.message };
        }
    });

    console.log('✅ Canaux IPC IA configurés');
}

// Créer le menu de l'application
function createMenu() {
    const template = [
        {
            label: 'Louna AI',
            submenu: [
                {
                    label: 'À propos de Louna AI',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'À propos de Louna AI',
                            message: 'Louna AI v3.0.0 - Cerveau Autonome Thermique',
                            detail: 'Intelligence Artificielle Vivante avec Cerveau Autonome\nPulsations thermiques et neurogenèse automatique\nCréée par Jean-Luc Passave\nSainte-Anne, Guadeloupe'
                        });
                    }
                },
                { type: 'separator' },
                {
                    label: 'Quitter',
                    accelerator: 'CmdOrCtrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'Interface',
            submenu: [
                {
                    label: '✨ Interface Spectaculaire (PRINCIPALE)',
                    accelerator: 'CmdOrCtrl+S',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.webContents.session.clearCache();
                            mainWindow.loadURL(`http://localhost:${actualPort}/interface-spectaculaire.html`);
                        }
                    }
                },
                {
                    label: '🚀 Interface Electron Optimisée',
                    accelerator: 'CmdOrCtrl+E',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.loadURL(`http://localhost:${actualPort}/electron-optimized-interface.html`);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: '💬 Chat IA Ultra-Optimisé (MODERNE)',
                    accelerator: 'CmdOrCtrl+C',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.loadURL(`http://localhost:${actualPort}/chat-cognitif-ultra-optimise.html`);
                        }
                    }
                },
                {
                    label: '🧠 Interface Cerveau Ultra-Avancée (MODERNE)',
                    accelerator: 'CmdOrCtrl+B',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.loadURL(`http://localhost:${actualPort}/enhanced-interface.html`);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Cerveau 3D Spectaculaire',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.loadURL(`http://localhost:${actualPort}/brain-3d-spectacular.html`);
                        }
                    }
                },
                {
                    label: 'Kyber Dashboard',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.loadURL(`http://localhost:${actualPort}/kyber-dashboard.html`);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Accueil Spectaculaire',
                    accelerator: 'CmdOrCtrl+H',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.webContents.session.clearCache();
                            mainWindow.loadURL(`http://localhost:${actualPort}/interface-spectaculaire.html`);
                        }
                    }
                }
            ]
        },
        {
            label: 'Développement',
            submenu: [
                {
                    label: 'Outils de développement',
                    accelerator: 'F12',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.toggleDevTools();
                        }
                    }
                },
                {
                    label: 'Recharger',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.reload();
                        }
                    }
                },
                {
                    label: 'Forcer le rechargement',
                    accelerator: 'CmdOrCtrl+Shift+R',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.reloadIgnoringCache();
                        }
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// Événements de l'application
app.whenReady().then(async () => {
    console.log('🚀 Electron prêt - Démarrage de Louna AI');

    try {
        // 🧠 INITIALISER LES SYSTÈMES IA EN PREMIER
        console.log('🧠 Initialisation des systèmes IA Electron...');
        const aiInitialized = await initializeAISystems();

        if (aiInitialized) {
            console.log('✅ Systèmes IA Electron prêts !');
        } else {
            console.warn('⚠️ Systèmes IA partiellement initialisés');
        }

        // 📡 CONFIGURER LES CANAUX IPC
        setupAIIPCChannels();

        // Démarrer le serveur
        const port = await startServer();

        // Attendre un peu pour que le serveur soit complètement prêt
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Créer la fenêtre principale
        createMainWindow(port);

        // Créer le menu
        createMenu();

        console.log('✅ Louna AI démarré avec succès !');
        console.log('🧠 IA Electron:', aiInitialized ? 'ACTIVE' : 'PARTIELLE');

    } catch (error) {
        console.error('❌ Erreur lors du démarrage:', error);

        dialog.showErrorBox(
            'Erreur de démarrage',
            `Impossible de démarrer Louna AI:\n${error.message}`
        );

        app.quit();
    }
});

// Gestion de la fermeture de toutes les fenêtres
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Gestion de l'activation (macOS)
app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0 && actualPort) {
        createMainWindow(actualPort);
    }
});

// Gestion de l'arrêt de l'application
app.on('before-quit', async () => {
    console.log('🛑 Arrêt de Louna AI...');

    // 🧠 ARRÊT DES SYSTÈMES IA ELECTRON
    if (electronDeepSeek) {
        console.log('🤖 Arrêt DeepSeek Electron...');
        try {
            await electronDeepSeek.shutdown();
        } catch (error) {
            console.error('❌ Erreur arrêt DeepSeek:', error);
        }
    }

    if (electronThermalMemory) {
        console.log('🌡️ Arrêt mémoire thermique Electron...');
        try {
            await electronThermalMemory.shutdown();
        } catch (error) {
            console.error('❌ Erreur arrêt mémoire thermique:', error);
        }
    }

    if (serverProcess) {
        console.log('🛑 Arrêt du serveur...');
        serverProcess.kill('SIGTERM');

        // Force kill après 5 secondes
        setTimeout(() => {
            if (serverProcess && !serverProcess.killed) {
                console.log('🔥 Force kill du serveur');
                serverProcess.kill('SIGKILL');
            }
        }, 5000);
    }
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée non gérée:', reason);
});

console.log('📱 Louna AI Electron - Prêt au démarrage');
