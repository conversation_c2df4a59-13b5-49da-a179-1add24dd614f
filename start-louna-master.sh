#!/bin/bash

# 🎯 LOUNA AI - DÉMARRAGE MASTER UNIQUE
# Script de démarrage simplifié et organisé

echo "🎯 ================================"
echo "🧠 LOUNA AI - DÉMARRAGE MASTER"
echo "🎯 ================================"

# 🛑 ARRÊTER TOUS LES SERVEURS EXISTANTS
echo "🛑 Arrêt des serveurs existants..."
pkill -f "node.*server" 2>/dev/null || true
sleep 2

# 🧹 NETTOYAGE DES PROCESSUS
echo "🧹 Nettoyage des processus..."
lsof -ti:52796 | xargs kill -9 2>/dev/null || true
lsof -ti:3005 | xargs kill -9 2>/dev/null || true
sleep 1

# 📁 VÉRIFICATION DU RÉPERTOIRE
if [ ! -f "server-master.js" ]; then
    echo "❌ Erreur: server-master.js introuvable"
    echo "📁 Assurez-vous d'être dans le bon répertoire"
    exit 1
fi

# 🚀 DÉMARRAGE DU SERVEUR MASTER
echo "🚀 Démarrage du serveur master..."
echo "📱 Port: 52796"
echo "🌐 Interface: LOUNA AI Ultra-Autonome"
echo ""

# 🎯 LANCEMENT
node server-master.js &
SERVER_PID=$!

# ⏱️ ATTENDRE QUE LE SERVEUR SOIT PRÊT
echo "⏱️ Attente du démarrage du serveur..."
sleep 3

# 🔍 VÉRIFICATION
if curl -s http://localhost:52796/api/metrics > /dev/null 2>&1; then
    echo "✅ Serveur master démarré avec succès !"
    echo ""
    echo "🎯 ================================"
    echo "🎉 LOUNA AI PRÊT !"
    echo "🎯 ================================"
    echo "🌐 URL: http://localhost:52796"
    echo "📱 Interface: LOUNA AI Ultra-Autonome"
    echo "🎯 ================================"
    echo ""
    
    # 🌐 OUVRIR L'INTERFACE
    if command -v open > /dev/null 2>&1; then
        echo "🌐 Ouverture de l'interface..."
        open "http://localhost:52796"
    elif command -v xdg-open > /dev/null 2>&1; then
        echo "🌐 Ouverture de l'interface..."
        xdg-open "http://localhost:52796"
    else
        echo "🌐 Ouvrez manuellement: http://localhost:52796"
    fi
    
    echo ""
    echo "✅ LOUNA AI est maintenant organisé et fonctionnel !"
    echo "🎯 Un seul serveur, une seule interface, une seule configuration"
    echo ""
    echo "📋 Pour arrêter: Ctrl+C ou pkill -f server-master"
    
    # 💾 SAUVEGARDER LE PID
    echo $SERVER_PID > louna-master.pid
    
    # 🔄 ATTENDRE
    wait $SERVER_PID
    
else
    echo "❌ Erreur: Le serveur n'a pas pu démarrer"
    echo "🔍 Vérifiez les logs pour plus d'informations"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi
