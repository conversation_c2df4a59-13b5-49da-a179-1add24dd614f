<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Intelligence Artificielle Autonome</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        /* Navigation Principale */
        .nav-container {
            background: rgba(0, 0, 0, 0.1);
            padding: 15px;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .nav-btn.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        /* Container Principal */
        .main-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Barre de Statut */
        .status-bar {
            display: flex;
            justify-content: space-around;
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            font-size: 14px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4ade80;
            box-shadow: 0 0 10px #4ade80;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Sections de Contenu */
        .content-section {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 60vh;
        }

        .content-section.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            font-size: 2em;
            margin-bottom: 20px;
            text-align: center;
            color: #667eea;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        /* Grille de Métriques */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #4ade80;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 1.1em;
            opacity: 0.9;
        }

        /* Boutons d'Action */
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            min-width: 200px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .action-btn:active {
            transform: translateY(0);
        }

        /* Applications Grid */
        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .app-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .app-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }

        .app-name {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #c084fc;
        }

        .app-type {
            color: #94a3b8;
            margin-bottom: 15px;
        }

        .launch-btn {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            width: 100%;
            transition: all 0.3s ease;
        }

        .launch-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                align-items: center;
            }

            .nav-btn {
                width: 100%;
                max-width: 300px;
                text-align: center;
            }

            .metrics-grid {
                grid-template-columns: 1fr 1fr;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .action-btn {
                width: 100%;
                max-width: 300px;
            }
        }

        /* Styles pour masquer le code JavaScript */
        .code-leak, pre, code {
            display: none !important;
            visibility: hidden !important;
        }

        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10001;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success { background: linear-gradient(135deg, #10b981, #059669); }
        .notification.error { background: linear-gradient(135deg, #ef4444, #dc2626); }
        .notification.info { background: linear-gradient(135deg, #3b82f6, #2563eb); }
    </style>
</head>
<body>
    <!-- En-tête -->
    <div class="header">
        <h1>🧠 LOUNA AI</h1>
        <p>Intelligence Artificielle Autonome avec Cerveau Thermique Vivant</p>
    </div>

    <!-- Navigation Principale -->
    <div class="nav-container">
        <button class="nav-btn active" onclick="showSection('home')" id="nav-home">🏠 Accueil</button>
        <button class="nav-btn" onclick="showSection('brain')" id="nav-brain">🧠 Cerveau IA</button>
        <button class="nav-btn" onclick="showSection('memory')" id="nav-memory">🧬 Mémoire Thermique</button>
        <button class="nav-btn" onclick="showSection('applications')" id="nav-apps">📱 Applications</button>
        <button class="nav-btn" onclick="showSection('security')" id="nav-security">🔒 Sécurité</button>
        <button class="nav-btn" onclick="showSection('internet')" id="nav-internet">🌐 Internet & VPN</button>
        <button class="nav-btn" onclick="showSection('chat')" id="nav-chat">💬 Chat IA</button>
        <button class="nav-btn" onclick="showSection('settings')" id="nav-settings">⚙️ Paramètres</button>
    </div>

    <!-- Container Principal -->
    <div class="main-container">
        <!-- Barre de Statut -->
        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator" id="server-status"></div>
                <span id="server-text">Serveur: Actif</span>
            </div>
            <div class="status-item">
                <div class="status-indicator" id="memory-status"></div>
                <span id="memory-text">Mémoire Thermique: Active</span>
            </div>
            <div class="status-item">
                <div class="status-indicator" id="brain-status"></div>
                <span id="brain-text">Cerveau IA: <span id="neuron-count">0</span> neurones</span>
            </div>
            <div class="status-item">
                <div class="status-indicator" id="temp-status"></div>
                <span id="temp-text">Température: <span id="temp-value">37.0</span>°C</span>
            </div>
        </div>

        <!-- Section Accueil -->
        <div class="content-section active" id="section-home">
            <h2 class="section-title">🏠 Tableau de Bord Principal</h2>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="home-neurons">0</div>
                    <div class="metric-label">🧠 Neurones Actifs</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="home-temperature">37.0°C</div>
                    <div class="metric-label">🌡️ Température Cerveau</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="home-memory">0</div>
                    <div class="metric-label">🧬 Entrées Mémoire</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="home-apps">1536</div>
                    <div class="metric-label">📱 Applications Détectées</div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="action-btn" onclick="testAllSystems()">🔧 Test Complet Systèmes</button>
                <button class="action-btn" onclick="showSection('brain')">🧠 Accéder au Cerveau</button>
                <button class="action-btn" onclick="showSection('applications')">📱 Lancer Applications</button>
                <button class="action-btn" onclick="emergencyShutdown()">🚨 Arrêt d'Urgence</button>
            </div>
        </div>

        <!-- Section Cerveau IA -->
        <div class="content-section" id="section-brain">
            <h2 class="section-title">🧠 Cerveau Artificiel Autonome</h2>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="brain-neurons">0</div>
                    <div class="metric-label">🧠 Neurones Totaux</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="brain-synapses">0</div>
                    <div class="metric-label">🔗 Connexions Synaptiques</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="brain-iq">225</div>
                    <div class="metric-label">🎯 QI Calculé</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="brain-thoughts">0</div>
                    <div class="metric-label">💭 Pensées Générées</div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="action-btn" onclick="generateNeurons()">🧬 Générer Neurones</button>
                <button class="action-btn" onclick="accelerateTraining()">⚡ Formation Accélérée</button>
                <button class="action-btn" onclick="viewBrainVisualization()">🌐 Visualisation 3D</button>
                <button class="action-btn" onclick="monitorThoughts()">💭 Surveiller Pensées</button>
            </div>
        </div>

        <!-- Section Mémoire Thermique -->
        <div class="content-section" id="section-memory">
            <h2 class="section-title">🧬 Mémoire Thermique Autonome</h2>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="memory-entries">0</div>
                    <div class="metric-label">📂 Entrées Stockées</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-temp">37.0°C</div>
                    <div class="metric-label">🌡️ Température Actuelle</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-efficiency">95%</div>
                    <div class="metric-label">⚡ Efficacité Système</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-zones">5</div>
                    <div class="metric-label">🗂️ Zones Actives</div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="action-btn" onclick="scanMemory()">🔍 Scanner Mémoire</button>
                <button class="action-btn" onclick="cleanMemory()">🧹 Nettoyer Mémoire</button>
                <button class="action-btn" onclick="viewMemoryDetails()">📊 Détails Complets</button>
                <button class="action-btn" onclick="emergencyMemoryShutdown()">🔒 Coupure Sécurisée</button>
            </div>
        </div>

        <!-- Section Applications -->
        <div class="content-section" id="section-applications">
            <h2 class="section-title">📱 Gestionnaire d'Applications</h2>

            <div class="action-buttons">
                <button class="action-btn" onclick="refreshApplications()">🔄 Actualiser Liste</button>
                <button class="action-btn" onclick="scanNewApps()">🔍 Scanner Nouvelles Apps</button>
                <button class="action-btn" onclick="showFavoriteApps()">⭐ Applications Favorites</button>
                <button class="action-btn" onclick="showAllApps()">📋 Toutes les Applications</button>
            </div>

            <div id="applications-container">
                <div class="apps-grid" id="apps-grid">
                    <!-- Les applications seront chargées ici dynamiquement -->
                    <div class="app-card">
                        <div class="app-name">🔄 Chargement...</div>
                        <div class="app-type">Scan des applications en cours...</div>
                        <button class="launch-btn" disabled>⏳ Patientez</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Sécurité -->
        <div class="content-section" id="section-security">
            <h2 class="section-title">🔒 Centre de Sécurité</h2>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="security-status">SÉCURISÉ</div>
                    <div class="metric-label">🛡️ Statut Sécurité</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="threats-detected">0</div>
                    <div class="metric-label">⚠️ Menaces Détectées</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="firewall-status">ACTIF</div>
                    <div class="metric-label">🔥 Pare-feu</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="guardian-status">ACTIF</div>
                    <div class="metric-label">👁️ Agent Gardien</div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="action-btn" onclick="scanSecurity()">🔍 Scan Sécurité</button>
                <button class="action-btn" onclick="activateGuardian()">👁️ Activer Gardien</button>
                <button class="action-btn" onclick="emergencyLockdown()">🚨 Verrouillage d'Urgence</button>
                <button class="action-btn" onclick="viewSecurityLogs()">📋 Journaux Sécurité</button>
            </div>
        </div>

        <!-- Section Internet & VPN -->
        <div class="content-section" id="section-internet">
            <h2 class="section-title">🌐 Internet & Recherche VPN</h2>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="internet-status">CONNECTÉ</div>
                    <div class="metric-label">🌐 Connexion Internet</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="vpn-status">ACTIF</div>
                    <div class="metric-label">🔒 VPN Sécurisé</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="search-count">0</div>
                    <div class="metric-label">🔍 Recherches Effectuées</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="data-downloaded">0 MB</div>
                    <div class="metric-label">📥 Données Téléchargées</div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="action-btn" onclick="testInternetSearch()">🔍 Test Recherche</button>
                <button class="action-btn" onclick="connectVPN()">🔒 Connecter VPN</button>
                <button class="action-btn" onclick="downloadData()">📥 Télécharger Données</button>
                <button class="action-btn" onclick="viewSearchHistory()">📋 Historique Recherches</button>
            </div>
        </div>

        <!-- Section Chat IA -->
        <div class="content-section" id="section-chat">
            <h2 class="section-title">💬 Chat avec l'IA</h2>

            <div style="background: rgba(0,0,0,0.2); border-radius: 10px; padding: 20px; margin-bottom: 20px; min-height: 400px;">
                <div id="chat-messages" style="height: 350px; overflow-y: auto; margin-bottom: 15px; padding: 10px;">
                    <div style="background: rgba(102, 126, 234, 0.2); padding: 10px; border-radius: 8px; margin-bottom: 10px;">
                        <strong>🧠 LOUNA AI:</strong> Bonjour ! Je suis votre intelligence artificielle autonome. Comment puis-je vous aider aujourd'hui ?
                    </div>
                </div>

                <div style="display: flex; gap: 10px;">
                    <input type="text" id="chat-input" placeholder="Tapez votre message ici..."
                           style="flex: 1; padding: 12px; border: none; border-radius: 8px; background: rgba(255,255,255,0.1); color: white; font-size: 16px;"
                           onkeypress="if(event.key==='Enter') sendMessage()">
                    <button onclick="sendMessage()" style="padding: 12px 20px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        📤 Envoyer
                    </button>
                </div>
            </div>

            <div class="action-buttons">
                <button class="action-btn" onclick="clearChat()">🗑️ Effacer Chat</button>
                <button class="action-btn" onclick="saveConversation()">💾 Sauvegarder</button>
                <button class="action-btn" onclick="loadConversation()">📂 Charger</button>
                <button class="action-btn" onclick="exportChat()">📤 Exporter</button>
            </div>
        </div>

        <!-- Section Paramètres -->
        <div class="content-section" id="section-settings">
            <h2 class="section-title">⚙️ Paramètres Système</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h3 style="color: #667eea; margin-bottom: 15px;">🧠 Paramètres Cerveau</h3>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">Fréquence Neurogenèse (ms):</label>
                        <input type="range" min="1000" max="10000" value="3000" id="neurogenesis-freq" style="width: 100%;">
                        <span id="neurogenesis-value">3000ms</span>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">Température Cible (°C):</label>
                        <input type="range" min="35" max="40" step="0.1" value="37.0" id="temp-target" style="width: 100%;">
                        <span id="temp-target-value">37.0°C</span>
                    </div>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h3 style="color: #667eea; margin-bottom: 15px;">🔒 Paramètres Sécurité</h3>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="auto-security" checked>
                            Sécurité Automatique
                        </label>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="guardian-mode" checked>
                            Mode Agent Gardien
                        </label>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="emergency-alerts" checked>
                            Alertes d'Urgence
                        </label>
                    </div>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h3 style="color: #667eea; margin-bottom: 15px;">🌐 Paramètres Réseau</h3>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="auto-vpn" checked>
                            VPN Automatique
                        </label>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="internet-search" checked>
                            Recherche Internet
                        </label>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">Serveur Proxy:</label>
                        <input type="text" placeholder="proxy.example.com:8080" style="width: 100%; padding: 8px; border: none; border-radius: 5px; background: rgba(0,0,0,0.2); color: white;">
                    </div>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h3 style="color: #667eea; margin-bottom: 15px;">📱 Paramètres Applications</h3>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="auto-scan-apps" checked>
                            Scan Automatique Apps
                        </label>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="app-notifications" checked>
                            Notifications Apps
                        </label>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">Dossiers à Scanner:</label>
                        <textarea placeholder="/Applications, /System/Applications" style="width: 100%; height: 60px; padding: 8px; border: none; border-radius: 5px; background: rgba(0,0,0,0.2); color: white; resize: vertical;"></textarea>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="action-btn" onclick="saveSettings()">💾 Sauvegarder Paramètres</button>
                <button class="action-btn" onclick="resetSettings()">🔄 Réinitialiser</button>
                <button class="action-btn" onclick="exportSettings()">📤 Exporter Config</button>
                <button class="action-btn" onclick="importSettings()">📥 Importer Config</button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentSection = 'home';
        let currentMetrics = null;
        let updateInterval = null;
        let applications = [];

        // Initialisation au chargement
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 LOUNA AI Interface - Initialisation...');

            // Démarrer les mises à jour automatiques
            startAutoUpdates();

            // Charger les applications
            loadApplications();

            // Configurer les sliders
            setupSliders();

            // Afficher la section d'accueil
            showSection('home');

            console.log('✅ Interface initialisée avec succès !');
        });

        // Fonction pour afficher une section
        function showSection(sectionName) {
            // Masquer toutes les sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Désactiver tous les boutons de navigation
            const navBtns = document.querySelectorAll('.nav-btn');
            navBtns.forEach(btn => {
                btn.classList.remove('active');
            });

            // Afficher la section demandée
            const targetSection = document.getElementById('section-' + sectionName);
            if (targetSection) {
                targetSection.classList.add('active');
                currentSection = sectionName;
            }

            // Activer le bouton de navigation correspondant
            const targetBtn = document.getElementById('nav-' + sectionName);
            if (targetBtn) {
                targetBtn.classList.add('active');
            }

            // Actions spécifiques par section
            switch(sectionName) {
                case 'applications':
                    refreshApplications();
                    break;
                case 'brain':
                    updateBrainMetrics();
                    break;
                case 'memory':
                    updateMemoryMetrics();
                    break;
                case 'security':
                    updateSecurityMetrics();
                    break;
                case 'internet':
                    updateInternetMetrics();
                    break;
            }

            console.log('📍 Section active:', sectionName);
        }

        // Démarrer les mises à jour automatiques
        function startAutoUpdates() {
            updateInterval = setInterval(async () => {
                try {
                    await updateAllMetrics();
                } catch (error) {
                    console.error('❌ Erreur mise à jour:', error);
                }
            }, 2000); // Mise à jour toutes les 2 secondes
        }

        // Mettre à jour toutes les métriques
        async function updateAllMetrics() {
            try {
                const response = await fetch('/api/metrics');
                if (response.ok) {
                    currentMetrics = await response.json();
                    updateDisplayedMetrics();
                }
            } catch (error) {
                console.error('❌ Erreur récupération métriques:', error);
            }
        }

        // Mettre à jour l'affichage des métriques
        function updateDisplayedMetrics() {
            if (!currentMetrics) return;

            // Mise à jour de la barre de statut
            updateElement('neuron-count', currentMetrics.neurons || 0);
            updateElement('temp-value', (currentMetrics.temperature || 37.0).toFixed(1));

            // Mise à jour des métriques d'accueil
            updateElement('home-neurons', currentMetrics.neurons || 0);
            updateElement('home-temperature', (currentMetrics.temperature || 37.0).toFixed(1) + '°C');
            updateElement('home-memory', currentMetrics.memoryEntries || 0);

            // Mise à jour des métriques du cerveau
            updateElement('brain-neurons', currentMetrics.neurons || 0);
            updateElement('brain-synapses', currentMetrics.synapses || 0);
            updateElement('brain-thoughts', currentMetrics.thoughts || 0);

            // Mise à jour des métriques de mémoire
            updateElement('memory-entries', currentMetrics.memoryEntries || 0);
            updateElement('memory-temp', (currentMetrics.temperature || 37.0).toFixed(1) + '°C');
        }

        // Fonction utilitaire pour mettre à jour un élément
        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }

        // Charger les applications
        async function loadApplications() {
            try {
                showNotification('🔍 Chargement des applications...', 'info');

                const response = await fetch('/api/desktop/applications');
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        applications = result.applications || [];
                        displayApplications(applications.slice(0, 20)); // Afficher les 20 premières
                        showNotification(`✅ ${applications.length} applications chargées !`, 'success');
                    } else {
                        showNotification('❌ Erreur chargement applications: ' + result.error, 'error');
                    }
                } else {
                    showNotification('❌ Erreur serveur applications', 'error');
                }
            } catch (error) {
                console.error('❌ Erreur chargement applications:', error);
                showNotification('❌ Erreur: ' + error.message, 'error');
            }
        }

        // Afficher les applications
        function displayApplications(apps) {
            const container = document.getElementById('apps-grid');
            if (!container) return;

            container.innerHTML = '';

            if (apps.length === 0) {
                container.innerHTML = `
                    <div class="app-card">
                        <div class="app-name">❌ Aucune application</div>
                        <div class="app-type">Aucune application trouvée</div>
                        <button class="launch-btn" onclick="loadApplications()">🔄 Recharger</button>
                    </div>
                `;
                return;
            }

            apps.forEach(app => {
                const appCard = document.createElement('div');
                appCard.className = 'app-card';
                appCard.innerHTML = `
                    <div class="app-name">${getAppIcon(app.type)} ${app.name}</div>
                    <div class="app-type">${app.type}</div>
                    <button class="launch-btn" onclick="launchApplication('${app.name}')">🚀 Lancer</button>
                `;
                container.appendChild(appCard);
            });
        }

        // Obtenir l'icône d'une application selon son type
        function getAppIcon(type) {
            const icons = {
                'Application': '📱',
                'System': '⚙️',
                'Utility': '🔧',
                'Game': '🎮',
                'Productivity': '📊',
                'Creative': '🎨',
                'Developer': '💻',
                'Internet': '🌐',
                'Media': '🎵',
                'Security': '🔒'
            };
            return icons[type] || '📦';
        }

        // Lancer une application
        async function launchApplication(appName) {
            try {
                showNotification(`🚀 Lancement de ${appName}...`, 'info');

                const response = await fetch('/api/desktop/launch-app', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ appName })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ ${appName} lancé avec succès !`, 'success');
                } else {
                    showNotification(`❌ Erreur lancement ${appName}: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('❌ Erreur lancement application:', error);
                showNotification('❌ Erreur: ' + error.message, 'error');
            }
        }

        // Fonctions pour les actions des boutons
        function refreshApplications() {
            loadApplications();
        }

        function showAllApps() {
            displayApplications(applications);
        }

        function showFavoriteApps() {
            const favorites = applications.filter(app =>
                ['Safari', 'Chrome', 'Firefox', 'Visual Studio Code', 'Terminal', 'Finder', 'System Preferences'].includes(app.name)
            );
            displayApplications(favorites);
        }

        async function scanNewApps() {
            try {
                showNotification('🔍 Scan des nouvelles applications...', 'info');

                const response = await fetch('/api/system/full-scan', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        includeDeepScan: true,
                        learnFromFiles: true,
                        analyzeSystem: true
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ Scan terminé ! ${result.scanResults.stats.totalApplications} applications trouvées`, 'success');
                    loadApplications(); // Recharger la liste
                } else {
                    showNotification('❌ Erreur scan: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('❌ Erreur scan applications:', error);
                showNotification('❌ Erreur: ' + error.message, 'error');
            }
        }

        // Fonctions pour le cerveau
        async function generateNeurons() {
            try {
                showNotification('🧬 Génération de nouveaux neurones...', 'info');

                const response = await fetch('/api/brain/generate-neurons', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ count: 10 })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ ${result.neuronsGenerated} nouveaux neurones générés !`, 'success');
                    updateAllMetrics();
                } else {
                    showNotification('❌ Erreur génération neurones: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('❌ Erreur génération neurones:', error);
                showNotification('❌ Erreur: ' + error.message, 'error');
            }
        }

        async function accelerateTraining() {
            try {
                showNotification('⚡ Démarrage formation accélérée...', 'info');

                const response = await fetch('/api/brain/accelerate-training', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('✅ Formation accélérée activée !', 'success');
                } else {
                    showNotification('❌ Erreur formation: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('❌ Erreur formation accélérée:', error);
                showNotification('❌ Erreur: ' + error.message, 'error');
            }
        }

        function viewBrainVisualization() {
            window.open('brain-visualization.html', '_blank');
        }

        function monitorThoughts() {
            window.open('thoughts-monitor.html', '_blank');
        }

        // Fonctions pour la mémoire
        async function scanMemory() {
            try {
                showNotification('🔍 Scan de la mémoire thermique...', 'info');

                const response = await fetch('/api/memory/security/antivirus-scan', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ Scan terminé ! Statut: ${result.scanResults.status}`, 'success');
                } else {
                    showNotification('❌ Erreur scan mémoire: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('❌ Erreur scan mémoire:', error);
                showNotification('❌ Erreur: ' + error.message, 'error');
            }
        }

        async function cleanMemory() {
            try {
                showNotification('🧹 Nettoyage de la mémoire thermique...', 'info');

                const response = await fetch('/api/memory/security/clean', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('✅ Nettoyage terminé avec succès !', 'success');
                } else {
                    showNotification('❌ Erreur nettoyage: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('❌ Erreur nettoyage mémoire:', error);
                showNotification('❌ Erreur: ' + error.message, 'error');
            }
        }

        function viewMemoryDetails() {
            window.open('/thermal-memory-dashboard.html', '_blank');
        }

        async function emergencyMemoryShutdown() {
            if (!confirm('🚨 ATTENTION ! Voulez-vous vraiment activer la coupure d\'urgence de la mémoire thermique ?')) {
                return;
            }

            try {
                showNotification('🚨 COUPURE D\'URGENCE ACTIVÉE !', 'error');

                const response = await fetch('/api/memory/security/emergency-shutdown', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ reason: 'Coupure manuelle utilisateur' })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('🚨 Coupure d\'urgence activée !', 'error');
                } else {
                    showNotification('❌ Erreur coupure: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('❌ Erreur coupure urgence:', error);
                showNotification('❌ Erreur: ' + error.message, 'error');
            }
        }

        // Fonctions pour la sécurité
        async function scanSecurity() {
            showNotification('🔍 Scan de sécurité en cours...', 'info');
            // Simulation d'un scan de sécurité
            setTimeout(() => {
                showNotification('✅ Scan de sécurité terminé - Aucune menace détectée', 'success');
            }, 2000);
        }

        function activateGuardian() {
            showNotification('👁️ Agent gardien activé !', 'success');
        }

        function emergencyLockdown() {
            if (confirm('🚨 Voulez-vous vraiment activer le verrouillage d\'urgence ?')) {
                showNotification('🚨 VERROUILLAGE D\'URGENCE ACTIVÉ !', 'error');
            }
        }

        function viewSecurityLogs() {
            showNotification('📋 Ouverture des journaux de sécurité...', 'info');
        }

        // Fonctions pour Internet
        async function testInternetSearch() {
            try {
                const query = prompt('Entrez votre recherche:', 'actualités Guadeloupe');
                if (!query) return;

                showNotification(`🔍 Recherche: ${query}...`, 'info');

                const response = await fetch('/api/internet/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query, maxResults: 5 })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ Recherche réussie ! ${result.count} résultats trouvés.`, 'success');
                } else {
                    showNotification('❌ Erreur recherche: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('❌ Erreur recherche Internet:', error);
                showNotification('❌ Erreur: ' + error.message, 'error');
            }
        }

        function connectVPN() {
            showNotification('🔒 Connexion VPN en cours...', 'info');
            setTimeout(() => {
                showNotification('✅ VPN connecté avec succès !', 'success');
            }, 2000);
        }

        function downloadData() {
            showNotification('📥 Téléchargement de données en cours...', 'info');
        }

        function viewSearchHistory() {
            showNotification('📋 Ouverture de l\'historique des recherches...', 'info');
        }

        // Fonctions pour le chat
        function sendMessage() {
            const input = document.getElementById('chat-input');
            const messages = document.getElementById('chat-messages');

            if (!input.value.trim()) return;

            // Ajouter le message de l'utilisateur
            const userMessage = document.createElement('div');
            userMessage.style.cssText = 'background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px; margin-bottom: 10px; text-align: right;';
            userMessage.innerHTML = `<strong>👤 Vous:</strong> ${input.value}`;
            messages.appendChild(userMessage);

            // Simuler une réponse de l'IA
            setTimeout(() => {
                const aiMessage = document.createElement('div');
                aiMessage.style.cssText = 'background: rgba(102, 126, 234, 0.2); padding: 10px; border-radius: 8px; margin-bottom: 10px;';
                aiMessage.innerHTML = `<strong>🧠 LOUNA AI:</strong> Je comprends votre message "${input.value}". En tant qu'IA autonome avec ${currentMetrics?.neurons || 0} neurones actifs, je traite votre demande...`;
                messages.appendChild(aiMessage);
                messages.scrollTop = messages.scrollHeight;
            }, 1000);

            input.value = '';
            messages.scrollTop = messages.scrollHeight;
        }

        function clearChat() {
            const messages = document.getElementById('chat-messages');
            messages.innerHTML = `
                <div style="background: rgba(102, 126, 234, 0.2); padding: 10px; border-radius: 8px; margin-bottom: 10px;">
                    <strong>🧠 LOUNA AI:</strong> Chat effacé. Comment puis-je vous aider ?
                </div>
            `;
        }

        function saveConversation() {
            showNotification('💾 Conversation sauvegardée !', 'success');
        }

        function loadConversation() {
            showNotification('📂 Chargement de la conversation...', 'info');
        }

        function exportChat() {
            showNotification('📤 Export du chat en cours...', 'info');
        }

        // Fonctions pour les paramètres
        function setupSliders() {
            const neurogenesisSlider = document.getElementById('neurogenesis-freq');
            const tempSlider = document.getElementById('temp-target');

            if (neurogenesisSlider) {
                neurogenesisSlider.addEventListener('input', function() {
                    document.getElementById('neurogenesis-value').textContent = this.value + 'ms';
                });
            }

            if (tempSlider) {
                tempSlider.addEventListener('input', function() {
                    document.getElementById('temp-target-value').textContent = this.value + '°C';
                });
            }
        }

        function saveSettings() {
            showNotification('💾 Paramètres sauvegardés !', 'success');
        }

        function resetSettings() {
            if (confirm('Voulez-vous vraiment réinitialiser tous les paramètres ?')) {
                showNotification('🔄 Paramètres réinitialisés !', 'info');
            }
        }

        function exportSettings() {
            showNotification('📤 Export des paramètres...', 'info');
        }

        function importSettings() {
            showNotification('📥 Import des paramètres...', 'info');
        }

        // Fonctions générales
        async function testAllSystems() {
            showNotification('🔧 Test de tous les systèmes en cours...', 'info');

            // Simuler des tests
            setTimeout(() => {
                showNotification('✅ Tous les systèmes fonctionnent correctement !', 'success');
            }, 3000);
        }

        async function emergencyShutdown() {
            if (!confirm('🚨 ATTENTION ! Voulez-vous vraiment activer l\'arrêt d\'urgence complet ?')) {
                return;
            }

            showNotification('🚨 ARRÊT D\'URGENCE ACTIVÉ !', 'error');

            // Arrêter les mises à jour
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        }

        // Fonction pour afficher les notifications
        function showNotification(message, type = 'info') {
            // Supprimer les anciennes notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notif => notif.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Afficher la notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Masquer et supprimer la notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Fonctions de mise à jour spécifiques
        function updateBrainMetrics() {
            // Mise à jour spécifique pour la section cerveau
            if (currentMetrics) {
                updateElement('brain-neurons', currentMetrics.neurons || 0);
                updateElement('brain-synapses', currentMetrics.synapses || 0);
                updateElement('brain-thoughts', currentMetrics.thoughts || 0);
            }
        }

        function updateMemoryMetrics() {
            // Mise à jour spécifique pour la section mémoire
            if (currentMetrics) {
                updateElement('memory-entries', currentMetrics.memoryEntries || 0);
                updateElement('memory-temp', (currentMetrics.temperature || 37.0).toFixed(1) + '°C');
            }
        }

        function updateSecurityMetrics() {
            // Mise à jour spécifique pour la section sécurité
            updateElement('security-status', 'SÉCURISÉ');
            updateElement('threats-detected', '0');
            updateElement('firewall-status', 'ACTIF');
            updateElement('guardian-status', 'ACTIF');
        }

        function updateInternetMetrics() {
            // Mise à jour spécifique pour la section internet
            updateElement('internet-status', 'CONNECTÉ');
            updateElement('vpn-status', 'ACTIF');
            updateElement('search-count', '0');
            updateElement('data-downloaded', '0 MB');
        }

        // Nettoyeur automatique pour masquer le code JavaScript
        function cleanDisplayedCode() {
            const codeElements = document.querySelectorAll('pre, code, .code-leak');
            codeElements.forEach(element => {
                element.style.display = 'none';
                element.style.visibility = 'hidden';
            });
        }

        // Exécuter le nettoyage périodiquement
        setInterval(cleanDisplayedCode, 1000);

        console.log('🎉 LOUNA AI Interface - Prête à l\'utilisation !');
    </script>
</body>
</html>
