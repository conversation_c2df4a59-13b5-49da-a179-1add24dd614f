<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎓 RÉCUPÉRATION FORMATIONS - LOUNA AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .recovery-container {
            width: 100%;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .status-panel.critical {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.2);
        }

        .status-panel.warning {
            border-color: #ffa500;
            background: rgba(255, 165, 0, 0.2);
        }

        .status-panel.success {
            border-color: #4ecdc4;
            background: rgba(78, 205, 196, 0.2);
        }

        .panel-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
        }

        .metric-value {
            font-weight: bold;
            color: #4ecdc4;
        }

        .metric-value.critical {
            color: #ff6b6b;
        }

        .metric-value.warning {
            color: #ffa500;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .action-btn.critical {
            background: linear-gradient(135deg, #ff6b6b, #ff4757);
        }

        .action-btn.success {
            background: linear-gradient(135deg, #4ecdc4, #44bd87);
        }

        .action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .recommendations {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        .recommendation-item {
            margin: 10px 0;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid;
        }

        .recommendation-item.critical {
            background: rgba(255, 107, 107, 0.2);
            border-color: #ff6b6b;
        }

        .recommendation-item.warning {
            background: rgba(255, 165, 0, 0.2);
            border-color: #ffa500;
        }

        .recommendation-item.success {
            background: rgba(78, 205, 196, 0.2);
            border-color: #4ecdc4;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .log-panel {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            margin: 5px 0;
            padding: 8px;
            border-left: 3px solid #4ecdc4;
            background: rgba(255, 255, 255, 0.05);
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4ecdc4, #44bd87);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="recovery-container">
        <div class="header">
            <h1><i class="fas fa-graduation-cap"></i> Récupération des Formations</h1>
            <p>Vérification et restauration de l'intelligence de LOUNA AI</p>
            <div style="margin-top: 15px;">
                <a href="/interface-spectaculaire.html" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
                <a href="/neuron-recovery-emergency.html" class="nav-btn"><i class="fas fa-brain"></i> Neurones</a>
                <a href="/qi-evolution-test.html" class="nav-btn"><i class="fas fa-chart-line"></i> Tests QI</a>
            </div>
        </div>

        <div class="status-grid">
            <!-- Intelligence Panel -->
            <div class="status-panel" id="intelligence-panel">
                <div class="panel-title">
                    <i class="fas fa-brain"></i> État de l'Intelligence
                </div>
                <div class="metric-item">
                    <span>QI Actuel:</span>
                    <span class="metric-value" id="current-qi">Vérification...</span>
                </div>
                <div class="metric-item">
                    <span>QI Attendu:</span>
                    <span class="metric-value">225</span>
                </div>
                <div class="metric-item">
                    <span>Évolution:</span>
                    <span class="metric-value" id="qi-evolution">Calcul...</span>
                </div>
                <div class="metric-item">
                    <span>Statut:</span>
                    <span class="metric-value" id="intelligence-status">Inconnu</span>
                </div>
            </div>

            <!-- Formations Panel -->
            <div class="status-panel" id="formations-panel">
                <div class="panel-title">
                    <i class="fas fa-book"></i> Formations Sauvegardées
                </div>
                <div class="metric-item">
                    <span>Total Formations:</span>
                    <span class="metric-value" id="total-formations">0</span>
                </div>
                <div class="metric-item">
                    <span>Mathématiques:</span>
                    <span class="metric-value" id="math-formations">0</span>
                </div>
                <div class="metric-item">
                    <span>Logique:</span>
                    <span class="metric-value" id="logic-formations">0</span>
                </div>
                <div class="metric-item">
                    <span>Calcul:</span>
                    <span class="metric-value" id="calcul-formations">0</span>
                </div>
            </div>

            <!-- Neurones Panel -->
            <div class="status-panel" id="neurones-panel">
                <div class="panel-title">
                    <i class="fas fa-network-wired"></i> État des Neurones
                </div>
                <div class="metric-item">
                    <span>Neurones Actifs:</span>
                    <span class="metric-value" id="active-neurons">0</span>
                </div>
                <div class="metric-item">
                    <span>Neurones Sauvegardés:</span>
                    <span class="metric-value" id="saved-neurons">0</span>
                </div>
                <div class="metric-item">
                    <span>Neurones Générés:</span>
                    <span class="metric-value" id="generated-neurons">0</span>
                </div>
                <div class="metric-item">
                    <span>Statut:</span>
                    <span class="metric-value" id="neurons-status">Inconnu</span>
                </div>
            </div>

            <!-- Sauvegardes Panel -->
            <div class="status-panel" id="backups-panel">
                <div class="panel-title">
                    <i class="fas fa-save"></i> Sauvegardes Disponibles
                </div>
                <div class="metric-item">
                    <span>Formations Sauvegardées:</span>
                    <span class="metric-value" id="formations-exist">Non</span>
                </div>
                <div class="metric-item">
                    <span>Sauvegarde d'Urgence:</span>
                    <span class="metric-value" id="emergency-backup">Non</span>
                </div>
                <div class="metric-item">
                    <span>Dernière Sauvegarde:</span>
                    <span class="metric-value" id="last-backup">Jamais</span>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="action-btn" onclick="checkTrainingStatus()">
                <i class="fas fa-search"></i> Vérifier État Complet
            </button>
            
            <button class="action-btn critical" onclick="recoverTraining()">
                <i class="fas fa-medkit"></i> Récupérer Formations
            </button>
            
            <button class="action-btn" onclick="createBackup()">
                <i class="fas fa-save"></i> Créer Sauvegarde
            </button>
            
            <button class="action-btn success" onclick="restoreFromBackup()">
                <i class="fas fa-upload"></i> Restaurer Sauvegarde
            </button>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress-bar"></div>
        </div>
        <div style="text-align: center; margin-top: 10px;">
            <span id="progress-text">Prêt pour la vérification</span>
        </div>

        <div class="recommendations" id="recommendations">
            <h3><i class="fas fa-lightbulb"></i> Recommandations</h3>
            <div id="recommendations-list">
                <p>Cliquez sur "Vérifier État Complet" pour obtenir des recommandations personnalisées.</p>
            </div>
        </div>

        <div class="log-panel">
            <h4><i class="fas fa-list"></i> Journal des Opérations</h4>
            <div id="operation-log">
                <div class="log-entry">
                    [INIT] Interface de récupération des formations prête
                </div>
            </div>
        </div>
    </div>

    <script>
        let isProcessing = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎓 Interface de récupération des formations initialisée');
            checkTrainingStatus();
        });

        // Vérifier l'état complet des formations
        async function checkTrainingStatus() {
            if (isProcessing) return;
            
            const btn = event?.target;
            if (btn) {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Vérification...';
            }
            
            updateProgress(25, 'Vérification de l\'état des formations...');
            addLogEntry('Démarrage de la vérification complète');
            
            try {
                const response = await fetch('/api/check-training-status');
                const data = await response.json();
                
                updateProgress(75, 'Analyse des résultats...');
                
                if (data.success) {
                    updateStatusDisplay(data.status);
                    updateRecommendations(data.recommendations);
                    updateProgress(100, 'Vérification terminée');
                    addLogEntry('Vérification terminée avec succès');
                } else {
                    updateProgress(0, 'Erreur lors de la vérification');
                    addLogEntry('ERREUR: ' + data.error);
                }
                
            } catch (error) {
                updateProgress(0, 'Erreur de connexion');
                addLogEntry('ERREUR: ' + error.message);
            }
            
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-search"></i> Vérifier État Complet';
            }
        }

        // Mettre à jour l'affichage du statut
        function updateStatusDisplay(status) {
            // Intelligence
            document.getElementById('current-qi').textContent = status.intelligence.current || 'Inconnu';
            document.getElementById('qi-evolution').textContent = status.intelligence.evolution ? 
                (status.intelligence.evolution > 0 ? '+' : '') + status.intelligence.evolution : 'N/A';
            document.getElementById('intelligence-status').textContent = status.intelligence.status;
            
            const intelligencePanel = document.getElementById('intelligence-panel');
            intelligencePanel.className = 'status-panel ' + 
                (status.intelligence.status === 'degraded' ? 'critical' : 
                 status.intelligence.status === 'stable' ? 'success' : 'warning');
            
            // Formations
            document.getElementById('total-formations').textContent = status.formations.totalFormations;
            document.getElementById('math-formations').textContent = status.formations.mathFormations;
            document.getElementById('logic-formations').textContent = status.formations.logicFormations;
            document.getElementById('calcul-formations').textContent = status.formations.calculFormations;
            
            const formationsPanel = document.getElementById('formations-panel');
            formationsPanel.className = 'status-panel ' + 
                (status.formations.saved ? 'success' : 'critical');
            
            // Neurones
            document.getElementById('active-neurons').textContent = status.neurones.total;
            document.getElementById('saved-neurons').textContent = status.neurones.saved;
            document.getElementById('generated-neurons').textContent = status.neurones.generated;
            document.getElementById('neurons-status').textContent = status.neurones.status;
            
            const neuronsPanel = document.getElementById('neurones-panel');
            neuronsPanel.className = 'status-panel ' + 
                (status.neurones.status === 'saved' ? 'success' : 'critical');
            
            // Sauvegardes
            document.getElementById('formations-exist').textContent = status.sauvegardes.formationsExist ? 'Oui' : 'Non';
            document.getElementById('emergency-backup').textContent = status.sauvegardes.emergencyBackupExists ? 'Oui' : 'Non';
            document.getElementById('last-backup').textContent = status.sauvegardes.lastBackup ? 
                new Date(status.sauvegardes.lastBackup).toLocaleString() : 'Jamais';
            
            const backupsPanel = document.getElementById('backups-panel');
            backupsPanel.className = 'status-panel ' + 
                (status.sauvegardes.formationsExist ? 'success' : 'warning');
        }

        // Mettre à jour les recommandations
        function updateRecommendations(recommendations) {
            const container = document.getElementById('recommendations-list');
            container.innerHTML = '';
            
            recommendations.forEach(rec => {
                const item = document.createElement('div');
                item.className = `recommendation-item ${rec.type}`;
                item.innerHTML = `
                    <strong>${rec.message}</strong><br>
                    <small>Action recommandée: ${rec.action}</small>
                `;
                container.appendChild(item);
            });
        }

        // Fonctions d'action (à implémenter)
        async function recoverTraining() {
            addLogEntry('RÉCUPÉRATION: Fonction en cours de développement');
            alert('Fonction de récupération en cours de développement');
        }

        async function createBackup() {
            addLogEntry('SAUVEGARDE: Fonction en cours de développement');
            alert('Fonction de sauvegarde en cours de développement');
        }

        async function restoreFromBackup() {
            addLogEntry('RESTAURATION: Fonction en cours de développement');
            alert('Fonction de restauration en cours de développement');
        }

        // Utilitaires
        function updateProgress(percentage, text) {
            document.getElementById('progress-bar').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = text;
        }

        function addLogEntry(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('operation-log');
            
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    </script>
</body>
</html>
