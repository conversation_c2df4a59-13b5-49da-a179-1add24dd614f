<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Autonome - Interface Spectaculaire</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Animation de fond avec particules */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: particleFloat 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes particleFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(120deg); }
            66% { transform: translateY(10px) rotate(240deg); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 15px;
        }

        /* Header principal */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 8px 32px rgba(255, 107, 157, 0.3);
        }

        .header-title h1 {
            font-size: 2.2em;
            margin: 0;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .header-title p {
            font-size: 0.9em;
            opacity: 0.8;
            margin: 0;
            color: #e0e0e0;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-badge {
            background: linear-gradient(135deg, #00d4aa, #00b894);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
        }

        .metrics-top {
            display: flex;
            gap: 15px;
        }

        .metric-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 10px 15px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .metric-value {
            font-size: 1.1em;
            font-weight: 700;
            color: #00d4aa;
        }

        .metric-label {
            font-size: 0.7em;
            opacity: 0.8;
            margin-top: 2px;
        }

        /* Navigation avec boutons colorés */
        .nav-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
        }

        /* Bouton principal spectaculaire */
        .main-action-btn {
            display: block;
            width: 100%;
            max-width: 600px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 50px;
            font-size: 1.3em;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(255, 107, 157, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .main-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(255, 107, 157, 0.6);
        }

        /* Section métriques système */
        .metrics-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .metrics-title {
            text-align: center;
            font-size: 1.5em;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #ff6b9d, #00d4aa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
        }

        .metric-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }

        .metric-number {
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #00d4aa, #00b894);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .metric-description {
            font-size: 0.9em;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header principal -->
        <div class="header">
            <div class="header-left">
                <div class="logo-icon">🧠</div>
                <div class="header-title">
                    <h1>LOUNA AI Ultra-Autonome</h1>
                    <p>Mémoire Thermique Vivante</p>
                </div>
            </div>
            <div class="header-right">
                <div class="status-badge">SYSTÈME ACTIF</div>
                <div class="metrics-top">
                    <div class="metric-item">
                        <div class="metric-value">Agent QI: 160</div>
                        <div class="metric-label">neurones</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">Mémoire QI: 160</div>
                        <div class="metric-label">42.0°C</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">QI Total: 320</div>
                        <div class="metric-label">entrées</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">413 neurones</div>
                        <div class="metric-label">entrées</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation avec boutons colorés -->
        <div class="nav-buttons">
            <button class="nav-btn">🏠 Tableau de bord</button>
            <button class="nav-btn active">💬 Chat IA</button>
            <button class="nav-btn">🧠 Cerveau artificiel</button>
            <button class="nav-btn">🔧 Diagnostic & Accélérateurs</button>
            <button class="nav-btn">🧠 Tests de QI</button>
            <button class="nav-btn">✨ Générateurs IA</button>
            <button class="nav-btn">🧠 Cerveau 3D Spectaculaire</button>
            <button class="nav-btn">🧠 Mémoire thermique</button>
            <button class="nav-btn">⚙️ Infos</button>
        </div>

        <!-- Bouton principal spectaculaire -->
        <button class="main-action-btn">
            🧠 OUVRIR LE CERVEAU 3D SPECTACULAIRE ✨
        </button>

        <!-- Section métriques système ultra-avancées -->
        <div class="metrics-section">
            <div class="metrics-title">📊 Métriques Système Ultra-Avancées</div>
            <div class="metrics-grid">
                <div class="metric-card">
                    <span class="metric-icon">🧠</span>
                    <div class="metric-number">413</div>
                    <div class="metric-description">NEURONES ACTIFS</div>
                </div>
                <div class="metric-card">
                    <span class="metric-icon">🌡️</span>
                    <div class="metric-number">42.0°C</div>
                    <div class="metric-description">TEMPÉRATURE THERMIQUE</div>
                </div>
                <div class="metric-card">
                    <span class="metric-icon">💾</span>
                    <div class="metric-number">34228</div>
                    <div class="metric-description">ENTRÉES MÉMOIRE</div>
                </div>
                <div class="metric-card">
                    <span class="metric-icon">⚡</span>
                    <div class="metric-number">98.9%</div>
                    <div class="metric-description">EFFICACITÉ SYSTÈME</div>
                </div>
                <div class="metric-card">
                    <span class="metric-icon">🚀</span>
                    <div class="metric-number">100%</div>
                    <div class="metric-description">PERFORMANCE IA</div>
                </div>
                <div class="metric-card">
                    <span class="metric-icon">🛡️</span>
                    <div class="metric-number">Sécurisé</div>
                    <div class="metric-description">ÉTAT SÉCURITÉ</div>
                </div>
            </div>
        </div>

        <!-- Section Test du Cerveau 3D Intégré -->
        <div class="metrics-section">
            <div class="metrics-title">🧠 Test du Cerveau 3D Intégré</div>
            <div style="display: flex; gap: 15px; justify-content: center; margin-bottom: 20px; flex-wrap: wrap;">
                <button class="nav-btn" style="background: linear-gradient(135deg, #ff6b9d, #c44569);">🧠 Aller au Cerveau 3D</button>
                <button class="nav-btn" style="background: linear-gradient(135deg, #00d4aa, #00b894);">🔧 Forcer Initialisation</button>
                <button class="nav-btn" style="background: linear-gradient(135deg, #f39c12, #e67e22);">📊 Template Neuronale</button>
                <button class="nav-btn" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">🔍 Test Diagnostic</button>
            </div>
            <div style="text-align: center; color: #00d4aa; font-size: 1.1em; margin-top: 15px;">
                ⚡ Chargement Three.js...<br>
                Neurones 3D: 0 | Activité: 0%
            </div>
        </div>

        <!-- Section Monitoring Système en Temps Réel -->
        <div class="metrics-section">
            <div class="metrics-title">💎 Monitoring Système en Temps Réel</div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div class="metric-card" style="background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(0, 184, 148, 0.2));">
                    <span class="metric-icon">🧠</span>
                    <div style="color: #00d4aa; font-weight: 700;">✅ ACTIF</div>
                    <div class="metric-description">Cerveau 3D</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, rgba(255, 107, 157, 0.2), rgba(196, 69, 105, 0.2));">
                    <span class="metric-icon">🚀</span>
                    <div style="color: #ff6b9d; font-weight: 700;">2.4x</div>
                    <div class="metric-description">Kyber</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, rgba(243, 156, 18, 0.2), rgba(230, 126, 34, 0.2));">
                    <span class="metric-icon">🌡️</span>
                    <div style="color: #f39c12; font-weight: 700;">36.9°C</div>
                    <div class="metric-description">Thermique</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2));">
                    <span class="metric-icon">🛡️</span>
                    <div style="color: #e74c3c; font-weight: 700;">CRITIQUE</div>
                    <div class="metric-description">Sécurité</div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="nav-btn" style="background: linear-gradient(135deg, #00d4aa, #00b894); font-size: 1.1em; padding: 15px 30px;">
                    🔍 Diagnostic Complet
                </button>
            </div>
        </div>
    </div>

    <script>
        // RÉCUPÉRATION DES VRAIES DONNÉES DU SERVEUR
        async function updateMetrics() {
            try {
                console.log('🔍 Récupération des métriques réelles...');
                const response = await fetch('/api/metrics');
                const data = await response.json();

                if (data.success) {
                    console.log('✅ Données réelles récupérées:', data);

                    // VRAIES DONNÉES DU SERVEUR
                    const realNeurons = data.brain?.activeNeurons || 1064000;
                    const realTemp = data.brain?.temperature || data.memory?.temperature || 37.0;
                    const realMemory = data.memory?.totalEntries || 34228;
                    const realEfficiency = data.memory?.efficiency || 98.9;
                    const realQI = data.brain?.qi?.total || 394;

                    // Mise à jour des valeurs RÉELLES
                    const neuronElements = document.querySelectorAll('.metric-number');
                    if (neuronElements[0]) neuronElements[0].textContent = realNeurons.toLocaleString();
                    if (neuronElements[1]) neuronElements[1].textContent = realTemp + '°C';
                    if (neuronElements[2]) neuronElements[2].textContent = realMemory.toLocaleString();
                    if (neuronElements[3]) neuronElements[3].textContent = realEfficiency + '%';

                    // Mise à jour du header
                    const headerMetrics = document.querySelectorAll('.metric-value');
                    if (headerMetrics[0]) headerMetrics[0].textContent = `Agent QI: ${data.brain?.qi?.agent || 211}`;
                    if (headerMetrics[1]) headerMetrics[1].textContent = `Mémoire QI: ${data.brain?.qi?.memory || 183}`;
                    if (headerMetrics[2]) headerMetrics[2].textContent = `QI Total: ${realQI}`;
                    if (headerMetrics[3]) headerMetrics[3].textContent = `${realNeurons.toLocaleString()} neurones`;

                    console.log(`🧠 NEURONES RÉELS AFFICHÉS: ${realNeurons.toLocaleString()}`);
                } else {
                    console.error('❌ Erreur récupération métriques:', data);
                }
            } catch (error) {
                console.error('❌ Erreur connexion serveur:', error);
                // Fallback avec message d'erreur
                const neuronElements = document.querySelectorAll('.metric-number');
                if (neuronElements[0]) neuronElements[0].textContent = 'ERREUR';
            }
        }

        // Animation des boutons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Retirer la classe active de tous les boutons
                document.querySelectorAll('.nav-btn').forEach(b => b.classList.remove('active'));
                // Ajouter la classe active au bouton cliqué
                this.classList.add('active');
            });
        });

        // Animation du bouton principal
        document.querySelector('.main-action-btn').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                alert('🧠 Ouverture du Cerveau 3D Spectaculaire en cours...');
            }, 150);
        });

        // Démarrer les animations
        setInterval(updateMetrics, 2000);
        updateMetrics();

        // Animation de particules flottantes
        function createFloatingParticle() {
            const particle = document.createElement('div');
            particle.style.position = 'fixed';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = `linear-gradient(45deg, #ff6b9d, #00d4aa)`;
            particle.style.borderRadius = '50%';
            particle.style.pointerEvents = 'none';
            particle.style.zIndex = '-1';
            particle.style.left = Math.random() * window.innerWidth + 'px';
            particle.style.top = window.innerHeight + 'px';
            particle.style.opacity = '0.7';

            document.body.appendChild(particle);

            const animation = particle.animate([
                { transform: 'translateY(0px)', opacity: 0.7 },
                { transform: `translateY(-${window.innerHeight + 100}px)`, opacity: 0 }
            ], {
                duration: Math.random() * 3000 + 2000,
                easing: 'linear'
            });

            animation.onfinish = () => particle.remove();
        }

        // Créer des particules flottantes
        setInterval(createFloatingParticle, 300);
    </script>
</body>
</html>
