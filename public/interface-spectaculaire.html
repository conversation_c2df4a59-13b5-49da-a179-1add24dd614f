<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Autonome - Accueil</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 15px;
        }

        /* Header principal */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 8px 32px rgba(255, 107, 157, 0.3);
        }

        .header-title h1 {
            font-size: 2.2em;
            margin: 0;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .header-title p {
            font-size: 0.9em;
            opacity: 0.8;
            margin: 0;
            color: #00ff00;
            font-weight: bold;
        }

        .status-badge {
            background: linear-gradient(135deg, #00d4aa, #00b894);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
        }

        /* Navigation principale */
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .nav-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
        }

        .nav-card-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }

        .nav-card-title {
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #ff6b9d, #00d4aa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-card-desc {
            font-size: 0.9em;
            opacity: 0.8;
            line-height: 1.4;
        }

        /* Métriques système */
        .metrics-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .metrics-title {
            text-align: center;
            font-size: 1.5em;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #ff6b9d, #00d4aa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .metric-number {
            font-size: 2em;
            font-weight: 700;
            color: #00d4aa;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header principal -->
        <div class="header">
            <div class="header-left">
                <div class="logo-icon">🧠</div>
                <div class="header-title">
                    <h1>LOUNA AI Ultra-Autonome</h1>
                    <p>🏠 ACCUEIL PRINCIPAL - TOUTES FONCTIONNALITÉS</p>
                </div>
            </div>
            <div class="status-badge">SYSTÈME ACTIF</div>
        </div>

        <!-- Navigation principale -->
        <div class="nav-grid">
            <div class="nav-card" onclick="goToChat()">
                <span class="nav-card-icon">💬</span>
                <div class="nav-card-title">Chat IA Cognitif</div>
                <div class="nav-card-desc">Discutez avec LOUNA AI et voyez ses vraies pensées basées sur vos interactions</div>
            </div>

            <div class="nav-card" onclick="goToBrain3D()">
                <span class="nav-card-icon">🧠</span>
                <div class="nav-card-title">Cerveau 3D Spectaculaire</div>
                <div class="nav-card-desc">Visualisation 3D du cerveau artificiel avec neurones en temps réel</div>
            </div>

            <div class="nav-card" onclick="goToDiagnostic()">
                <span class="nav-card-icon">🔧</span>
                <div class="nav-card-title">Diagnostic & Kyber</div>
                <div class="nav-card-desc">Accélérateurs Kyber, diagnostic système et monitoring avancé</div>
            </div>

            <div class="nav-card" onclick="goToQITests()">
                <span class="nav-card-icon">🧠</span>
                <div class="nav-card-title">Tests de QI</div>
                <div class="nav-card-desc">Tests d'intelligence et évaluation des capacités cognitives</div>
            </div>

            <div class="nav-card" onclick="goToGenerators()">
                <span class="nav-card-icon">✨</span>
                <div class="nav-card-title">Générateurs IA</div>
                <div class="nav-card-desc">Génération d'images, vidéos, musique et contenu créatif</div>
            </div>

            <div class="nav-card" onclick="goToThermalMemory()">
                <span class="nav-card-icon">🌡️</span>
                <div class="nav-card-title">Mémoire Thermique</div>
                <div class="nav-card-desc">Dashboard de la mémoire thermique et gestion des souvenirs</div>
            </div>
        </div>

        <!-- Métriques système en temps réel -->
        <div class="metrics-section">
            <div class="metrics-title">📊 État Système en Temps Réel</div>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-number" id="neurons">Chargement...</div>
                    <div class="metric-label">Neurones Actifs</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number" id="synapses">Chargement...</div>
                    <div class="metric-label">Connexions Synaptiques</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number" id="temperature">Chargement...</div>
                    <div class="metric-label">Température Thermique</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number" id="qi">Chargement...</div>
                    <div class="metric-label">QI Total</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // NAVIGATION VERS LES DIFFÉRENTES INTERFACES
        function goToChat() {
            window.location.href = '/louna-interface-nouvelle.html';
        }

        function goToBrain3D() {
            window.location.href = '/brain-3d-spectacular.html';
        }

        function goToDiagnostic() {
            window.location.href = '/kyber-dashboard.html';
        }

        function goToQITests() {
            window.location.href = '/qi-test-ultra-avance.html';
        }

        function goToGenerators() {
            window.location.href = '/image-generator.html';
        }

        function goToThermalMemory() {
            window.location.href = '/thermal-memory-dashboard.html';
        }

        // MISE À JOUR DES VRAIES MÉTRIQUES SYSTÈME
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('neurons').textContent = data.brain?.activeNeurons?.toLocaleString() || 'N/A';
                    document.getElementById('synapses').textContent = data.brain?.synapticConnections?.toLocaleString() || 'N/A';
                    document.getElementById('temperature').textContent = (data.brain?.temperature || 0).toFixed(1) + '°C';
                    document.getElementById('qi').textContent = Math.floor(data.brain?.qi?.total || 0);
                }
            } catch (error) {
                console.error('Erreur métriques:', error);
                document.getElementById('neurons').textContent = 'Erreur';
                document.getElementById('synapses').textContent = 'Erreur';
                document.getElementById('temperature').textContent = 'Erreur';
                document.getElementById('qi').textContent = 'Erreur';
            }
        }

        // INITIALISATION
        document.addEventListener('DOMContentLoaded', () => {
            updateMetrics();
            
            // Actualisation automatique toutes les 10 secondes
            setInterval(updateMetrics, 10000);
        });
    </script>
</body>
</html>
