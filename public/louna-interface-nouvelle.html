<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Autonome - NOUVELLE INTERFACE SANS CACHE</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 15px;
        }

        /* Header principal */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 8px 32px rgba(255, 107, 157, 0.3);
        }

        .header-title h1 {
            font-size: 2.2em;
            margin: 0;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .header-title p {
            font-size: 0.9em;
            opacity: 0.8;
            margin: 0;
            color: #00ff00;
            font-weight: bold;
        }

        .status-badge {
            background: linear-gradient(135deg, #00d4aa, #00b894);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
        }

        /* Section Chat avec pensées continues */
        .chat-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chat-title {
            text-align: center;
            font-size: 1.8em;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #ff6b9d, #00d4aa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .chat-input {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1.1em;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .send-btn {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1.1em;
            width: 100%;
            margin-bottom: 20px;
        }

        .thoughts-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .thought-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #ff6b9d;
        }

        .thought-time {
            font-size: 0.8em;
            color: #00d4aa;
            font-weight: bold;
        }

        .thought-content {
            margin-top: 5px;
            line-height: 1.4;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .metric-number {
            font-size: 2em;
            font-weight: 700;
            color: #00d4aa;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header principal -->
        <div class="header">
            <div class="header-left">
                <div class="logo-icon">🧠</div>
                <div class="header-title">
                    <h1>LOUNA AI Ultra-Autonome</h1>
                    <p>✅ NOUVELLE INTERFACE SANS CACHE - PENSÉES CONTINUES ACTIVES</p>
                </div>
            </div>
            <div style="display: flex; gap: 15px; align-items: center;">
                <button onclick="goToHome()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-weight: 600;">
                    🏠 Accueil
                </button>
                <button onclick="toggleVoiceReflection()" id="voiceBtn" style="background: linear-gradient(135deg, #ff6b9d, #c44569); color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-weight: 600;">
                    🔊 Écouter Pensées
                </button>
                <div class="status-badge">SYSTÈME ACTIF</div>
            </div>
        </div>

        <!-- Section Chat avec pensées continues -->
        <div class="chat-section">
            <div class="chat-title">💬 Chat IA avec Pensées Continues</div>
            
            <input type="text" class="chat-input" id="messageInput" placeholder="Tapez votre message à LOUNA AI...">
            <button class="send-btn" onclick="sendMessage()">🚀 Envoyer Message</button>
            
            <div id="chatResponse" style="background: rgba(0, 0, 0, 0.3); padding: 20px; border-radius: 15px; margin-bottom: 20px; display: none;">
                <h3 style="color: #00d4aa; margin-bottom: 10px;">Réponse de LOUNA AI:</h3>
                <div id="responseText"></div>
            </div>

            <div class="thoughts-display">
                <h3 style="color: #ff6b9d; margin-bottom: 15px;">🧠 Pensées Continues de LOUNA AI:</h3>
                <div id="thoughtsList">
                    <div class="thought-item">
                        <div class="thought-time">Chargement des pensées continues...</div>
                        <div class="thought-content">🧠 Initialisation du système de pensées continues...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Métriques système -->
        <div class="chat-section">
            <div class="chat-title">📊 Métriques Système en Temps Réel</div>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-number" id="neurons">1,064,000</div>
                    <div class="metric-label">Neurones Actifs</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number" id="synapses">7,448,000</div>
                    <div class="metric-label">Connexions Synaptiques</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number" id="temperature">37.0°C</div>
                    <div class="metric-label">Température Thermique</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number" id="qi">450</div>
                    <div class="metric-label">QI Total</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // VARIABLES GLOBALES POUR LA SYNTHÈSE VOCALE
        let voiceEnabled = false;
        let currentVoice = null;
        let lastSpokenThought = null;

        // NAVIGATION VERS L'ACCUEIL
        function goToHome() {
            window.location.href = '/interface-spectaculaire.html';
        }

        // INITIALISER LA SYNTHÈSE VOCALE
        function initializeVoice() {
            if ('speechSynthesis' in window) {
                const voices = speechSynthesis.getVoices();
                // Chercher une voix française féminine
                currentVoice = voices.find(voice =>
                    voice.lang.includes('fr') &&
                    (voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('femme'))
                ) || voices.find(voice => voice.lang.includes('fr')) || voices[0];

                console.log('🔊 Voix sélectionnée pour les pensées:', currentVoice?.name);
            }
        }

        // ACTIVER/DÉSACTIVER L'ÉCOUTE DES PENSÉES
        function toggleVoiceReflection() {
            voiceEnabled = !voiceEnabled;
            const btn = document.getElementById('voiceBtn');

            if (voiceEnabled) {
                btn.textContent = '🔇 Arrêter Écoute';
                btn.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
                console.log('🔊 Écoute des pensées ACTIVÉE');

                // Démarrer la réflexion continue sur le serveur
                fetch('/api/thoughts/control', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'start' })
                });
            } else {
                btn.textContent = '🔊 Écouter Pensées';
                btn.style.background = 'linear-gradient(135deg, #ff6b9d, #c44569)';
                console.log('🔇 Écoute des pensées DÉSACTIVÉE');

                // Arrêter toute synthèse en cours
                if ('speechSynthesis' in window) {
                    speechSynthesis.cancel();
                }
            }
        }

        // PARLER UNE PENSÉE
        function speakThought(thoughtContent) {
            if (!voiceEnabled || !('speechSynthesis' in window)) return;

            // Arrêter toute synthèse en cours
            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(thoughtContent);

            if (currentVoice) {
                utterance.voice = currentVoice;
            }

            // Paramètres optimisés pour les pensées
            utterance.rate = 0.8; // Plus lent pour la réflexion
            utterance.pitch = 1.1; // Légèrement aigu
            utterance.volume = 0.7; // Volume modéré

            utterance.onstart = () => {
                console.log('🗣️ LOUNA pense à voix haute:', thoughtContent);
            };

            utterance.onerror = (error) => {
                console.error('❌ Erreur synthèse pensée:', error);
            };

            speechSynthesis.speak(utterance);
        }

        // RÉCUPÉRATION DES VRAIES PENSÉES CONTINUES AVEC SYNTHÈSE VOCALE
        async function loadContinuousThoughts() {
            try {
                const response = await fetch('/api/thoughts/continuous');
                const data = await response.json();

                if (data.success && data.thoughts && data.thoughts.length > 0) {
                    const thoughtsList = document.getElementById('thoughtsList');
                    thoughtsList.innerHTML = '';

                    // Vérifier s'il y a une nouvelle pensée à dire
                    const latestThought = data.thoughts[data.thoughts.length - 1];
                    if (voiceEnabled && latestThought && latestThought.id !== lastSpokenThought) {
                        speakThought(latestThought.content);
                        lastSpokenThought = latestThought.id;
                    }

                    data.thoughts.slice(-10).forEach(thought => {
                        const thoughtDiv = document.createElement('div');
                        thoughtDiv.className = 'thought-item';

                        // Ajouter un indicateur si la pensée a été parlée
                        const spokenIndicator = (thought.id === lastSpokenThought && voiceEnabled) ? '🔊 ' : '';

                        thoughtDiv.innerHTML = `
                            <div class="thought-time">[${thought.time}] ${spokenIndicator}Type: ${thought.type}</div>
                            <div class="thought-content">${thought.content}</div>
                            ${thought.stats ? `<div style="font-size: 0.8em; opacity: 0.7; margin-top: 5px;">
                                Neurones: ${thought.stats.neurons} | Temp: ${thought.stats.temperature}°C | QI: ${thought.stats.qi}
                            </div>` : ''}
                        `;
                        thoughtsList.appendChild(thoughtDiv);
                    });

                    // Mettre à jour le statut de réflexion
                    if (data.isReflecting) {
                        document.querySelector('.chat-title').textContent = '💬 Chat IA avec Réflexion Continue Active 🧠';
                    }
                } else {
                    // Afficher un message si aucune pensée réelle
                    const thoughtsList = document.getElementById('thoughtsList');
                    thoughtsList.innerHTML = `
                        <div class="thought-item">
                            <div class="thought-time">Réflexion continue en cours...</div>
                            <div class="thought-content">🧠 LOUNA AI réfléchit en continu. Activez l'écoute pour entendre ses pensées !</div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erreur chargement pensées:', error);
            }
        }

        // ENVOI DE MESSAGE
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('chatResponse').style.display = 'block';
                    document.getElementById('responseText').textContent = data.response;
                    
                    // Afficher les réflexions d'agent
                    if (data.agentReflections) {
                        const thoughtsList = document.getElementById('thoughtsList');
                        data.agentReflections.forEach(reflection => {
                            const thoughtDiv = document.createElement('div');
                            thoughtDiv.className = 'thought-item';
                            thoughtDiv.innerHTML = `
                                <div class="thought-time">[${reflection.time}] ${reflection.type}</div>
                                <div class="thought-content">${reflection.content}</div>
                            `;
                            thoughtsList.appendChild(thoughtDiv);
                        });
                    }
                }
                
                input.value = '';
            } catch (error) {
                console.error('Erreur envoi message:', error);
            }
        }

        // MISE À JOUR DES MÉTRIQUES
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('neurons').textContent = data.brain?.activeNeurons?.toLocaleString() || '1,064,000';
                    document.getElementById('synapses').textContent = data.brain?.synapticConnections?.toLocaleString() || '7,448,000';
                    document.getElementById('temperature').textContent = (data.brain?.temperature || 37.0).toFixed(1) + '°C';
                    document.getElementById('qi').textContent = Math.floor(data.brain?.qi?.total || 450);
                }
            } catch (error) {
                console.error('Erreur métriques:', error);
            }
        }

        // INITIALISATION
        document.addEventListener('DOMContentLoaded', () => {
            // Initialiser la synthèse vocale
            initializeVoice();

            // Charger les pensées et métriques
            loadContinuousThoughts();
            updateMetrics();

            // Actualisation automatique plus fréquente pour les pensées
            setInterval(loadContinuousThoughts, 3000); // Toutes les 3 secondes
            setInterval(updateMetrics, 10000);

            // Envoi avec Entrée
            document.getElementById('messageInput').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') sendMessage();
            });

            // Réinitialiser la voix quand les voix sont chargées
            if ('speechSynthesis' in window) {
                speechSynthesis.onvoiceschanged = initializeVoice;
            }
        });
    </script>
</body>
</html>
