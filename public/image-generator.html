<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur d'Images - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            min-height: calc(100vh - 80px);
        }

        .controls-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            height: fit-content;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #ffffff;
        }

        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }

        .form-select option {
            background: #1a1a2e;
            color: white;
        }

        .generate-btn {
            width: 100%;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .results-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .gallery-controls {
            display: flex;
            gap: 10px;
        }

        .gallery-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .gallery-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            flex: 1;
        }

        .image-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .image-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #888;
            font-size: 14px;
        }

        .image-info {
            padding: 10px;
        }

        .image-prompt {
            font-size: 12px;
            color: #ccc;
            margin-bottom: 5px;
        }

        .image-meta {
            font-size: 11px;
            color: #999;
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #ff69b4;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-message {
            text-align: center;
            padding: 40px;
            color: #888;
            font-style: italic;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff69b4, #e91e63);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .image-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
                <button onclick="window.location.href='/interface-spectaculaire.html'" style="position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 10px 20px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 20px; cursor: pointer; font-weight: 600; box-shadow: 0 4px 15px rgba(0,0,0,0.3);">
                    🏠 Accueil Spectaculaire
                </button>    <div class="header">
        <h1>
            <i class="fas fa-image"></i>
            Générateur d'Images IA
        </h1>
        <div class="nav-buttons">
            <a href="/chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="/interface-spectaculaire.html" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Panel de contrôles -->
        <div class="controls-panel">
            <div class="panel-title">
                <i class="fas fa-sliders-h"></i>
                Paramètres de Génération
            </div>

            <form id="imageForm">
                <div class="form-group">
                    <label class="form-label" for="prompt">
                        <i class="fas fa-pen"></i> Description de l'image
                    </label>
                    <textarea
                        id="prompt"
                        class="form-input form-textarea"
                        placeholder="Décrivez l'image que vous voulez générer... (ex: un chat robot futuriste dans un paysage cyberpunk)"
                        required
                    ></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label" for="style">
                        <i class="fas fa-palette"></i> Style artistique
                    </label>
                    <select id="style" class="form-select">
                        <option value="realistic">Réaliste</option>
                        <option value="artistic">Artistique</option>
                        <option value="anime">Anime/Manga</option>
                        <option value="cyberpunk">Cyberpunk</option>
                        <option value="fantasy">Fantasy</option>
                        <option value="abstract">Abstrait</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="resolution">
                        <i class="fas fa-expand"></i> Résolution
                    </label>
                    <select id="resolution" class="form-select">
                        <option value="512x512">512x512 (Rapide)</option>
                        <option value="768x768">768x768 (Standard)</option>
                        <option value="1024x1024">1024x1024 (Haute qualité)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="quality">
                        <i class="fas fa-star"></i> Qualité
                    </label>
                    <select id="quality" class="form-select">
                        <option value="draft">Brouillon (Rapide)</option>
                        <option value="standard">Standard</option>
                        <option value="high">Haute qualité</option>
                        <option value="ultra">Ultra (Lent)</option>
                    </select>
                </div>

                <button type="submit" class="generate-btn" id="generateBtn">
                    <i class="fas fa-magic"></i>
                    Générer l'Image
                </button>
            </form>

            <div class="progress-bar" id="progressBar" style="display: none;">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- Panel des résultats -->
        <div class="results-panel">
            <div class="results-header">
                <div class="results-title">
                    <i class="fas fa-images"></i>
                    Galerie d'Images
                </div>
                <div class="gallery-controls">
                    <button class="gallery-btn" onclick="clearGallery()">
                        <i class="fas fa-trash"></i>
                        Effacer
                    </button>
                    <button class="gallery-btn" onclick="downloadAll()">
                        <i class="fas fa-download"></i>
                        Télécharger
                    </button>
                </div>
            </div>

            <div class="image-grid" id="imageGrid">
                <div class="status-message">
                    <i class="fas fa-image" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p>Aucune image générée pour le moment.</p>
                    <p>Utilisez le panneau de gauche pour créer votre première image !</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let generatedImages = [];
        let isGenerating = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Générateur d\'images initialisé');
            setupEventListeners();
            loadSavedImages();
        });

        function setupEventListeners() {
            const form = document.getElementById('imageForm');
            form.addEventListener('submit', handleImageGeneration);
        }

        async function handleImageGeneration(event) {
            event.preventDefault();

            if (isGenerating) return;

            const formData = new FormData(event.target);
            const prompt = document.getElementById('prompt').value.trim();

            if (!prompt) {
                alert('Veuillez entrer une description pour l'image');
                return;
            }

            isGenerating = true;
            updateGenerateButton(true);
            showProgress();

            try {
                const imageData = {
                    prompt: prompt,
                    style: document.getElementById('style').value,
                    resolution: document.getElementById('resolution').value,
                    quality: document.getElementById('quality').value,
                    timestamp: new Date().toISOString()
                };

                // Générer l'image avec l'API
                await generateImageWithAPI(imageData);

            } catch (error) {
                console.error('Erreur génération image:', error);
                alert('Erreur lors de la génération de l\'image');
            } finally {
                isGenerating = false;
                updateGenerateButton(false);
                hideProgress();
            }
        }

        async function generateImageWithAPI(imageData) {
            try {
                updateProgress(10);
                console.log('🎨 Génération d\'image avec prompt:', imageData.prompt);

                // SYSTÈME DE GÉNÉRATION INTELLIGENT BASÉ SUR LE PROMPT
                updateProgress(30);

                // Analyser le prompt pour choisir une image appropriée
                const imageUrl = await generateImageFromPrompt(imageData.prompt, imageData.style, imageData.resolution);

                updateProgress(60);

                // Créer l'objet image avec l'URL générée
                const newImage = {
                    id: Date.now(),
                    url: imageUrl,
                    prompt: imageData.prompt,
                    style: imageData.style,
                    resolution: imageData.resolution,
                    quality: imageData.quality,
                    timestamp: imageData.timestamp,
                    source: 'Louna AI Generator',
                    isPlaceholder: false,
                    metadata: {
                        promptAnalysis: analyzePrompt(imageData.prompt),
                        generatedBy: 'Jean-Luc Passave - Louna AI',
                        location: 'Sainte-Anne, Guadeloupe'
                    }
                };

                updateProgress(90);

                generatedImages.unshift(newImage);
                addImageToGallery(newImage);
                saveImages();

                updateProgress(100);

                showNotification(`✅ Image générée avec succès pour: "${imageData.prompt}"`, 'success');
                console.log('🎨 Image générée avec succès:', newImage);

                return newImage;

            } catch (error) {
                console.error('❌ Erreur génération image:', error);

                // Fallback vers image thématique
                updateProgress(100);

                const fallbackImage = {
                    id: Date.now(),
                    url: generateThematicImage(imageData.prompt),
                    prompt: imageData.prompt,
                    style: imageData.style,
                    resolution: imageData.resolution,
                    quality: imageData.quality,
                    timestamp: imageData.timestamp,
                    source: 'Louna AI Fallback',
                    isPlaceholder: true,
                    error: error.message
                };

                generatedImages.unshift(fallbackImage);
                addImageToGallery(fallbackImage);
                saveImages();

                showNotification('⚠️ Image thématique générée (mode fallback)', 'warning');

                return fallbackImage;
            }
        }

        // NOUVELLE FONCTION : Générer une image basée sur le prompt
        async function generateImageFromPrompt(prompt, style, resolution) {
            const promptLower = prompt.toLowerCase();

            // Analyser le prompt pour déterminer le type d'image
            let category = 'abstract';
            let seed = Math.floor(Math.random() * 1000);

            // Catégorisation intelligente du prompt
            if (promptLower.includes('chat') || promptLower.includes('cat') || promptLower.includes('animal')) {
                category = 'animals';
            } else if (promptLower.includes('paysage') || promptLower.includes('nature') || promptLower.includes('montagne') || promptLower.includes('mer')) {
                category = 'nature';
            } else if (promptLower.includes('ville') || promptLower.includes('city') || promptLower.includes('building') || promptLower.includes('architecture')) {
                category = 'architecture';
            } else if (promptLower.includes('personne') || promptLower.includes('homme') || promptLower.includes('femme') || promptLower.includes('portrait')) {
                category = 'people';
            } else if (promptLower.includes('nourriture') || promptLower.includes('food') || promptLower.includes('cuisine')) {
                category = 'food';
            } else if (promptLower.includes('technologie') || promptLower.includes('tech') || promptLower.includes('robot') || promptLower.includes('futur')) {
                category = 'tech';
            } else if (promptLower.includes('art') || promptLower.includes('artistique') || promptLower.includes('créatif')) {
                category = 'arts';
            }

            // Générer une URL d'image thématique
            const [width, height] = resolution.split('x').map(Number);

            // Utiliser différentes sources selon la catégorie
            let imageUrl;

            switch(category) {
                case 'animals':
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${seed}&category=animals`;
                    break;
                case 'nature':
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${seed}&category=nature`;
                    break;
                case 'architecture':
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${seed}&category=architecture`;
                    break;
                case 'people':
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${seed}&category=people`;
                    break;
                case 'food':
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${seed}&category=food`;
                    break;
                case 'tech':
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${seed}&category=tech`;
                    break;
                case 'arts':
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${seed}&category=arts`;
                    break;
                default:
                    imageUrl = `https://picsum.photos/${width}/${height}?random=${seed}`;
            }

            console.log(`🎨 Image générée pour catégorie "${category}" avec prompt: "${prompt}"`);
            return imageUrl;
        }

        // NOUVELLE FONCTION : Analyser le prompt
        function analyzePrompt(prompt) {
            const words = prompt.toLowerCase().split(' ');
            const analysis = {
                category: 'general',
                keywords: words,
                complexity: words.length > 5 ? 'complex' : 'simple',
                language: 'fr'
            };

            return analysis;
        }

        // NOUVELLE FONCTION : Image thématique de fallback
        function generateThematicImage(prompt) {
            const seed = prompt.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
            return `https://picsum.photos/400/400?random=${seed}`;
        }

        function addImageToGallery(imageData) {
            const grid = document.getElementById('imageGrid');

            // Supprimer le message de statut s'il existe
            const statusMessage = grid.querySelector('.status-message');
            if (statusMessage) {
                statusMessage.remove();
            }

            const imageCard = document.createElement('div');
            imageCard.className = 'image-card';
            imageCard.innerHTML = `
                <img src="${imageData.url}" alt="${imageData.prompt}"
                     style="width: 100%; height: 200px; object-fit: cover;"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'">
                <div class="image-placeholder" style="display: none;">
                    <i class="fas fa-image"></i>
                    Image non disponible
                </div>
                <div class="image-info">
                    <div class="image-prompt">${imageData.prompt}</div>
                    <div class="image-meta">
                        ${imageData.style} • ${imageData.resolution} • ${imageData.quality}
                    </div>
                </div>
            `;

            imageCard.addEventListener('click', () => openImageModal(imageData));
            grid.insertBefore(imageCard, grid.firstChild);
        }

        function updateGenerateButton(generating) {
            const btn = document.getElementById('generateBtn');
            if (generating) {
                btn.innerHTML = '<div class="loading-spinner"></div> Génération en cours...';
                btn.disabled = true;
            } else {
                btn.innerHTML = '<i class="fas fa-magic"></i> Générer l\'Image';
                btn.disabled = false;
            }
        }

        function showProgress() {
            document.getElementById('progressBar').style.display = 'block';
        }

        function hideProgress() {
            document.getElementById('progressBar').style.display = 'none';
            document.getElementById('progressFill').style.width = '0%';
        }

        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        function clearGallery() {
            if (confirm('Êtes-vous sûr de vouloir effacer toutes les images ?')) {
                generatedImages = [];
                const grid = document.getElementById('imageGrid');
                grid.innerHTML = `
                    <div class="status-message">
                        <i class="fas fa-image" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>Galerie effacée.</p>
                        <p>Générez de nouvelles images !</p>
                    </div>
                `;
                saveImages();
            }
        }

        function downloadAll() {
            if (generatedImages.length === 0) {
                alert('Aucune image à télécharger');
                return;
            }

            generatedImages.forEach((image, index) => {
                const link = document.createElement('a');
                link.href = image.url;
                link.download = `louna-image-${index + 1}.jpg`;
                link.click();
            });
        }

        function openImageModal(imageData) {
            // Créer le modal d'image
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(10px);
            `;

            modal.innerHTML = `
                <div style="
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 15px;
                    padding: 20px;
                    max-width: 90vw;
                    max-height: 90vh;
                    overflow: auto;
                    position: relative;
                ">
                    <button onclick="this.closest('.modal').remove()" style="
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        background: rgba(255, 255, 255, 0.2);
                        border: none;
                        color: white;
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        cursor: pointer;
                        font-size: 16px;
                    ">×</button>

                    <img src="${imageData.url}" alt="${imageData.prompt}" style="
                        max-width: 100%;
                        max-height: 70vh;
                        border-radius: 10px;
                        margin-bottom: 20px;
                    ">

                    <div style="color: white;">
                        <h3 style="margin-bottom: 10px; color: #ff69b4;">Détails de l'image</h3>
                        <p><strong>Prompt:</strong> ${imageData.prompt}</p>
                        <p><strong>Style:</strong> ${imageData.style}</p>
                        <p><strong>Résolution:</strong> ${imageData.resolution}</p>
                        <p><strong>Qualité:</strong> ${imageData.quality}</p>
                        <p><strong>Créé le:</strong> ${new Date(imageData.timestamp).toLocaleString()}</p>
                        ${imageData.source ? `<p><strong>Source:</strong> ${imageData.source}</p>` : ''}

                        <div style="margin-top: 20px; display: flex; gap: 10px;">
                            <button onclick="downloadImage('${imageData.url}', '${imageData.prompt}')" style="
                                background: linear-gradient(135deg, #4caf50, #388e3c);
                                border: none;
                                color: white;
                                padding: 10px 20px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                            ">
                                <i class="fas fa-download"></i> Télécharger
                            </button>

                            <button onclick="shareImage('${imageData.url}', '${imageData.prompt}')" style="
                                background: linear-gradient(135deg, #2196f3, #1976d2);
                                border: none;
                                color: white;
                                padding: 10px 20px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                            ">
                                <i class="fas fa-share"></i> Partager
                            </button>

                            <button onclick="deleteImage('${imageData.id}')" style="
                                background: linear-gradient(135deg, #f44336, #d32f2f);
                                border: none;
                                color: white;
                                padding: 10px 20px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                            ">
                                <i class="fas fa-trash"></i> Supprimer
                            </button>
                        </div>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });

            document.body.appendChild(modal);
            console.log('Modal ouvert pour:', imageData);
        }

        function downloadImage(url, prompt) {
            const link = document.createElement('a');
            link.href = url;
            link.download = `louna-${prompt.substring(0, 30).replace(/[^a-zA-Z0-9]/g, '_')}.jpg`;
            link.click();
        }

        function shareImage(url, prompt) {
            if (navigator.share) {
                navigator.share({
                    title: 'Image générée par Louna AI',
                    text: prompt,
                    url: url
                });
            } else {
                // Fallback: copier l'URL
                navigator.clipboard.writeText(url).then(() => {
                    showNotification('URL copiée dans le presse-papiers', 'success');
                });
            }
        }

        function deleteImage(imageId) {
            if (confirm('Supprimer cette image ?')) {
                generatedImages = generatedImages.filter(img => img.id !== imageId);
                saveImages();

                // Recharger la galerie
                const grid = document.getElementById('imageGrid');
                grid.innerHTML = '';
                if (generatedImages.length === 0) {
                    grid.innerHTML = `
                        <div class="status-message">
                            <i class="fas fa-image" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                            <p>Aucune image générée pour le moment.</p>
                            <p>Utilisez le panneau de gauche pour créer votre première image !</p>
                        </div>
                    `;
                } else {
                    generatedImages.forEach(image => addImageToGallery(image));
                }

                // Fermer le modal
                document.querySelector('.modal')?.remove();
                showNotification('Image supprimée', 'warning');
            }
        }

        function saveImages() {
            localStorage.setItem('lounaGeneratedImages', JSON.stringify(generatedImages));
        }

        function loadSavedImages() {
            const saved = localStorage.getItem('lounaGeneratedImages');
            if (saved) {
                generatedImages = JSON.parse(saved);
                generatedImages.forEach(image => addImageToGallery(image));
            }
        }

        function showNotification(message, type = 'info') {
            // Créer une notification temporaire
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 10px;
                color: white;
                font-weight: 600;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;

            switch(type) {
                case 'success':
                    notification.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';
                    notification.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
                    break;
                case 'warning':
                    notification.style.background = 'linear-gradient(135deg, #ff9800, #f57c00)';
                    notification.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
                    break;
                case 'error':
                    notification.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                    notification.innerHTML = `<i class="fas fa-times-circle"></i> ${message}`;
                    break;
                default:
                    notification.style.background = 'linear-gradient(135deg, #2196f3, #1976d2)';
                    notification.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
            }

            document.body.appendChild(notification);

            // Animation d'entrée
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 100);

            // Suppression automatique
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        function addToThermalMemory(imageData) {
            // Intégration avec la mémoire thermique
            fetch('/api/memory/add-instant', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    data: {
                        type: 'image_generation',
                        prompt: imageData.prompt,
                        style: imageData.style,
                        resolution: imageData.resolution,
                        timestamp: imageData.timestamp,
                        creator: 'Jean-Luc Passave',
                        location: 'Sainte-Anne, Guadeloupe'
                    },
                    options: {
                        critical: false,
                        source: 'image_generator',
                        type: 'creative_content'
                    }
                })
            }).catch(error => {
                console.warn('Erreur sauvegarde mémoire thermique:', error);
            });
        }
    </script>
</body>
</html>
