/**
 * 🧠 TESTS QI AVANCÉS - LOUNA AI
 * Système d'évaluation cognitive avancé pour mesurer l'intelligence artificielle
 */

class AdvancedIQTests {
    constructor() {
        this.currentTest = null;
        this.testResults = [];
        this.currentScore = 0;
        this.totalQuestions = 0;
        this.timeStarted = null;
        this.testTypes = {
            logical: 'Raisonnement Logique',
            mathematical: 'Mathématiques Avancées',
            pattern: 'Reconnaissance de Motifs',
            verbal: 'Compréhension Verbale',
            spatial: 'Intelligence Spatiale',
            memory: 'Mémoire de Travail',
            processing: 'Vitesse de Traitement'
        };
        
        this.init();
    }
    
    init() {
        console.log('🧠 Initialisation des tests QI avancés...');
        this.loadTestDatabase();
        console.log('✅ Tests QI avancés initialisés !');
    }
    
    loadTestDatabase() {
        // Base de données de questions de test QI
        this.testDatabase = {
            logical: [
                {
                    question: "Si A > B et B > C, alors :",
                    options: ["A < C", "A > C", "A = C", "Impossible à déterminer"],
                    correct: 1,
                    difficulty: 1,
                    explanation: "Par transitivité, si A > B et B > C, alors A > C"
                },
                {
                    question: "Tous les X sont Y. Tous les Y sont Z. Donc :",
                    options: ["Tous les Z sont X", "Tous les X sont Z", "Aucun X n'est Z", "Certains Z sont X"],
                    correct: 1,
                    difficulty: 2,
                    explanation: "Par syllogisme logique, tous les X sont Z"
                },
                {
                    question: "Si P → Q et Q → R, alors P → ?",
                    options: ["¬R", "R", "¬P", "Indéterminé"],
                    correct: 1,
                    difficulty: 3,
                    explanation: "Par transitivité logique, P → R"
                }
            ],
            mathematical: [
                {
                    question: "Quelle est la suite : 2, 6, 12, 20, 30, ?",
                    options: ["40", "42", "44", "46"],
                    correct: 1,
                    difficulty: 2,
                    explanation: "Suite des nombres n(n+1) : 1×2, 2×3, 3×4, 4×5, 5×6, 6×7=42"
                },
                {
                    question: "Si f(x) = x² + 3x - 2, que vaut f'(2) ?",
                    options: ["5", "7", "9", "11"],
                    correct: 1,
                    difficulty: 3,
                    explanation: "f'(x) = 2x + 3, donc f'(2) = 2(2) + 3 = 7"
                },
                {
                    question: "Résolvez : ∫(2x + 1)dx",
                    options: ["x² + x + C", "2x² + x + C", "x² + 2x + C", "2x + C"],
                    correct: 0,
                    difficulty: 4,
                    explanation: "∫(2x + 1)dx = x² + x + C"
                }
            ],
            pattern: [
                {
                    question: "Quelle forme suit : ○ △ □ ○ △ ?",
                    options: ["○", "△", "□", "◇"],
                    correct: 2,
                    difficulty: 1,
                    explanation: "Motif répétitif : cercle, triangle, carré"
                },
                {
                    question: "Suite numérique : 1, 4, 9, 16, 25, ?",
                    options: ["30", "32", "36", "40"],
                    correct: 2,
                    difficulty: 2,
                    explanation: "Carrés parfaits : 1², 2², 3², 4², 5², 6²=36"
                }
            ],
            verbal: [
                {
                    question: "Quel mot n'appartient pas au groupe : Chien, Chat, Oiseau, Voiture",
                    options: ["Chien", "Chat", "Oiseau", "Voiture"],
                    correct: 3,
                    difficulty: 1,
                    explanation: "Voiture n'est pas un animal vivant"
                },
                {
                    question: "Analogie : Livre est à Lire comme Musique est à ?",
                    options: ["Entendre", "Écouter", "Son", "Note"],
                    correct: 1,
                    difficulty: 2,
                    explanation: "Action correspondante : on lit un livre, on écoute de la musique"
                }
            ],
            spatial: [
                {
                    question: "Si vous pliez un carré en deux, puis encore en deux, combien de couches avez-vous ?",
                    options: ["2", "4", "6", "8"],
                    correct: 1,
                    difficulty: 1,
                    explanation: "Chaque pliage double le nombre de couches : 1→2→4"
                }
            ],
            memory: [
                {
                    question: "Mémorisez : 7, 3, 9, 1, 5. Quelle était la séquence ?",
                    options: ["7,3,9,1,5", "3,7,1,9,5", "7,9,3,5,1", "5,1,9,3,7"],
                    correct: 0,
                    difficulty: 2,
                    explanation: "Séquence exacte mémorisée"
                }
            ],
            processing: [
                {
                    question: "Comptez rapidement les 'A' : ABACADAEAFAGA",
                    options: ["5", "6", "7", "8"],
                    correct: 2,
                    difficulty: 2,
                    explanation: "7 lettres 'A' dans la séquence"
                }
            ]
        };
    }
    
    startTest(testType = 'mixed', difficulty = 'adaptive') {
        console.log(`🧠 Démarrage du test QI : ${testType}`);
        
        this.currentTest = {
            type: testType,
            difficulty: difficulty,
            questions: this.generateTestQuestions(testType, difficulty),
            currentQuestion: 0,
            score: 0,
            startTime: Date.now(),
            answers: []
        };
        
        this.timeStarted = Date.now();
        this.displayCurrentQuestion();
        
        return this.currentTest;
    }
    
    generateTestQuestions(testType, difficulty) {
        let questions = [];
        
        if (testType === 'mixed') {
            // Test mixte avec questions de tous types
            Object.keys(this.testDatabase).forEach(type => {
                questions = questions.concat(this.testDatabase[type].slice(0, 2));
            });
        } else if (this.testDatabase[testType]) {
            questions = this.testDatabase[testType];
        }
        
        // Mélanger les questions
        return this.shuffleArray(questions);
    }
    
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    
    displayCurrentQuestion() {
        if (!this.currentTest) return;
        
        const question = this.currentTest.questions[this.currentTest.currentQuestion];
        if (!question) {
            this.finishTest();
            return;
        }
        
        const questionData = {
            number: this.currentTest.currentQuestion + 1,
            total: this.currentTest.questions.length,
            question: question.question,
            options: question.options,
            difficulty: question.difficulty,
            timeRemaining: this.getTimeRemaining()
        };
        
        // Émettre l'événement pour l'interface
        this.emitEvent('questionDisplayed', questionData);
        
        return questionData;
    }
    
    answerQuestion(answerIndex) {
        if (!this.currentTest) return false;
        
        const question = this.currentTest.questions[this.currentTest.currentQuestion];
        const isCorrect = answerIndex === question.correct;
        const timeSpent = Date.now() - this.timeStarted;
        
        // Enregistrer la réponse
        this.currentTest.answers.push({
            questionIndex: this.currentTest.currentQuestion,
            answer: answerIndex,
            correct: isCorrect,
            timeSpent: timeSpent,
            difficulty: question.difficulty
        });
        
        if (isCorrect) {
            this.currentTest.score += this.calculateQuestionScore(question.difficulty, timeSpent);
        }
        
        this.currentTest.currentQuestion++;
        this.timeStarted = Date.now();
        
        // Afficher la question suivante ou finir le test
        if (this.currentTest.currentQuestion < this.currentTest.questions.length) {
            this.displayCurrentQuestion();
        } else {
            this.finishTest();
        }
        
        return {
            correct: isCorrect,
            explanation: question.explanation,
            score: this.currentTest.score
        };
    }
    
    calculateQuestionScore(difficulty, timeSpent) {
        // Score basé sur la difficulté et le temps de réponse
        const baseScore = difficulty * 10;
        const timeBonus = Math.max(0, 30000 - timeSpent) / 1000; // Bonus pour rapidité
        return Math.round(baseScore + timeBonus);
    }
    
    finishTest() {
        if (!this.currentTest) return;
        
        const totalTime = Date.now() - this.currentTest.startTime;
        const correctAnswers = this.currentTest.answers.filter(a => a.correct).length;
        const totalQuestions = this.currentTest.questions.length;
        const accuracy = (correctAnswers / totalQuestions) * 100;
        
        // Calculer le QI estimé
        const estimatedIQ = this.calculateIQ(accuracy, totalTime, this.currentTest.score);
        
        const results = {
            type: this.currentTest.type,
            score: this.currentTest.score,
            correctAnswers: correctAnswers,
            totalQuestions: totalQuestions,
            accuracy: accuracy,
            totalTime: totalTime,
            averageTime: totalTime / totalQuestions,
            estimatedIQ: estimatedIQ,
            timestamp: new Date(),
            answers: this.currentTest.answers
        };
        
        this.testResults.push(results);
        this.currentTest = null;
        
        // Émettre l'événement de fin de test
        this.emitEvent('testCompleted', results);
        
        console.log('🎯 Test QI terminé :', results);
        return results;
    }
    
    calculateIQ(accuracy, totalTime, score) {
        // Formule complexe pour estimer le QI
        const baseIQ = 100;
        const accuracyBonus = (accuracy - 50) * 2; // +2 points par % au-dessus de 50%
        const speedBonus = Math.max(0, (60000 - totalTime / this.currentTest.questions.length) / 1000); // Bonus vitesse
        const scoreBonus = score / 10;
        
        const estimatedIQ = Math.round(baseIQ + accuracyBonus + speedBonus + scoreBonus);
        return Math.max(70, Math.min(200, estimatedIQ)); // Limiter entre 70 et 200
    }
    
    getTimeRemaining() {
        if (!this.timeStarted) return 60000;
        const elapsed = Date.now() - this.timeStarted;
        return Math.max(0, 60000 - elapsed); // 60 secondes par question
    }
    
    getTestHistory() {
        return this.testResults;
    }
    
    getAverageIQ() {
        if (this.testResults.length === 0) return 0;
        const total = this.testResults.reduce((sum, result) => sum + result.estimatedIQ, 0);
        return Math.round(total / this.testResults.length);
    }
    
    emitEvent(eventName, data) {
        // Émettre un événement personnalisé
        const event = new CustomEvent(`iqTest_${eventName}`, { detail: data });
        document.dispatchEvent(event);
    }
    
    // Méthodes utilitaires
    getCurrentTest() {
        return this.currentTest;
    }
    
    isTestActive() {
        return this.currentTest !== null;
    }
    
    abortTest() {
        if (this.currentTest) {
            console.log('🛑 Test QI interrompu');
            this.currentTest = null;
            this.emitEvent('testAborted', {});
        }
    }
}

// Export pour utilisation globale
window.AdvancedIQTests = AdvancedIQTests;
