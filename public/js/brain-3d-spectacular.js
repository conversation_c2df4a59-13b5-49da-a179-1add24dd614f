/**
 * 🧠 CERVEAU 3D SPECTACULAIRE - LOUNA AI
 * Visualisation 3D avancée du cerveau artificiel avec Three.js
 */

class SpectacularBrain3D {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.brain = null;
        this.neurons = [];
        this.connections = [];
        this.animationId = null;
        this.isRotating = true;
        this.neuronCount = 0;
        this.connectionCount = 0;
        
        this.init();
    }
    
    init() {
        console.log('🧠 Initialisation du cerveau 3D spectaculaire...');
        
        // Créer la scène
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0a0a0a);
        
        // Créer la caméra
        this.camera = new THREE.PerspectiveCamera(
            75, 
            this.container.clientWidth / this.container.clientHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 0, 5);
        
        // Créer le renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.container.appendChild(this.renderer.domElement);
        
        // Ajouter les lumières
        this.addLights();
        
        // Créer le cerveau
        this.createBrain();
        
        // Créer les neurones
        this.createNeurons();
        
        // Créer les connexions
        this.createConnections();
        
        // Démarrer l'animation
        this.animate();
        
        // Gestion du redimensionnement
        window.addEventListener('resize', () => this.onWindowResize());
        
        console.log('✅ Cerveau 3D spectaculaire initialisé !');
    }
    
    addLights() {
        // Lumière ambiante
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        
        // Lumière directionnelle principale
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
        
        // Lumières colorées pour l'effet spectaculaire
        const light1 = new THREE.PointLight(0xff00ff, 0.5, 10);
        light1.position.set(-3, 2, 3);
        this.scene.add(light1);
        
        const light2 = new THREE.PointLight(0x00ffff, 0.5, 10);
        light2.position.set(3, -2, -3);
        this.scene.add(light2);
    }
    
    createBrain() {
        // Géométrie du cerveau (forme organique)
        const brainGeometry = new THREE.SphereGeometry(1.5, 32, 32);
        
        // Matériau du cerveau avec effet translucide
        const brainMaterial = new THREE.MeshPhongMaterial({
            color: 0x8a2be2,
            transparent: true,
            opacity: 0.3,
            shininess: 100
        });
        
        this.brain = new THREE.Mesh(brainGeometry, brainMaterial);
        this.brain.castShadow = true;
        this.brain.receiveShadow = true;
        this.scene.add(this.brain);
        
        // Ajouter des détails au cerveau
        this.addBrainDetails();
    }
    
    addBrainDetails() {
        // Créer des circonvolutions cérébrales
        for (let i = 0; i < 20; i++) {
            const detailGeometry = new THREE.SphereGeometry(0.1, 8, 8);
            const detailMaterial = new THREE.MeshPhongMaterial({
                color: 0xda70d6,
                transparent: true,
                opacity: 0.6
            });
            
            const detail = new THREE.Mesh(detailGeometry, detailMaterial);
            
            // Position aléatoire sur la surface du cerveau
            const phi = Math.random() * Math.PI * 2;
            const theta = Math.random() * Math.PI;
            const radius = 1.6;
            
            detail.position.x = radius * Math.sin(theta) * Math.cos(phi);
            detail.position.y = radius * Math.sin(theta) * Math.sin(phi);
            detail.position.z = radius * Math.cos(theta);
            
            this.brain.add(detail);
        }
    }
    
    createNeurons() {
        const neuronGeometry = new THREE.SphereGeometry(0.02, 8, 8);
        
        for (let i = 0; i < 200; i++) {
            // Matériau du neurone avec couleur variable
            const hue = Math.random() * 0.3 + 0.7; // Violet à rose
            const neuronMaterial = new THREE.MeshPhongMaterial({
                color: new THREE.Color().setHSL(hue, 0.8, 0.6),
                emissive: new THREE.Color().setHSL(hue, 0.5, 0.1)
            });
            
            const neuron = new THREE.Mesh(neuronGeometry, neuronMaterial);
            
            // Position aléatoire dans le cerveau
            const radius = Math.random() * 1.4 + 0.5;
            const phi = Math.random() * Math.PI * 2;
            const theta = Math.random() * Math.PI;
            
            neuron.position.x = radius * Math.sin(theta) * Math.cos(phi);
            neuron.position.y = radius * Math.sin(theta) * Math.sin(phi);
            neuron.position.z = radius * Math.cos(theta);
            
            // Animation du neurone
            neuron.userData = {
                originalScale: 1,
                pulseSpeed: Math.random() * 0.02 + 0.01,
                pulsePhase: Math.random() * Math.PI * 2
            };
            
            this.neurons.push(neuron);
            this.scene.add(neuron);
        }
        
        this.neuronCount = this.neurons.length;
    }
    
    createConnections() {
        for (let i = 0; i < 100; i++) {
            const neuron1 = this.neurons[Math.floor(Math.random() * this.neurons.length)];
            const neuron2 = this.neurons[Math.floor(Math.random() * this.neurons.length)];
            
            if (neuron1 !== neuron2) {
                const connection = this.createConnection(neuron1.position, neuron2.position);
                this.connections.push(connection);
                this.scene.add(connection);
            }
        }
        
        this.connectionCount = this.connections.length;
    }
    
    createConnection(pos1, pos2) {
        const points = [pos1, pos2];
        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        
        const material = new THREE.LineBasicMaterial({
            color: 0x00ffff,
            transparent: true,
            opacity: 0.3
        });
        
        const line = new THREE.Line(geometry, material);
        
        // Animation de la connexion
        line.userData = {
            pulseSpeed: Math.random() * 0.03 + 0.01,
            pulsePhase: Math.random() * Math.PI * 2
        };
        
        return line;
    }
    
    animate() {
        this.animationId = requestAnimationFrame(() => this.animate());
        
        const time = Date.now() * 0.001;
        
        // Rotation du cerveau
        if (this.isRotating && this.brain) {
            this.brain.rotation.y += 0.005;
            this.brain.rotation.x += 0.002;
        }
        
        // Animation des neurones (pulsation)
        this.neurons.forEach(neuron => {
            const userData = neuron.userData;
            const pulse = Math.sin(time * userData.pulseSpeed + userData.pulsePhase) * 0.5 + 1;
            neuron.scale.setScalar(userData.originalScale * pulse);
            
            // Changement de couleur selon l'activité
            const intensity = pulse * 0.5 + 0.5;
            neuron.material.emissive.setScalar(intensity * 0.2);
        });
        
        // Animation des connexions
        this.connections.forEach(connection => {
            const userData = connection.userData;
            const pulse = Math.sin(time * userData.pulseSpeed + userData.pulsePhase) * 0.5 + 0.5;
            connection.material.opacity = pulse * 0.6 + 0.1;
        });
        
        this.renderer.render(this.scene, this.camera);
    }
    
    // Méthodes de contrôle
    toggleRotation() {
        this.isRotating = !this.isRotating;
        return this.isRotating;
    }
    
    addNeuron() {
        const neuronGeometry = new THREE.SphereGeometry(0.02, 8, 8);
        const hue = Math.random() * 0.3 + 0.7;
        const neuronMaterial = new THREE.MeshPhongMaterial({
            color: new THREE.Color().setHSL(hue, 0.8, 0.6),
            emissive: new THREE.Color().setHSL(hue, 0.5, 0.1)
        });
        
        const neuron = new THREE.Mesh(neuronGeometry, neuronMaterial);
        
        // Position aléatoire
        const radius = Math.random() * 1.4 + 0.5;
        const phi = Math.random() * Math.PI * 2;
        const theta = Math.random() * Math.PI;
        
        neuron.position.x = radius * Math.sin(theta) * Math.cos(phi);
        neuron.position.y = radius * Math.sin(theta) * Math.sin(phi);
        neuron.position.z = radius * Math.cos(theta);
        
        neuron.userData = {
            originalScale: 1,
            pulseSpeed: Math.random() * 0.02 + 0.01,
            pulsePhase: Math.random() * Math.PI * 2
        };
        
        this.neurons.push(neuron);
        this.scene.add(neuron);
        this.neuronCount++;
        
        // Ajouter quelques connexions au nouveau neurone
        for (let i = 0; i < 3; i++) {
            const randomNeuron = this.neurons[Math.floor(Math.random() * this.neurons.length)];
            if (randomNeuron !== neuron) {
                const connection = this.createConnection(neuron.position, randomNeuron.position);
                this.connections.push(connection);
                this.scene.add(connection);
                this.connectionCount++;
            }
        }
    }
    
    getStats() {
        return {
            neurons: this.neuronCount,
            connections: this.connectionCount,
            isRotating: this.isRotating
        };
    }
    
    onWindowResize() {
        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }
    
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        if (this.renderer && this.container) {
            this.container.removeChild(this.renderer.domElement);
        }
        
        // Nettoyer les ressources Three.js
        this.scene?.clear();
        this.renderer?.dispose();
    }
}

// Export pour utilisation globale
window.SpectacularBrain3D = SpectacularBrain3D;
