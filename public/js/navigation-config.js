/**
 * 🧭 CONFIGURATION NAVIGATION LOUNA AI
 * Système de navigation cohérent pour toutes les interfaces
 */

const NAVIGATION_CONFIG = {
    // 🏠 PAGE D'ACCUEIL PRINCIPALE
    home: {
        url: '/interface-spectaculaire.html',
        title: '🏠 Accueil Spectaculaire',
        description: 'Interface principale LOUNA AI'
    },

    // 💬 INTERFACES DE CHAT (MODERNES)
    chat: {
        main: '/chat-cognitif-ultra-optimise.html',  // 🚀 ULTRA-MODERNE
        complete: '/chat-cognitif-complet.html',
        simple: '/simple-chat.html',
        mcp: '/mcp-chat-interface.html',
        streaming: '/streaming-chat.html',
        electron: '/electron-optimized-interface.html'  // 🚀 ELECTRON OPTIMISÉ
    },

    // 🧠 INTERFACES CERVEAU (AVANCÉES)
    brain: {
        enhanced: '/enhanced-interface.html',  // 🚀 INTERFACE ULTRA-AVANCÉE
        monitoring: '/brain-monitoring-complete.html',
        visualization: '/brain-visualization.html',
        spectacular: '/brain-3d-spectacular.html',
        activity: '/brain-activity.html',
        neural: '/neural-interface.html'
    },

    // 🔧 DIAGNOSTIC ET OUTILS
    diagnostic: {
        kyber: '/kyber-dashboard.html',
        monitoring: '/monitoring-ultra-avance.html',
        control: '/control-dashboard.html',
        hardware: '/hardware-error.html'
    },

    // 🧠 TESTS DE QI
    iq: {
        ultraAdvanced: '/qi-test-ultra-avance.html',
        evolution: '/qi-evolution-test.html',
        manager: '/qi-manager.html',
        simple: '/qi-test-simple.html',
        biological: '/biological-tests.html'
    },

    // ✨ GÉNÉRATEURS IA
    generators: {
        image: '/image-generator.html',
        code: '/virtual-code-studio.html',
        voice: '/advanced-voice-system.html',
        face: '/face-recognition.html'
    },

    // 🧠 MÉMOIRE THERMIQUE
    thermal: {
        dashboard: '/thermal-memory-dashboard.html',
        paradigm: '/thermal-paradigm-explorer.html',
        recovery: '/neuron-recovery-emergency.html'
    },

    // ⚙️ INFORMATIONS SYSTÈME
    system: {
        monitoring: '/monitoring-ultra-avance.html',
        documentation: '/documentation.html',
        privacy: '/privacy-control.html',
        security: '/secure-lock.html'
    },

    // 🎓 FORMATION ET APPRENTISSAGE
    training: {
        interface: '/training-interface.html',
        recovery: '/training-recovery-interface.html',
        language: '/language-training.html',
        learning: '/learning-dashboard.html'
    },

    // 🔬 TESTS ET DÉVELOPPEMENT
    testing: {
        agent: '/agent-test-suite.html',
        quick: '/quick-test.html',
        interface: '/test-interface.html',
        mcp: '/mcp-test-interface.html'
    }
};

/**
 * 🧭 FONCTIONS DE NAVIGATION GLOBALES
 */
class NavigationManager {
    constructor() {
        this.currentPage = window.location.pathname;
        this.history = [];
    }

    // 🏠 Aller à l'accueil
    goHome() {
        this.navigateTo(NAVIGATION_CONFIG.home.url, 'Retour à l\'accueil');
    }

    // 💬 Ouvrir le chat principal (ULTRA-MODERNE)
    openChat() {
        this.navigateTo(NAVIGATION_CONFIG.chat.main, 'Ouverture Chat IA Ultra-Optimisé');
    }

    // 🧠 Ouvrir le cerveau principal (INTERFACE AVANCÉE)
    openBrain() {
        this.navigateTo(NAVIGATION_CONFIG.brain.enhanced, 'Ouverture Interface Cerveau Ultra-Avancée');
    }

    // 🔧 Ouvrir le diagnostic
    openDiagnostic() {
        this.navigateTo(NAVIGATION_CONFIG.diagnostic.kyber, 'Ouverture Diagnostic');
    }

    // 🧠 Ouvrir les tests de QI
    openIQTests() {
        this.navigateTo(NAVIGATION_CONFIG.iq.ultraAdvanced, 'Ouverture Tests QI');
    }

    // ✨ Ouvrir les générateurs
    openGenerators() {
        this.navigateTo(NAVIGATION_CONFIG.generators.image, 'Ouverture Générateurs');
    }

    // 🧠 Ouvrir le cerveau 3D
    openBrain3D() {
        this.navigateTo(NAVIGATION_CONFIG.brain.spectacular, 'Ouverture Cerveau 3D');
    }

    // 🧠 Ouvrir la mémoire thermique
    openThermalMemory() {
        this.navigateTo(NAVIGATION_CONFIG.thermal.dashboard, 'Ouverture Mémoire Thermique');
    }

    // ⚙️ Ouvrir les informations système
    openSystemInfo() {
        this.navigateTo(NAVIGATION_CONFIG.system.monitoring, 'Ouverture Système');
    }

    // 🧭 Navigation générique
    navigateTo(url, message = 'Navigation...') {
        try {
            // Afficher notification si disponible
            if (typeof showNotification === 'function') {
                showNotification(message, 'info');
            }

            // Ajouter à l'historique
            this.history.push({
                from: this.currentPage,
                to: url,
                timestamp: Date.now(),
                message
            });

            // Garder seulement les 50 dernières navigations
            if (this.history.length > 50) {
                this.history = this.history.slice(-50);
            }

            // Naviguer
            if (url.startsWith('http')) {
                window.open(url, '_blank');
            } else {
                window.location.href = url;
            }

            console.log(`🧭 Navigation: ${message} → ${url}`);

        } catch (error) {
            console.error('❌ Erreur navigation:', error);
            if (typeof showNotification === 'function') {
                showNotification('❌ Erreur de navigation', 'error');
            }
        }
    }

    // 🔙 Navigation arrière
    goBack() {
        if (this.history.length > 0) {
            const lastNav = this.history[this.history.length - 1];
            this.navigateTo(lastNav.from, 'Retour en arrière');
        } else {
            this.goHome();
        }
    }

    // 📊 Obtenir l'historique de navigation
    getHistory() {
        return this.history;
    }

    // 🔍 Vérifier si une page existe
    async checkPageExists(url) {
        try {
            const response = await fetch(url, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    // 📋 Obtenir toutes les pages disponibles
    getAllPages() {
        const pages = [];
        
        // Parcourir la configuration
        Object.keys(NAVIGATION_CONFIG).forEach(category => {
            if (typeof NAVIGATION_CONFIG[category] === 'object') {
                if (NAVIGATION_CONFIG[category].url) {
                    // Page simple
                    pages.push({
                        category,
                        name: category,
                        url: NAVIGATION_CONFIG[category].url,
                        title: NAVIGATION_CONFIG[category].title
                    });
                } else {
                    // Sous-pages
                    Object.keys(NAVIGATION_CONFIG[category]).forEach(subPage => {
                        pages.push({
                            category,
                            name: subPage,
                            url: NAVIGATION_CONFIG[category][subPage],
                            title: `${category} - ${subPage}`
                        });
                    });
                }
            }
        });

        return pages;
    }
}

// 🌐 Instance globale du gestionnaire de navigation
const navigationManager = new NavigationManager();

// 🔧 Fonctions globales pour compatibilité
function goHome() { navigationManager.goHome(); }
function openChat() { navigationManager.openChat(); }
function openBrain() { navigationManager.openBrain(); }
function openDiagnostic() { navigationManager.openDiagnostic(); }
function openIQTests() { navigationManager.openIQTests(); }
function openGenerators() { navigationManager.openGenerators(); }
function openBrain3D() { navigationManager.openBrain3D(); }
function openThermalMemory() { navigationManager.openThermalMemory(); }
function openSystemInfo() { navigationManager.openSystemInfo(); }

// 📱 Export pour utilisation dans d'autres scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { NAVIGATION_CONFIG, NavigationManager, navigationManager };
}
