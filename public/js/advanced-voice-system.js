/**
 * 🎤 SYSTÈME VOCAL AVANCÉ - LOUNA AI
 * Reconnaissance vocale et synthèse vocale intelligente
 */

class AdvancedVoiceSystem {
    constructor() {
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.isListening = false;
        this.isProcessing = false;
        this.voices = [];
        this.selectedVoice = null;
        this.conversationHistory = [];
        
        this.init();
    }
    
    init() {
        console.log('🎤 Initialisation du système vocal avancé...');
        
        // Vérifier la compatibilité
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.error('❌ Reconnaissance vocale non supportée');
            return;
        }
        
        // Initialiser la reconnaissance vocale
        this.setupSpeechRecognition();
        
        // Initialiser la synthèse vocale
        this.setupSpeechSynthesis();
        
        console.log('✅ Système vocal avancé initialisé !');
    }
    
    setupSpeechRecognition() {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        // Configuration avancée
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.lang = 'fr-FR';
        this.recognition.maxAlternatives = 3;
        
        // Événements
        this.recognition.onstart = () => {
            console.log('🎤 Écoute démarrée');
            this.isListening = true;
            this.updateUI('listening');
        };
        
        this.recognition.onresult = (event) => {
            this.handleSpeechResult(event);
        };
        
        this.recognition.onerror = (event) => {
            console.error('❌ Erreur reconnaissance vocale:', event.error);
            this.isListening = false;
            this.updateUI('error');
        };
        
        this.recognition.onend = () => {
            console.log('🎤 Écoute terminée');
            this.isListening = false;
            this.updateUI('stopped');
        };
    }
    
    setupSpeechSynthesis() {
        // Charger les voix disponibles
        this.loadVoices();
        
        // Écouter les changements de voix
        if (this.synthesis.onvoiceschanged !== undefined) {
            this.synthesis.onvoiceschanged = () => {
                this.loadVoices();
            };
        }
    }
    
    loadVoices() {
        this.voices = this.synthesis.getVoices();
        
        // Sélectionner une voix française féminine par défaut
        this.selectedVoice = this.voices.find(voice => 
            voice.lang.includes('fr') && voice.name.toLowerCase().includes('female')
        ) || this.voices.find(voice => 
            voice.lang.includes('fr')
        ) || this.voices[0];
        
        console.log('🔊 Voix sélectionnée:', this.selectedVoice?.name);
    }
    
    handleSpeechResult(event) {
        let finalTranscript = '';
        let interimTranscript = '';
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            
            if (event.results[i].isFinal) {
                finalTranscript += transcript;
            } else {
                interimTranscript += transcript;
            }
        }
        
        // Afficher le texte en cours
        if (interimTranscript) {
            this.updateTranscript(interimTranscript, false);
        }
        
        // Traiter le texte final
        if (finalTranscript) {
            this.updateTranscript(finalTranscript, true);
            this.processVoiceCommand(finalTranscript.trim());
        }
    }
    
    async processVoiceCommand(command) {
        if (this.isProcessing) return;
        
        this.isProcessing = true;
        console.log('🎤 Commande vocale reçue:', command);
        
        try {
            // Ajouter à l'historique
            this.conversationHistory.push({
                type: 'user',
                text: command,
                timestamp: new Date()
            });
            
            // Analyser la commande
            const response = await this.analyzeCommand(command);
            
            // Répondre vocalement
            await this.speak(response);
            
            // Ajouter la réponse à l'historique
            this.conversationHistory.push({
                type: 'assistant',
                text: response,
                timestamp: new Date()
            });
            
        } catch (error) {
            console.error('❌ Erreur traitement commande:', error);
            await this.speak("Désolé, je n'ai pas pu traiter votre demande.");
        } finally {
            this.isProcessing = false;
        }
    }
    
    async analyzeCommand(command) {
        const lowerCommand = command.toLowerCase();
        
        // Commandes spéciales
        if (lowerCommand.includes('arrête') || lowerCommand.includes('stop')) {
            this.stopListening();
            return "Écoute arrêtée.";
        }
        
        if (lowerCommand.includes('neurone') || lowerCommand.includes('cerveau')) {
            if (window.brain3D) {
                window.brain3D.addNeuron();
                return "Nouveau neurone ajouté au cerveau artificiel.";
            }
            return "Le cerveau 3D n'est pas disponible.";
        }
        
        if (lowerCommand.includes('rotation') || lowerCommand.includes('tourne')) {
            if (window.brain3D) {
                const isRotating = window.brain3D.toggleRotation();
                return isRotating ? "Rotation du cerveau activée." : "Rotation du cerveau désactivée.";
            }
            return "Le cerveau 3D n'est pas disponible.";
        }
        
        if (lowerCommand.includes('statistique') || lowerCommand.includes('stat')) {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    const qi = Math.round(data.brain?.qi?.total || 0);
                    const neurons = data.brain?.activeNeurons || 0;
                    return `QI actuel : ${qi}. Neurones actifs : ${neurons.toLocaleString()}.`;
                }
            } catch (error) {
                console.error('Erreur récupération stats:', error);
            }
            return "Impossible de récupérer les statistiques.";
        }
        
        // Commande générale - envoyer au chat
        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message: command })
            });
            
            const data = await response.json();
            return data.response || "Je n'ai pas pu traiter votre demande.";
            
        } catch (error) {
            console.error('Erreur API chat:', error);
            return "Erreur de communication avec l'IA.";
        }
    }
    
    speak(text) {
        return new Promise((resolve, reject) => {
            if (!this.synthesis) {
                reject(new Error('Synthèse vocale non disponible'));
                return;
            }
            
            // Arrêter toute synthèse en cours
            this.synthesis.cancel();
            
            const utterance = new SpeechSynthesisUtterance(text);
            
            // Configuration de la voix
            if (this.selectedVoice) {
                utterance.voice = this.selectedVoice;
            }
            
            utterance.rate = 0.9;
            utterance.pitch = 1.1;
            utterance.volume = 0.8;
            
            // Événements
            utterance.onend = () => {
                console.log('🔊 Synthèse vocale terminée');
                resolve();
            };
            
            utterance.onerror = (event) => {
                console.error('❌ Erreur synthèse vocale:', event.error);
                reject(event.error);
            };
            
            // Parler
            this.synthesis.speak(utterance);
            console.log('🔊 Synthèse vocale:', text);
        });
    }
    
    startListening() {
        if (this.isListening || !this.recognition) return;
        
        try {
            this.recognition.start();
        } catch (error) {
            console.error('❌ Erreur démarrage écoute:', error);
        }
    }
    
    stopListening() {
        if (!this.isListening || !this.recognition) return;
        
        try {
            this.recognition.stop();
        } catch (error) {
            console.error('❌ Erreur arrêt écoute:', error);
        }
    }
    
    toggleListening() {
        if (this.isListening) {
            this.stopListening();
        } else {
            this.startListening();
        }
        return this.isListening;
    }
    
    updateUI(status) {
        // Mettre à jour l'interface utilisateur
        const voiceButton = document.getElementById('voice-button');
        const voiceStatus = document.getElementById('voice-status');
        
        if (voiceButton) {
            switch (status) {
                case 'listening':
                    voiceButton.style.background = 'linear-gradient(45deg, #ff1493, #ff69b4)';
                    voiceButton.innerHTML = '🎤 Écoute...';
                    break;
                case 'processing':
                    voiceButton.style.background = 'linear-gradient(45deg, #ffa500, #ff8c00)';
                    voiceButton.innerHTML = '🧠 Traitement...';
                    break;
                case 'stopped':
                    voiceButton.style.background = 'linear-gradient(45deg, #8a2be2, #da70d6)';
                    voiceButton.innerHTML = '🎤 Parler';
                    break;
                case 'error':
                    voiceButton.style.background = 'linear-gradient(45deg, #dc143c, #b22222)';
                    voiceButton.innerHTML = '❌ Erreur';
                    setTimeout(() => {
                        this.updateUI('stopped');
                    }, 2000);
                    break;
            }
        }
        
        if (voiceStatus) {
            voiceStatus.textContent = this.getStatusText(status);
        }
    }
    
    updateTranscript(text, isFinal) {
        const transcriptEl = document.getElementById('voice-transcript');
        if (transcriptEl) {
            if (isFinal) {
                transcriptEl.innerHTML += `<div class="final-transcript">👤 ${text}</div>`;
            } else {
                transcriptEl.innerHTML = transcriptEl.innerHTML.replace(
                    /<div class="interim-transcript">.*?<\/div>$/,
                    ''
                ) + `<div class="interim-transcript">🎤 ${text}</div>`;
            }
            transcriptEl.scrollTop = transcriptEl.scrollHeight;
        }
    }
    
    getStatusText(status) {
        switch (status) {
            case 'listening': return '🎤 En écoute...';
            case 'processing': return '🧠 Traitement en cours...';
            case 'stopped': return '⏹️ Arrêté';
            case 'error': return '❌ Erreur';
            default: return '🎤 Prêt';
        }
    }
    
    getConversationHistory() {
        return this.conversationHistory;
    }
    
    clearHistory() {
        this.conversationHistory = [];
        const transcriptEl = document.getElementById('voice-transcript');
        if (transcriptEl) {
            transcriptEl.innerHTML = '';
        }
    }
}

// Export pour utilisation globale
window.AdvancedVoiceSystem = AdvancedVoiceSystem;
