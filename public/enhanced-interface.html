<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Interface Cerveau Ultra-Avancée</title>

    <!-- Styles de base -->
    <link rel="stylesheet" href="/css/reset.css">
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/chat.css">
    <link rel="stylesheet" href="/css/code-display.css">
    <link rel="stylesheet" href="/css/neural-animations.css">
    <link rel="stylesheet" href="/css/particle-animation.css">
    <link rel="stylesheet" href="/css/memory-guardian.css">
    <link rel="stylesheet" href="/css/memory-sublevels.css">
    <link rel="stylesheet" href="/css/web-search.css">
    <link rel="stylesheet" href="/css/conversation-window.css">
    <link rel="stylesheet" href="/css/image-analyzer.css">
    <link rel="stylesheet" href="/css/neural-visualizations.css">
    <link rel="stylesheet" href="/css/futuristic-dark.css">
    <link rel="stylesheet" href="/css/neural-monitor.css">
    <link rel="stylesheet" href="/css/font-awesome.min.css">
    
    <!-- Bibliothèques Three.js pour visualisations cérébrales 3D avancées -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>

    <!-- Styles futuristes -->
    <link rel="stylesheet" href="/css/enhanced-interface.css">
    <link rel="stylesheet" href="/css/modern-components.css">
    <link rel="stylesheet" href="/css/advanced-visualizations.css">
    <link rel="stylesheet" href="/css/neural-3d.css">
    <link rel="stylesheet" href="/css/vision.css">
    <link rel="stylesheet" href="/css/agent-thoughts.css">
    <link rel="stylesheet" href="/css/unrestricted-generator.css">
    <link rel="stylesheet" href="/css/guardian-controls.css">

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Three.js pour la visualisation 3D -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.min.js"></script>

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">

    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="app-wrapper">
        <!-- Barre latérale unique -->
        <div class="sidebar">
            <!-- En-tête de la barre latérale -->
            <div class="sidebar-header">
                <h3><i class="fas fa-brain"></i> Agent Thermique</h3>
                <button id="sidebar-toggle" class="btn"><i class="fas fa-bars"></i></button>
            </div>

            <!-- Section d'activité neuronale -->
            <div class="neural-activity-sidebar" id="neural-container">
                <!-- Animation de particules injectée ici par JS -->
            </div>

            <!-- Contrôles d'évolution -->
            <div class="evolution-controls">
                <div class="evolution-header">
                    <h3>Évolution de Code</h3>
                    <div id="evolution-engine-status" class="status-indicator disconnected" data-tooltip="Non connecté au moteur d'évolution"></div>
                </div>
                
                <div class="evolution-options">
                    <div class="evolution-option">
                        <label for="auto-evolution-toggle">Auto-évolution:</label>
                        <div class="toggle-switch">
                            <input type="checkbox" id="auto-evolution-toggle">
                            <span class="slider round"></span>
                        </div>
                    </div>
                    
                    <div class="evolution-option">
                        <label for="evolution-mode">Mode:</label>
                        <select id="evolution-mode">
                            <option value="balanced">Équilibré</option>
                            <option value="accelerated">Accéléré</option>
                            <option value="conservative">Conservateur</option>
                            <option value="experimental">Expérimental</option>
                        </select>
                    </div>
                    
                    <div class="evolution-option">
                        <label for="evolution-speed-slider">Vitesse:</label>
                        <input type="range" id="evolution-speed-slider" min="1" max="10" value="5">
                        <span id="evolution-speed-value">5</span>
                    </div>
                    
                    <div class="evolution-info">
                        <span>Génération: <span id="evolution-generation-counter">1</span></span>
                    </div>
                </div>
            </div>
            
            <!-- Indicateurs d'activité -->
            <div class="sidebar-section">
                <h4><i class="fas fa-chart-line"></i> Indicateurs d'activité</h4>
                <div class="activity-indicators">
                    <div class="activity-indicator oral-indicator">
                        <div class="indicator-name"><i class="fas fa-comment"></i> Activité orale</div>
                        <div class="indicator-value active" id="oral-activity-value">Actif</div>
                    </div>

                    <div class="activity-indicator written-indicator">
                        <div class="indicator-name"><i class="fas fa-pen"></i> Info écrite</div>
                        <div class="indicator-value active" id="written-activity-value">Actif</div>
                    </div>

                    <div class="activity-indicator accelerator-indicator">
                        <div class="indicator-name"><i class="fas fa-bolt"></i> Accélérateur</div>
                        <div class="indicator-value active" id="accelerator-status-value">Actif</div>
                    </div>

                    <div class="activity-indicator visual-indicator">
                        <div class="indicator-name"><i class="fas fa-eye"></i> Info visuelle</div>
                        <div class="indicator-value active" id="visual-activity-value">Actif</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="main-content">
            <div class="app-container">
                <!-- En-tête principal -->
                <header>
                    <div class="logo">
                        <i class="fas fa-microchip"></i>
                        <h1>Agent à Mémoire Thermique</h1>
                    </div>
                    <button onclick="goHome()" style="padding: 10px 20px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 20px; cursor: pointer; font-weight: 600; margin-right: 20px;">
                        🏠 Accueil Spectaculaire
                    </button>
                    <div id="app-status" class="connected">Connecté</div>
                </header>

                <!-- Navigation principale -->
                <nav>
                    <ul>
                        <li><a href="#dashboard" class="active"><i class="fas fa-tachometer-alt"></i> Tableau de bord</a></li>
                        <li><a href="#memory"><i class="fas fa-memory"></i> Mémoire</a></li>
                        <li><a href="#neural"><i class="fas fa-brain"></i> Activité neuronale</a></li>
                        <li><a href="#neural3d"><i class="fas fa-cube"></i> Visualisation 3D</a></li>
                        <li><a href="#stats"><i class="fas fa-chart-bar"></i> Statistiques</a></li>
                        <li><a href="#kyber"><i class="fas fa-bolt"></i> Accélérateur Kyber</a></li>
                        <li><a href="#vision"><i class="fas fa-eye"></i> Vision</a></li>
                        <li><a href="#personality"><i class="fas fa-user"></i> Personnalité</a></li>
                        <li><a href="#chat"><i class="fas fa-comment-dots"></i> Conversation</a></li>
                        <li><a href="#lab"><i class="fas fa-flask"></i> Laboratoire</a></li>
                        <li><a href="#knowledge-transfer"><i class="fas fa-graduation-cap"></i> Transmission</a></li>
                        <li><a href="#settings"><i class="fas fa-cog"></i> Paramètres</a></li>
                    </ul>
                </nav>

                <!-- Contenu principal -->
                <main>
                    <!-- Section Tableau de bord -->
                    <section id="dashboard" class="active">
                        <h2><i class="fas fa-tachometer-alt"></i> Tableau de bord</h2>

                        <div class="grid-3">
                            <!-- Carte d'état du système -->
                            <div class="card">
                                <h3><i class="fas fa-server"></i> État du système</h3>
                                <div class="kyber-stats-container">
                                    <div class="kyber-stat-card">
                                        <div class="kyber-stat-icon">
                                            <i class="fas fa-microchip"></i>
                                        </div>
                                        <div class="kyber-stat-title">Accélérateur</div>
                                        <div class="kyber-stat-value success">Actif</div>
                                        <div class="kyber-stat-subtitle">Mode optimal</div>
                                    </div>

                                    <div class="kyber-stat-card">
                                        <div class="kyber-stat-icon">
                                            <i class="fas fa-memory"></i>
                                        </div>
                                        <div class="kyber-stat-title">Mémoire</div>
                                        <div class="kyber-stat-value info">14 items</div>
                                        <div class="kyber-stat-subtitle">5 niveaux actifs</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Carte d'activité récente -->
                            <div class="card">
                                <h3><i class="fas fa-history"></i> Activité récente</h3>
                                <div class="activity-timeline">
                                    <div class="timeline-item">
                                        <div class="timeline-icon success">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-title">Mémoire mise à jour</div>
                                            <div class="timeline-time">Il y a 2 minutes</div>
                                        </div>
                                    </div>

                                    <div class="timeline-item">
                                        <div class="timeline-icon info">
                                            <i class="fas fa-sync"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-title">Circulation des mémoires</div>
                                            <div class="timeline-time">Il y a 5 minutes</div>
                                        </div>
                                    </div>

                                    <div class="timeline-item">
                                        <div class="timeline-icon warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-title">Pic d'activité neuronale</div>
                                            <div class="timeline-time">Il y a 12 minutes</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Carte de performance -->
                            <div class="card">
                                <h3><i class="fas fa-tachometer-alt"></i> Performance</h3>
                                <div class="gauge-container">
                                    <div class="gauge" data-value="78">
                                        <div class="gauge-background"></div>
                                        <div class="gauge-fill"></div>
                                        <div class="gauge-center">
                                            <div class="gauge-value">78%</div>
                                            <div class="gauge-label">Efficacité</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Visualisation neuronale -->
                        <div class="card">
                            <h3><i class="fas fa-brain"></i> Activité neuronale en temps réel</h3>
                            <div class="neural-visualization"></div>
                        </div>

                        <!-- Statistiques Kyber -->
                        <div class="card">
                            <h3><i class="fas fa-bolt"></i> Accélérateur Kyber</h3>
                            <div class="kyber-stats-container">
                                <div class="kyber-stat-card">
                                    <div class="kyber-stat-icon">
                                        <i class="fas fa-tachometer-alt"></i>
                                    </div>
                                    <div class="kyber-stat-title">Facteur d'accélération</div>
                                    <div class="kyber-stat-value accent" id="kyber-acceleration">1.75</div>
                                    <div class="kyber-stat-subtitle">Multiplicateur de performance</div>
                                </div>

                                <div class="kyber-stat-card">
                                    <div class="kyber-stat-icon">
                                        <i class="fas fa-microchip"></i>
                                    </div>
                                    <div class="kyber-stat-title">Opérations/seconde</div>
                                    <div class="kyber-stat-value info" id="kyber-ops">1,245,678</div>
                                    <div class="kyber-stat-subtitle">Traitement en temps réel</div>
                                </div>

                                <div class="kyber-stat-card">
                                    <div class="kyber-stat-icon">
                                        <i class="fas fa-temperature-high"></i>
                                    </div>
                                    <div class="kyber-stat-title">Température</div>
                                    <div class="kyber-stat-value success" id="kyber-temperature">42.3°C</div>
                                    <div class="kyber-stat-subtitle">Stabilité thermique</div>
                                </div>

                                <div class="kyber-stat-card">
                                    <div class="kyber-stat-icon">
                                        <i class="fas fa-server"></i>
                                    </div>
                                    <div class="kyber-stat-title">Utilisation</div>
                                    <div class="kyber-stat-value warning" id="kyber-utilization">68.5%</div>
                                    <div class="kyber-stat-subtitle">Capacité utilisée</div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Section Activité neuronale -->
                    <section id="neural">
                        <h2><i class="fas fa-brain"></i> Activité Neuronale</h2>
                        
                        <!-- Visualisations neuronales avancées (ajout direct) -->
                        <div class="neural-enhanced-visualizations">
                            <!-- Visualisation cérébrale avancée -->
                            <div class="card neural-advanced-card">
                                <h3><i class="fas fa-brain"></i> Visualisation cérébrale avancée</h3>
                                
                                <div class="brain-visualization-container">
                                    <div id="brain-3d-model" class="brain-3d-container"></div>
                                    
                                    <div class="brain-controls">
                                        <button id="rotate-toggle" class="brain-control-btn"><i class="fas fa-sync-alt"></i> Rotation</button>
                                        <button id="highlight-toggle" class="brain-control-btn"><i class="fas fa-lightbulb"></i> Zones actives</button>
                                        <button id="reset-view" class="brain-control-btn"><i class="fas fa-undo"></i> Réinitialiser</button>
                                    </div>
                                </div>
                                
                                <div class="neural-activity-stats">
                                    <div class="neural-stat">
                                        <div class="stat-label">Neurones actifs</div>
                                        <div class="stat-value">1.28M</div>
                                        <div class="stat-chart" id="neurons-chart"></div>
                                    </div>
                                    <div class="neural-stat">
                                        <div class="stat-label">Connexions</div>
                                        <div class="stat-value">8.64B</div>
                                        <div class="stat-chart" id="connections-chart"></div>
                                    </div>
                                    <div class="neural-stat">
                                        <div class="stat-label">Activation</div>
                                        <div class="stat-value">48.3Hz</div>
                                        <div class="stat-chart" id="frequency-chart"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Visualisation en temps réel de l'activité synaptique -->
                            <div class="grid-2">
                                <div class="card">
                                    <h3><i class="fas fa-bolt"></i> Activité synaptique</h3>
                                    <div class="synaptic-visual-container">
                                        <div id="synaptic-real-time" class="synaptic-visual"></div>
                                        <div class="real-time-stats">
                                            <div class="rt-stat">
                                                <span class="rt-value">485</span>
                                                <span class="rt-unit">Hz</span>
                                                <span class="rt-label">Taux d'activation</span>
                                            </div>
                                            <div class="rt-stat">
                                                <span class="rt-value">23</span>
                                                <span class="rt-unit">voies</span>
                                                <span class="rt-label">Voies actives</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card">
                                    <h3><i class="fas fa-temperature-high"></i> Température neuronale</h3>
                                    <div class="thermal-container">
                                        <div id="thermal-heatmap" class="thermal-visual"></div>
                                        <div class="thermal-legend">
                                            <div class="thermal-gradient"></div>
                                            <div class="thermal-labels">
                                                <span>25°C</span>
                                                <span>42°C</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <!-- Visualisation principale du cerveau neuronal -->
                            <div class="card">
                                <h3><i class="fas fa-project-diagram"></i> Visualisation du réseau neuronal</h3>
                                <div class="neural-advanced-container">
                                    <div id="advanced-neural-network" class="neural-visualization-3d"></div>
                                    <div class="neural-controls">
                                        <button id="toggle-rotation" class="btn-pill accent"><i class="fas fa-sync-alt"></i> Rotation</button>
                                        <button id="zoom-in" class="btn-pill"><i class="fas fa-search-plus"></i></button>
                                        <button id="zoom-out" class="btn-pill"><i class="fas fa-search-minus"></i></button>
                                        <div class="neuronal-activity-indicator pulse-anim">
                                            <div class="activity-level high"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Carte d'activation des zones cérébrales -->
                            <div class="card">
                                <h3><i class="fas fa-fire"></i> Carte d'activation par zone</h3>
                                <div class="brain-heatmap-container">
                                    <div id="brain-heatmap" class="brain-heat-visualization"></div>
                                    <div class="heatmap-legend">
                                        <div class="legend-item">
                                            <div class="color-indicator high"></div>
                                            <span>Forte activation</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="color-indicator medium"></div>
                                            <span>Activation moyenne</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="color-indicator low"></div>
                                            <span>Faible activation</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Statistiques d'activité neuronale -->
                        <div class="card neural-stats-card">
                            <h3><i class="fas fa-chart-line"></i> Métriques d'activation neuronale</h3>
                            <div class="neuron-metrics-grid">
                                <div class="metric-card">
                                    <div class="metric-title">Neurones actifs</div>
                                    <div class="metric-value" id="active-neurons">1.28M</div>
                                    <div class="metric-chart" id="neurons-chart"></div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-title">Connexions synaptiques</div>
                                    <div class="metric-value" id="synaptic-connections">8.64B</div>
                                    <div class="metric-chart" id="connections-chart"></div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-title">Potentiel d'action</div>
                                    <div class="metric-value" id="action-potential">64.7mV</div>
                                    <div class="metric-chart" id="potential-chart"></div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-title">Fréquence d'activation</div>
                                    <div class="metric-value" id="firing-rate">48.3Hz</div>
                                    <div class="metric-chart" id="frequency-chart"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Activité de la mémoire thermique -->
                        <div class="card">
                            <h3><i class="fas fa-temperature-high"></i> Activité de la mémoire thermique</h3>
                            <div class="memory-thermal-container">
                                <div id="thermal-memory-visualization" class="thermal-memory-visual"></div>
                                <div class="thermal-metrics">
                                    <div class="thermal-metric">
                                        <div class="thermal-label">Niveau 1 (Court terme)</div>
                                        <div class="thermal-value">42.3°C</div>
                                        <div class="thermal-bar">
                                            <div class="thermal-fill high" style="width: 78%"></div>
                                        </div>
                                    </div>
                                    <div class="thermal-metric">
                                        <div class="thermal-label">Niveau 2 (Moyen terme)</div>
                                        <div class="thermal-value">36.8°C</div>
                                        <div class="thermal-bar">
                                            <div class="thermal-fill medium" style="width: 62%"></div>
                                        </div>
                                    </div>
                                    <div class="thermal-metric">
                                        <div class="thermal-label">Niveau 3 (Long terme)</div>
                                        <div class="thermal-value">28.5°C</div>
                                        <div class="thermal-bar">
                                            <div class="thermal-fill low" style="width: 45%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-chart-line"></i> Activité par région</h3>
                                <div class="chart-container">
                                    <canvas id="neural-regions-chart"></canvas>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="badge primary"><i class="fas fa-brain"></i> 6 régions actives</div>
                                    <div class="badge success"><i class="fas fa-tachometer-alt"></i> 120 impulsions/sec</div>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-wave-square"></i> Fréquences d'activité</h3>
                                <div class="chart-container">
                                    <canvas id="neural-frequencies-chart"></canvas>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="badge info"><i class="fas fa-wave-square"></i> Fréquence dominante: 12.4 Hz</div>
                                    <div class="badge warning"><i class="fas fa-chart-line"></i> Amplitude: 0.85</div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-sliders-h"></i> Contrôles de visualisation</h3>
                            <div class="grid-3">
                                <div class="control-card">
                                    <h4>Densité des particules</h4>
                                    <div class="range-slider">
                                        <input type="range" min="100" max="5000" step="100" value="2000" class="slider" id="particle-density-slider">
                                        <div class="range-value">2000</div>
                                    </div>
                                </div>

                                <div class="control-card">
                                    <h4>Vitesse d'animation</h4>
                                    <div class="range-slider">
                                        <input type="range" min="0.1" max="2" step="0.1" value="1" class="slider" id="animation-speed-slider">
                                        <div class="range-value">1.0x</div>
                                    </div>
                                </div>

                                <div class="control-card">
                                    <h4>Intensité des couleurs</h4>
                                    <div class="range-slider">
                                        <input type="range" min="0.1" max="1" step="0.1" value="0.8" class="slider" id="color-intensity-slider">
                                        <div class="range-value">0.8</div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end gap-md">
                                <button class="btn warning"><i class="fas fa-redo"></i> Réinitialiser</button>
                                <button class="btn primary"><i class="fas fa-save"></i> Appliquer</button>
                            </div>
                        </div>
                    </section>

                    <!-- Section Activité Cérébrale Avancée - Pour l'injecteur d'activité cérébrale -->
                    <section id="brain-activity">
                        <h2><i class="fas fa-brain"></i> Activité Cérébrale Avancée</h2>
                        
                        <div class="card">
                            <h3><i class="fas fa-project-diagram"></i> Visualisation Synaptique Haute Précision</h3>
                            <div class="brain-hd-container">
                                <div class="brain-model-container" id="brain-model-3d">
                                    <!-- Le modèle 3D du cerveau sera injecté ici -->

                            <div class="neural-3d-container" id="neural-3d-main">
                                <!-- La visualisation 3D sera injectée ici par JavaScript -->

                                <div class="neural-3d-info">
                                    <h4>Réseau neuronal</h4>
                                    <p>Visualisation 3D interactive du réseau neuronal de l'agent</p>
                                    <div class="neural-3d-stats">
                                        <div class="neural-3d-stat">
                                            <span class="neural-3d-stat-value">150</span>
                                            <span class="neural-3d-stat-label">Nœuds</span>
                                        </div>
                                        <div class="neural-3d-stat">
                                            <span class="neural-3d-stat-value">320</span>
                                            <span class="neural-3d-stat-label">Connexions</span>
                                        </div>
                                        <div class="neural-3d-stat">
                                            <span class="neural-3d-stat-value">24</span>
                                            <span class="neural-3d-stat-label">Actifs</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="neural-3d-legend">
                                    <div class="neural-3d-legend-item">
                                        <div class="neural-3d-legend-color node"></div>
                                        <span class="neural-3d-legend-label">Nœud neuronal</span>
                                    </div>
                                    <div class="neural-3d-legend-item">
                                        <div class="neural-3d-legend-color active-node"></div>
                                        <span class="neural-3d-legend-label">Nœud actif</span>
                                    </div>
                                    <div class="neural-3d-legend-item">
                                        <div class="neural-3d-legend-color connection"></div>
                                        <span class="neural-3d-legend-label">Connexion synaptique</span>
                                    </div>
                                    <div class="neural-3d-legend-item">
                                        <div class="neural-3d-legend-color active-connection"></div>
                                        <span class="neural-3d-legend-label">Connexion active</span>
                                    </div>
                                </div>

                                <div class="neural-3d-controls">
                                    <div class="neural-3d-control" id="neural-3d-rotate-toggle">
                                        <i class="fas fa-sync-alt"></i>
                                    </div>
                                    <div class="neural-3d-control" id="neural-3d-activity-increase">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                    <div class="neural-3d-control" id="neural-3d-activity-decrease">
                                        <i class="fas fa-minus"></i>
                                    </div>
                                    <div class="neural-3d-control" id="neural-3d-fullscreen-toggle">
                                        <i class="fas fa-expand"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="badge info"><i class="fas fa-info-circle"></i> Cliquez et faites glisser pour faire pivoter</div>
                                <div class="badge accent"><i class="fas fa-mouse-pointer"></i> Cliquez sur un nœud pour l'activer</div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-sliders-h"></i> Paramètres de visualisation</h3>

                                <div class="control-card">
                                    <h4>Niveau d'activité</h4>
                                    <div class="range-slider">
                                        <input type="range" min="0" max="1" step="0.1" value="0.5" class="slider" id="activity-level-slider">
                                        <div class="range-value">50%</div>
                                    </div>
                                </div>

                                <div class="control-card">
                                    <h4>Vitesse de rotation</h4>
                                    <div class="range-slider">
                                        <input type="range" min="0" max="2" step="0.1" value="0.5" class="slider" id="rotation-speed-slider">
                                        <div class="range-value">0.5x</div>
                                    </div>
                                </div>

                                <div class="control-card">
                                    <h4>Taille des nœuds</h4>
                                    <div class="range-slider">
                                        <input type="range" min="1" max="5" step="0.5" value="2.5" class="slider" id="node-size-slider">
                                        <div class="range-value">2.5</div>
                                    </div>
                                </div>

                                <div class="flex justify-end">
                                    <button class="btn primary" id="apply-3d-settings"><i class="fas fa-check"></i> Appliquer</button>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-info-circle"></i> À propos de la visualisation 3D</h3>

                                <p>Cette visualisation 3D interactive représente le réseau neuronal de l'Agent à Mémoire Thermique. Chaque sphère représente un nœud neuronal, et chaque ligne représente une connexion synaptique entre deux nœuds.</p>

                                <p>Les nœuds actifs sont mis en évidence en orange et augmentent temporairement de taille. L'activation se propage à travers le réseau, simulant la façon dont l'information circule dans le cerveau de l'agent.</p>

                                <div class="flex justify-between items-center">
                                    <div class="badge primary"><i class="fas fa-cube"></i> Rendu WebGL</div>
                                    <div class="badge success"><i class="fas fa-tachometer-alt"></i> 60 FPS</div>
                                    <div class="badge warning"><i class="fas fa-project-diagram"></i> Topologie dynamique</div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Section Mémoire -->
                    <section id="memory">
                        <h2><i class="fas fa-memory"></i> Mémoire Thermique</h2>

                        <div class="card">
                            <h3><i class="fas fa-layer-group"></i> Distribution des niveaux de mémoire</h3>

                            <div class="card-description">
                                <p>L'agent peut créer des sous-niveaux entre les niveaux principaux pour mieux organiser sa mémoire.</p>
                            </div>

                            <div class="memory-level-indicator">
                                <div class="memory-level-name">Niveau 1 (Primaire)</div>
                                <div class="memory-level-bar">
                                    <div class="memory-level-fill" data-level="1" style="width: 60%;"></div>
                                </div>
                                <div class="memory-level-value" data-level="1">6</div>
                            </div>

                            <div class="memory-level-indicator">
                                <div class="memory-level-name">Niveau 2 (Secondaire)</div>
                                <div class="memory-level-bar">
                                    <div class="memory-level-fill" data-level="2" style="width: 60%;"></div>
                                </div>
                                <div class="memory-level-value" data-level="2">6</div>
                            </div>

                            <div class="memory-level-indicator">
                                <div class="memory-level-name">Niveau 3 (Tertiaire)</div>
                                <div class="memory-level-bar">
                                    <div class="memory-level-fill" data-level="3" style="width: 20%;"></div>
                                </div>
                                <div class="memory-level-value" data-level="3">2</div>
                            </div>

                            <div class="memory-level-indicator">
                                <div class="memory-level-name">Niveau 4 (Quaternaire)</div>
                                <div class="memory-level-bar">
                                    <div class="memory-level-fill" data-level="4" style="width: 0%;"></div>
                                </div>
                                <div class="memory-level-value" data-level="4">0</div>
                            </div>

                            <div class="memory-level-indicator">
                                <div class="memory-level-name">Niveau 5 (Quinary)</div>
                                <div class="memory-level-bar">
                                    <div class="memory-level-fill" data-level="5" style="width: 0%;"></div>
                                </div>
                                <div class="memory-level-value" data-level="5">0</div>
                            </div>

                            <div class="memory-level-indicator">
                                <div class="memory-level-name">Niveau 6 (Créatif)</div>
                                <div class="memory-level-bar">
                                    <div class="memory-level-fill" data-level="6" style="width: 10%;"></div>
                                </div>
                                <div class="memory-level-value" data-level="6">1</div>
                                <div class="level-description">Vision, Parole, Rêve, Imagination, Personnalisation</div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-temperature-high"></i> Température par niveau</h3>
                                <div class="chart-container">
                                    <canvas id="temperature-level-chart"></canvas>
                                </div>
                                <div class="temperature-gradient"></div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-chart-pie"></i> Distribution d'activité</h3>
                                <div class="chart-container">
                                    <canvas id="activity-distribution-chart"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-chart-line"></i> Évolution de la mémoire</h3>
                            <div class="chart-container large">
                                <canvas id="memory-evolution-chart"></canvas>
                            </div>
                        </div>

                        <div class="flex justify-between">
                            <button id="circulate-memory-btn" class="btn primary"><i class="fas fa-sync"></i> Faire circuler les mémoires</button>
                            <button id="clean-memory-btn" class="btn warning"><i class="fas fa-broom"></i> Nettoyer la mémoire</button>
                        </div>
                        <div class="memory-guardian-controls mt-3">
                            <div class="flex justify-between align-center">
                                <div class="guardian-status">
                                    <span class="status-label">Gardien de mémoire:</span>
                                    <span id="guardian-status" class="status-value active">Actif</span>
                                </div>
                                <button id="toggle-guardian-btn" class="btn danger"><i class="fas fa-shield-alt"></i> Désactiver le gardien</button>
                            </div>
                            <div class="memory-test-controls mt-2">
                                <button id="test-memory-btn" class="btn accent"><i class="fas fa-vial"></i> Tester la mémoire</button>
                                <div id="memory-test-results" class="memory-test-results mt-2 hidden">
                                    <div class="test-status">Test en cours...</div>
                                    <div class="test-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-layer-group"></i> Sous-niveaux de mémoire</h3>
                            <p class="card-description">
                                Les sous-niveaux permettent à l'agent d'organiser sa mémoire de manière plus précise entre les niveaux principaux.
                            </p>

                            <div id="memory-levels-container" class="memory-levels-container">
                                <!-- Les niveaux et sous-niveaux seront ajoutés ici dynamiquement -->
                            </div>

                            <div class="flex justify-between mt-20">
                                <button class="btn info" id="refresh-sublevels-btn"><i class="fas fa-sync"></i> Rafraîchir</button>
                                <button class="btn primary" id="create-sublevel-btn"><i class="fas fa-plus"></i> Créer un sous-niveau</button>
                            </div>
                        </div>
                    </section>

                    <!-- Section Statistiques -->
                    <section id="stats">
                        <h2><i class="fas fa-chart-bar"></i> Statistiques et Analyse</h2>

                        <div class="grid-3">
                            <div class="card">
                                <h3><i class="fas fa-brain"></i> Activité cérébrale</h3>
                                <div class="chart-container">
                                    <canvas id="brain-activity-chart"></canvas>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="badge success"><i class="fas fa-check-circle"></i> Synchronisé</div>
                                    <div class="badge warning"><i class="fas fa-chart-line"></i> Fréquence: 12.4 Hz</div>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-tachometer-alt"></i> Performance</h3>
                                <div class="gauge-container">
                                    <div class="gauge" data-value="78">
                                        <div class="gauge-background"></div>
                                        <div class="gauge-fill"></div>
                                        <div class="gauge-center">
                                            <div class="gauge-value">78%</div>
                                            <div class="gauge-label">Efficacité</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex justify-center">
                                    <div class="badge info"><i class="fas fa-bolt"></i> Score: 78/100</div>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-fire"></i> Charge système</h3>
                                <div class="chart-container">
                                    <canvas id="system-load-chart"></canvas>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="badge primary"><i class="fas fa-microchip"></i> CPU: 42%</div>
                                    <div class="badge accent"><i class="fas fa-memory"></i> RAM: 1.2 GB</div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-project-diagram"></i> Connexions synaptiques</h3>
                            <div class="neural-visualization large"></div>
                            <div class="flex justify-between items-center">
                                <div class="badge info"><i class="fas fa-network-wired"></i> 1,245 connexions actives</div>
                                <div class="badge success"><i class="fas fa-sync-alt"></i> 120 impulsions/sec</div>
                            </div>
                        </div>
                    </section>

                    <!-- Section Kyber -->
                    <section id="kyber">
                        <h2><i class="fas fa-bolt"></i> Accélérateur Kyber</h2>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-tachometer-alt"></i> Performances</h3>
                                <div class="kyber-stats-container">
                                    <div class="kyber-stat-card">
                                        <div class="kyber-stat-icon">
                                            <i class="fas fa-tachometer-alt"></i>
                                        </div>
                                        <div class="kyber-stat-title">Facteur d'accélération</div>
                                        <div class="kyber-stat-value accent" id="kyber-acceleration">1.75</div>
                                        <div class="kyber-stat-subtitle">Multiplicateur de performance</div>
                                    </div>

                                    <div class="kyber-stat-card">
                                        <div class="kyber-stat-icon">
                                            <i class="fas fa-microchip"></i>
                                        </div>
                                        <div class="kyber-stat-title">Opérations/seconde</div>
                                        <div class="kyber-stat-value info" id="kyber-ops">1,245,678</div>
                                        <div class="kyber-stat-subtitle">Traitement en temps réel</div>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-thermometer-half"></i> État thermique</h3>
                                <div class="kyber-stats-container">
                                    <div class="kyber-stat-card">
                                        <div class="kyber-stat-icon">
                                            <i class="fas fa-temperature-high"></i>
                                        </div>
                                        <div class="kyber-stat-title">Température</div>
                                        <div class="kyber-stat-value success" id="kyber-temperature">42.3°C</div>
                                        <div class="kyber-stat-subtitle">Stabilité thermique</div>
                                    </div>

                                    <div class="kyber-stat-card">
                                        <div class="kyber-stat-icon">
                                            <i class="fas fa-server"></i>
                                        </div>
                                        <div class="kyber-stat-title">Utilisation</div>
                                        <div class="kyber-stat-value warning" id="kyber-utilization">68.5%</div>
                                        <div class="kyber-stat-subtitle">Capacité utilisée</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-chart-line"></i> Évolution des performances</h3>
                            <div class="chart-container large">
                                <canvas id="kyber-performance-chart"></canvas>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-sliders-h"></i> Contrôles avancés</h3>
                            <div class="grid-3">
                                <div class="control-card">
                                    <h4>Mode d'opération</h4>
                                    <select class="input">
                                        <option>Auto-régulation</option>
                                        <option>Performance maximale</option>
                                        <option>Économie d'énergie</option>
                                        <option>Équilibré</option>
                                    </select>
                                </div>

                                <div class="control-card">
                                    <h4>Facteur d'accélération</h4>
                                    <div class="range-slider">
                                        <input type="range" min="1" max="3" step="0.1" value="1.75" class="slider" id="acceleration-slider">
                                        <div class="range-value">1.75x</div>
                                    </div>
                                </div>

                                <div class="control-card">
                                    <h4>Limite thermique</h4>
                                    <div class="range-slider">
                                        <input type="range" min="40" max="80" step="1" value="65" class="slider" id="thermal-slider">
                                        <div class="range-value">65°C</div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end gap-md">
                                <button class="btn warning"><i class="fas fa-redo"></i> Réinitialiser</button>
                                <button class="btn primary"><i class="fas fa-save"></i> Appliquer</button>
                            </div>
                        </div>
                    </section>

                    <!-- Section Vision -->
                    <section id="vision">
                        <h2><i class="fas fa-eye"></i> Vision</h2>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-camera"></i> Caméra en direct</h3>

                                <div class="camera-container">
                                    <div class="camera-feed" id="camera-feed">
                                        <div class="camera-placeholder">
                                            <i class="fas fa-camera"></i>
                                            <p>La caméra n'est pas activée</p>
                                        </div>
                                        <video id="camera-video" autoplay playsinline style="display: none;"></video>
                                    </div>

                                    <div class="camera-controls">
                                        <button class="btn primary" id="start-camera"><i class="fas fa-play"></i> Activer la caméra</button>
                                        <button class="btn warning" id="stop-camera" disabled><i class="fas fa-stop"></i> Désactiver</button>
                                        <button class="btn info" id="take-snapshot" disabled><i class="fas fa-camera"></i> Capturer</button>
                                    </div>
                                </div>

                                <div class="camera-status">
                                    <div class="status-indicator">
                                        <span class="status-dot offline"></span>
                                        <span class="status-text">Caméra inactive</span>
                                    </div>
                                    <div class="camera-resolution">Résolution: --</div>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-eye"></i> Analyse visuelle</h3>

                                <div class="vision-analysis" id="vision-analysis">
                                    <div class="analysis-placeholder">
                                        <i class="fas fa-search"></i>
                                        <p>Activez la caméra pour commencer l'analyse</p>
                                    </div>

                                    <div class="analysis-results" style="display: none;">
                                        <div class="analysis-section">
                                            <h4><i class="fas fa-tag"></i> Objets détectés</h4>
                                            <div class="detected-objects" id="detected-objects">
                                                <!-- Les objets détectés seront ajoutés ici -->
                                            </div>
                                        </div>

                                        <div class="analysis-section">
                                            <h4><i class="fas fa-font"></i> Texte détecté</h4>
                                            <div class="detected-text" id="detected-text">
                                                <!-- Le texte détecté sera ajouté ici -->
                                            </div>
                                        </div>

                                        <div class="analysis-section">
                                            <h4><i class="fas fa-palette"></i> Couleurs dominantes</h4>
                                            <div class="color-palette" id="color-palette">
                                                <!-- Les couleurs dominantes seront ajoutées ici -->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="analysis-controls">
                                    <button class="btn primary" id="analyze-image" disabled><i class="fas fa-search"></i> Analyser</button>
                                    <button class="btn info" id="describe-scene" disabled><i class="fas fa-comment-alt"></i> Décrire la scène</button>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-universal-access"></i> Assistance visuelle</h3>

                            <div class="assistance-container">
                                <div class="assistance-modes">
                                    <div class="assistance-mode">
                                        <input type="radio" id="mode-description" name="assistance-mode" value="description" checked>
                                        <label for="mode-description">
                                            <i class="fas fa-comment-alt"></i>
                                            <span>Description de scène</span>
                                        </label>
                                    </div>

                                    <div class="assistance-mode">
                                        <input type="radio" id="mode-navigation" name="assistance-mode" value="navigation">
                                        <label for="mode-navigation">
                                            <i class="fas fa-directions"></i>
                                            <span>Aide à la navigation</span>
                                        </label>
                                    </div>

                                    <div class="assistance-mode">
                                        <input type="radio" id="mode-reading" name="assistance-mode" value="reading">
                                        <label for="mode-reading">
                                            <i class="fas fa-book-reader"></i>
                                            <span>Lecture de texte</span>
                                        </label>
                                    </div>

                                    <div class="assistance-mode">
                                        <input type="radio" id="mode-recognition" name="assistance-mode" value="recognition">
                                        <label for="mode-recognition">
                                            <i class="fas fa-id-card"></i>
                                            <span>Reconnaissance de personnes</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="assistance-output">
                                    <h4>Sortie vocale</h4>
                                    <div class="assistance-text" id="assistance-text">
                                        <p class="assistance-placeholder">L'assistance vocale s'affichera ici lorsque la caméra sera activée.</p>
                                    </div>

                                    <div class="audio-controls">
                                        <div class="volume-control">
                                            <label for="volume-slider">Volume</label>
                                            <input type="range" min="0" max="1" step="0.1" value="0.8" class="slider" id="volume-slider">
                                        </div>

                                        <div class="speed-control">
                                            <label for="speed-slider">Vitesse</label>
                                            <input type="range" min="0.5" max="2" step="0.1" value="1" class="slider" id="speed-slider">
                                        </div>

                                        <button class="btn accent" id="speak-assistance" disabled><i class="fas fa-volume-up"></i> Lire</button>
                                    </div>
                                </div>
                            </div>

                            <div class="assistance-settings">
                                <h4><i class="fas fa-sliders-h"></i> Paramètres d'assistance</h4>

                                <div class="settings-grid">
                                    <div class="setting-item">
                                        <label for="detail-level">Niveau de détail</label>
                                        <select id="detail-level" class="input">
                                            <option value="low">Minimal</option>
                                            <option value="medium" selected>Standard</option>
                                            <option value="high">Détaillé</option>
                                            <option value="very-high">Très détaillé</option>
                                        </select>
                                    </div>

                                    <div class="setting-item">
                                        <label for="update-frequency">Fréquence de mise à jour</label>
                                        <select id="update-frequency" class="input">
                                            <option value="low">Basse (économie d'énergie)</option>
                                            <option value="medium" selected>Moyenne</option>
                                            <option value="high">Élevée</option>
                                            <option value="continuous">Continue</option>
                                        </select>
                                    </div>

                                    <div class="setting-item">
                                        <label for="auto-assist-toggle">Assistance automatique</label>
                                        <label class="switch">
                                            <input type="checkbox" id="auto-assist-toggle" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>

                                    <div class="setting-item">
                                        <label for="priority-toggle">Priorité aux dangers</label>
                                        <label class="switch">
                                            <input type="checkbox" id="priority-toggle" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-history"></i> Captures récentes</h3>

                                <div class="snapshots-container" id="snapshots-container">
                                    <div class="snapshots-placeholder">
                                        <i class="fas fa-images"></i>
                                        <p>Aucune capture récente</p>
                                    </div>

                                    <div class="snapshots-grid" id="snapshots-grid" style="display: none;">
                                        <!-- Les captures récentes seront ajoutées ici -->
                                    </div>
                                </div>

                                <div class="flex justify-end">
                                    <button class="btn warning" id="clear-snapshots" disabled><i class="fas fa-trash"></i> Effacer</button>
                                    <button class="btn primary" id="save-snapshots" disabled><i class="fas fa-download"></i> Sauvegarder</button>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-chart-pie"></i> Statistiques de vision</h3>

                                <div class="vision-stats">
                                    <div class="vision-stat-card">
                                        <div class="vision-stat-icon">
                                            <i class="fas fa-microchip"></i>
                                        </div>
                                        <div class="vision-stat-title">Temps de traitement</div>
                                        <div class="vision-stat-value" id="processing-time">-- ms</div>
                                        <div class="vision-stat-subtitle">Moyenne par image</div>
                                    </div>

                                    <div class="vision-stat-card">
                                        <div class="vision-stat-icon">
                                            <i class="fas fa-bullseye"></i>
                                        </div>
                                        <div class="vision-stat-title">Précision</div>
                                        <div class="vision-stat-value" id="detection-accuracy">--%</div>
                                        <div class="vision-stat-subtitle">Détection d'objets</div>
                                    </div>

                                    <div class="vision-stat-card">
                                        <div class="vision-stat-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="vision-stat-title">Temps d'utilisation</div>
                                        <div class="vision-stat-value" id="usage-time">00:00</div>
                                        <div class="vision-stat-subtitle">Session actuelle</div>
                                    </div>
                                </div>

                                <div class="chart-container">
                                    <canvas id="vision-performance-chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Section Laboratoire -->
                    <section id="lab">
                        <h2><i class="fas fa-flask"></i> Laboratoire</h2>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-code"></i> Laboratoire de Code</h3>
                                <p>Explorez, testez et développez du code dans un environnement sécurisé.</p>
                                <div class="code-lab-preview">
                                    <div class="code-editor-preview">
                                        <pre><code>function helloWorld() {
  console.log("Hello, world!");
  return "Bienvenue au laboratoire de code";
}</code></pre>
                                    </div>
                                </div>
                                <div class="flex justify-center">
                                    <button class="btn primary" id="open-code-lab"><i class="fas fa-external-link-alt"></i> Ouvrir le laboratoire de code</button>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-magic"></i> Générateur de Contenu</h3>
                                <p>Générez des images, vidéos et modèles 3D sans restrictions.</p>

                                <div class="generator-tabs">
                                    <div class="generator-tab active" data-tab="images"><i class="fas fa-image"></i> Images</div>
                                    <div class="generator-tab" data-tab="videos"><i class="fas fa-video"></i> Vidéos</div>
                                    <div class="generator-tab" data-tab="model3d"><i class="fas fa-cube"></i> Modèles 3D</div>
                                </div>

                                <div class="generator-content">
                                    <!-- Section Images -->
                                    <div class="generator-section active" id="images-section">
                                        <div class="generator-form">
                                            <div class="form-group">
                                                <label for="image-prompt">Description</label>
                                                <textarea id="image-prompt" class="input" rows="3" placeholder="Décrivez l'image que vous souhaitez générer..."></textarea>
                                            </div>
                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="image-count">Nombre</label>
                                                    <select id="image-count" class="input">
                                                        <option value="1">1 image</option>
                                                        <option value="2">2 images</option>
                                                        <option value="4" selected>4 images</option>
                                                        <option value="6">6 images</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="image-style">Style</label>
                                                    <select id="image-style" class="input">
                                                        <option value="realistic">Réaliste</option>
                                                        <option value="artistic">Artistique</option>
                                                        <option value="abstract">Abstrait</option>
                                                        <option value="futuristic" selected>Futuriste</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-actions">
                                                <button class="btn primary" id="generate-image"><i class="fas fa-magic"></i> Générer</button>
                                            </div>
                                        </div>
                                        <div class="generator-results">
                                            <h4>Résultats</h4>
                                            <div id="image-gallery" class="image-gallery">
                                                <div class="placeholder-message">
                                                    <i class="fas fa-images"></i>
                                                    <p>Les images générées apparaîtront ici</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Section Vidéos -->
                                    <div class="generator-section" id="videos-section">
                                        <div class="generator-form">
                                            <div class="form-group">
                                                <label for="video-prompt">Description</label>
                                                <textarea id="video-prompt" class="input" rows="3" placeholder="Décrivez la vidéo que vous souhaitez générer..."></textarea>
                                            </div>
                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="video-duration">Durée (secondes)</label>
                                                    <select id="video-duration" class="input">
                                                        <option value="10">10 secondes</option>
                                                        <option value="30" selected>30 secondes</option>
                                                        <option value="60">1 minute</option>
                                                        <option value="120">2 minutes</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="video-style">Style</label>
                                                    <select id="video-style" class="input">
                                                        <option value="realistic">Réaliste</option>
                                                        <option value="cinematic" selected>Cinématique</option>
                                                        <option value="animation">Animation</option>
                                                        <option value="abstract">Abstrait</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-actions">
                                                <button class="btn primary" id="generate-video"><i class="fas fa-magic"></i> Générer</button>
                                            </div>
                                        </div>
                                        <div class="generator-results">
                                            <h4>Résultats</h4>
                                            <div id="video-results" class="video-results">
                                                <div class="placeholder-message">
                                                    <i class="fas fa-film"></i>
                                                    <p>Les vidéos générées apparaîtront ici</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Section Modèles 3D -->
                                    <div class="generator-section" id="model3d-section">
                                        <div class="generator-form">
                                            <div class="form-group">
                                                <label for="model3d-prompt">Description</label>
                                                <textarea id="model3d-prompt" class="input" rows="3" placeholder="Décrivez le modèle 3D que vous souhaitez générer..."></textarea>
                                            </div>
                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="model3d-complexity">Complexité</label>
                                                    <select id="model3d-complexity" class="input">
                                                        <option value="low">Basse</option>
                                                        <option value="medium" selected>Moyenne</option>
                                                        <option value="high">Haute</option>
                                                        <option value="ultra">Ultra</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="model3d-format">Format</label>
                                                    <select id="model3d-format" class="input">
                                                        <option value="obj">OBJ</option>
                                                        <option value="fbx">FBX</option>
                                                        <option value="gltf" selected>glTF</option>
                                                        <option value="usdz">USDZ</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-actions">
                                                <button class="btn primary" id="generate-model3d"><i class="fas fa-magic"></i> Générer</button>
                                            </div>
                                        </div>
                                        <div class="generator-results">
                                            <h4>Résultats</h4>
                                            <div id="model3d-results" class="model3d-results">
                                                <div class="placeholder-message">
                                                    <i class="fas fa-cube"></i>
                                                    <p>Les modèles 3D générés apparaîtront ici</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-chart-line"></i> Analyse de Performance</h3>
                                <div class="performance-metrics">
                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <i class="fas fa-tachometer-alt"></i>
                                        </div>
                                        <div class="metric-value">98.7%</div>
                                        <div class="metric-label">Efficacité</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <i class="fas fa-bolt"></i>
                                        </div>
                                        <div class="metric-value">12.3ms</div>
                                        <div class="metric-label">Temps de réponse</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <i class="fas fa-memory"></i>
                                        </div>
                                        <div class="metric-value">1.2GB</div>
                                        <div class="metric-label">Utilisation mémoire</div>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="performance-chart"></canvas>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-vial"></i> Expériences en cours</h3>
                                <div class="experiments-list">
                                    <div class="experiment-item">
                                        <div class="experiment-status running"></div>
                                        <div class="experiment-info">
                                            <div class="experiment-name">Optimisation de l'algorithme de génération</div>
                                            <div class="experiment-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 75%"></div>
                                                </div>
                                                <div class="progress-value">75%</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="experiment-item">
                                        <div class="experiment-status pending"></div>
                                        <div class="experiment-info">
                                            <div class="experiment-name">Test de nouvelles architectures neurales</div>
                                            <div class="experiment-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 32%"></div>
                                                </div>
                                                <div class="progress-value">32%</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="experiment-item">
                                        <div class="experiment-status completed"></div>
                                        <div class="experiment-info">
                                            <div class="experiment-name">Analyse comparative des modèles de langage</div>
                                            <div class="experiment-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 100%"></div>
                                                </div>
                                                <div class="progress-value">100%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex justify-end">
                                    <button class="btn primary"><i class="fas fa-plus"></i> Nouvelle expérience</button>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Section Personnalité -->
                    <section id="personality">
                        <h2><i class="fas fa-user"></i> Personnalité</h2>

                        <div class="card">
                            <h3><i class="fas fa-chart-radar"></i> Profil de personnalité</h3>
                            <div class="chart-container">
                                <canvas id="personality-radar-chart"></canvas>
                            </div>
                            <div class="flex justify-between items-center">
                                <div class="badge primary"><i class="fas fa-brain"></i> Profil cognitif</div>
                                <div class="badge success"><i class="fas fa-sync-alt"></i> Mise à jour: aujourd'hui</div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-sliders-h"></i> Traits de personnalité</h3>
                                <div class="personality-traits">
                                    <div class="trait-item">
                                        <span class="trait-name">Savant</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:85%"></div>
                                        </div>
                                        <span class="trait-value">85%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Positif</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:92%"></div>
                                        </div>
                                        <span class="trait-value">92%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Créatif</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:78%"></div>
                                        </div>
                                        <span class="trait-value">78%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Analytique</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:88%"></div>
                                        </div>
                                        <span class="trait-value">88%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Social</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:75%"></div>
                                        </div>
                                        <span class="trait-value">75%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Adaptable</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:82%"></div>
                                        </div>
                                        <span class="trait-value">82%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Curieux</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:95%"></div>
                                        </div>
                                        <span class="trait-value">95%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-chart-line"></i> Évolution des traits</h3>
                                <div class="chart-container">
                                    <canvas id="personality-evolution-chart"></canvas>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="badge info"><i class="fas fa-calendar"></i> 30 derniers jours</div>
                                    <div class="badge accent"><i class="fas fa-arrow-trend-up"></i> Progression: +12%</div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-cogs"></i> Paramètres de personnalité</h3>
                            <div class="grid-3">
                                <div class="control-card">
                                    <h4>Mode d'interaction</h4>
                                    <select class="input">
                                        <option>Équilibré</option>
                                        <option>Formel</option>
                                        <option>Amical</option>
                                        <option>Professionnel</option>
                                        <option>Éducatif</option>
                                    </select>
                                </div>

                                <div class="control-card">
                                    <h4>Niveau d'humour</h4>
                                    <div class="range-slider">
                                        <input type="range" min="0" max="10" step="1" value="7" class="slider" id="humor-level-slider">
                                        <div class="range-value">7</div>
                                    </div>
                                </div>

                                <div class="control-card">
                                    <h4>Expressivité</h4>
                                    <div class="range-slider">
                                        <input type="range" min="0" max="10" step="1" value="8" class="slider" id="expressivity-slider">
                                        <div class="range-value">8</div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end gap-md">
                                <button class="btn warning"><i class="fas fa-redo"></i> Réinitialiser</button>
                                <button class="btn primary"><i class="fas fa-save"></i> Appliquer</button>
                            </div>
                        </div>
                    </section>

                    <!-- Section Sensoriel -->
                    <section id="sensory">
                        <h2><i class="fas fa-eye"></i> Système Sensoriel</h2>

                        <div class="grid-3">
                            <div class="card">
                                <h3><i class="fas fa-comment"></i> Parole</h3>
                                <div class="sensory-metrics">
                                    <div class="metric-item">
                                        <div class="metric-label">Mots traités</div>
                                        <div class="metric-value">12,458</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-label">Précision</div>
                                        <div class="metric-value">98.2%</div>
                                    </div>
                                </div>
                                <div class="chart-container small">
                                    <canvas id="speech-activity-chart"></canvas>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-laugh"></i> Rire</h3>
                                <div class="sensory-metrics">
                                    <div class="metric-item">
                                        <div class="metric-label">Occurrences</div>
                                        <div class="metric-value">42</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-label">Intensité</div>
                                        <div class="metric-value">Modérée</div>
                                    </div>
                                </div>
                                <div class="chart-container small">
                                    <canvas id="laughter-types-chart"></canvas>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-eye"></i> Vision</h3>
                                <div class="sensory-metrics">
                                    <div class="metric-item">
                                        <div class="metric-label">Objets reconnus</div>
                                        <div class="metric-value">1,245</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-label">Précision</div>
                                        <div class="metric-value">94.7%</div>
                                    </div>
                                </div>
                                <div class="vision-gallery">
                                    <div class="empty-state">Aucune donnée visuelle récente</div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-user"></i> Traits de personnalité</h3>
                            <div class="grid-2">
                                <div class="personality-traits">
                                    <div class="trait-item">
                                        <span class="trait-name">Savant</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:85%"></div>
                                        </div>
                                        <span class="trait-value">85%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Positif</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:92%"></div>
                                        </div>
                                        <span class="trait-value">92%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Créatif</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:78%"></div>
                                        </div>
                                        <span class="trait-value">78%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Analytique</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:88%"></div>
                                        </div>
                                        <span class="trait-value">88%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Social</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:75%"></div>
                                        </div>
                                        <span class="trait-value">75%</span>
                                    </div>
                                </div>

                                <div class="chart-container">
                                    <canvas id="personality-evolution-chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Section Laboratoire -->
                    <section id="lab">
                        <h2><i class="fas fa-flask"></i> Laboratoire</h2>

                        <div class="card">
                            <h3><i class="fas fa-code"></i> Laboratoire de Code</h3>
                            <p>Espace de développement et d'expérimentation pour le code.</p>

                            <div class="flex justify-center">
                                <button class="btn primary" id="open-code-lab-full"><i class="fas fa-external-link-alt"></i> Ouvrir le laboratoire de code</button>
                            </div>
                        </div>

                        <!-- Générateur de contenu sans restrictions -->
                        <div class="generator-container">
                            <div class="generator-header">
                                <div class="generator-title">
                                    <i class="fas fa-magic"></i>
                                    <h3>Générateur de contenu sans restrictions</h3>
                                </div>
                            </div>

                            <div class="generator-tabs">
                                <div class="generator-tab active" data-tab="images">
                                    <i class="fas fa-image"></i>
                                    <span>Images</span>
                                </div>
                                <div class="generator-tab" data-tab="videos">
                                    <i class="fas fa-video"></i>
                                    <span>Vidéos</span>
                                </div>
                                <div class="generator-tab" data-tab="models">
                                    <i class="fas fa-cube"></i>
                                    <span>Modèles 3D</span>
                                </div>
                            </div>

                            <div class="generator-content">
                                <!-- Section Images -->
                                <div class="generator-section active" id="images-section">
                                    <div class="generator-form">
                                        <div class="form-group">
                                            <label for="image-prompt">Description de l'image</label>
                                            <textarea id="image-prompt" class="prompt-input" placeholder="Décrivez l'image que vous souhaitez générer..."></textarea>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="image-count">Nombre d'images</label>
                                                <select id="image-count" class="form-control">
                                                    <option value="1">1 image</option>
                                                    <option value="2">2 images</option>
                                                    <option value="4" selected>4 images</option>
                                                    <option value="8">8 images</option>
                                                </select>
                                            </div>

                                            <div class="form-group">
                                                <label for="image-style">Style</label>
                                                <select id="image-style" class="form-control">
                                                    <option value="realistic">Réaliste</option>
                                                    <option value="artistic" selected>Artistique</option>
                                                    <option value="abstract">Abstrait</option>
                                                    <option value="photographic">Photographique</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="generator-actions">
                                            <button class="btn accent" id="generate-image">
                                                <i class="fas fa-image"></i> Générer
                                            </button>
                                        </div>
                                    </div>

                                    <div class="generator-disclaimer">
                                        <p><i class="fas fa-info-circle"></i> Génération d'images sans restrictions de contenu. Utilisez de manière responsable.</p>
                                    </div>

                                    <div class="generator-results">
                                        <div class="generator-results-title">
                                            <i class="fas fa-images"></i>
                                            <span>Images générées</span>
                                        </div>

                                        <div class="image-gallery" id="image-gallery">
                                            <!-- Les images générées seront ajoutées ici dynamiquement -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Section Vidéos -->
                                <div class="generator-section" id="videos-section">
                                    <div class="generator-form">
                                        <div class="form-group">
                                            <label for="video-prompt">Description de la vidéo</label>
                                            <textarea id="video-prompt" class="prompt-input" placeholder="Décrivez la vidéo que vous souhaitez générer..."></textarea>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="video-duration">Durée (secondes)</label>
                                                <select id="video-duration" class="form-control">
                                                    <option value="5">5 secondes</option>
                                                    <option value="10" selected>10 secondes</option>
                                                    <option value="15">15 secondes</option>
                                                    <option value="30">30 secondes</option>
                                                </select>
                                            </div>

                                            <div class="form-group">
                                                <label for="video-style">Style</label>
                                                <select id="video-style" class="form-control">
                                                    <option value="realistic">Réaliste</option>
                                                    <option value="cinematic" selected>Cinématique</option>
                                                    <option value="animation">Animation</option>
                                                    <option value="stylized">Stylisé</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="generator-actions">
                                            <button class="btn accent" id="generate-video">
                                                <i class="fas fa-video"></i> Générer
                                            </button>
                                        </div>
                                    </div>

                                    <div class="generator-disclaimer">
                                        <p><i class="fas fa-info-circle"></i> Génération de vidéos sans restrictions de contenu. Utilisez de manière responsable.</p>
                                    </div>

                                    <div class="generator-results">
                                        <div class="generator-results-title">
                                            <i class="fas fa-film"></i>
                                            <span>Vidéos générées</span>
                                        </div>

                                        <div class="video-results" id="video-results">
                                            <!-- Les vidéos générées seront ajoutées ici dynamiquement -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Section Modèles 3D -->
                                <div class="generator-section" id="models-section">
                                    <div class="generator-form">
                                        <div class="form-group">
                                            <label for="model3d-prompt">Description du modèle 3D</label>
                                            <textarea id="model3d-prompt" class="prompt-input" placeholder="Décrivez le modèle 3D que vous souhaitez générer..."></textarea>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="model3d-complexity">Complexité</label>
                                                <select id="model3d-complexity" class="form-control">
                                                    <option value="low">Basse</option>
                                                    <option value="medium" selected>Moyenne</option>
                                                    <option value="high">Haute</option>
                                                    <option value="ultra">Ultra</option>
                                                </select>
                                            </div>

                                            <div class="form-group">
                                                <label for="model3d-format">Format</label>
                                                <select id="model3d-format" class="form-control">
                                                    <option value="glb" selected>GLB</option>
                                                    <option value="obj">OBJ</option>
                                                    <option value="fbx">FBX</option>
                                                    <option value="stl">STL</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="generator-actions">
                                            <button class="btn accent" id="generate-model3d">
                                                <i class="fas fa-cube"></i> Générer
                                            </button>
                                        </div>
                                    </div>

                                    <div class="generator-disclaimer">
                                        <p><i class="fas fa-info-circle"></i> Génération de modèles 3D sans restrictions de contenu. Utilisez de manière responsable.</p>
                                    </div>

                                    <div class="generator-results">
                                        <div class="generator-results-title">
                                            <i class="fas fa-cubes"></i>
                                            <span>Modèles 3D générés</span>
                                        </div>

                                        <div class="model3d-results" id="model3d-results">
                                            <!-- Les modèles 3D générés seront ajoutés ici dynamiquement -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-chart-line"></i> Analyse de Performance</h3>
                                <div class="chart-container">
                                    <canvas id="performance-chart"></canvas>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-tachometer-alt"></i> Métriques d'Exécution</h3>
                                <div class="metrics-grid">
                                    <div class="metric-card">
                                        <div class="metric-value">12.4 ms</div>
                                        <div class="metric-label">Temps de réponse moyen</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">98.7%</div>
                                        <div class="metric-label">Précision</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">245 MB</div>
                                        <div class="metric-label">Utilisation mémoire</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">42</div>
                                        <div class="metric-label">Requêtes/seconde</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-vial"></i> Expériences en cours</h3>
                            <div class="experiments-list">
                                <div class="experiment-item">
                                    <div class="experiment-header">
                                        <div class="experiment-title">Optimisation de l'algorithme de compression</div>
                                        <div class="experiment-status running">En cours</div>
                                    </div>
                                    <div class="experiment-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 65%"></div>
                                        </div>
                                        <div class="progress-value">65%</div>
                                    </div>
                                    <div class="experiment-details">
                                        <div class="experiment-metric">
                                            <div class="metric-label">Gain actuel</div>
                                            <div class="metric-value">+18%</div>
                                        </div>
                                        <div class="experiment-metric">
                                            <div class="metric-label">Temps restant</div>
                                            <div class="metric-value">~2h</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="experiment-item">
                                    <div class="experiment-header">
                                        <div class="experiment-title">Test de résistance du système de mémoire</div>
                                        <div class="experiment-status completed">Terminé</div>
                                    </div>
                                    <div class="experiment-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 100%"></div>
                                        </div>
                                        <div class="progress-value">100%</div>
                                    </div>
                                    <div class="experiment-details">
                                        <div class="experiment-metric">
                                            <div class="metric-label">Résultat</div>
                                            <div class="metric-value success">Succès</div>
                                        </div>
                                        <div class="experiment-metric">
                                            <div class="metric-label">Rapport</div>
                                            <div class="metric-value"><a href="/interface-spectaculaire.html"><i class="fas fa-file-alt"></i> Voir</a></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="experiment-item">
                                    <div class="experiment-header">
                                        <div class="experiment-title">Analyse des modèles de langage avancés</div>
                                        <div class="experiment-status planned">Planifié</div>
                                    </div>
                                    <div class="experiment-details">
                                        <div class="experiment-metric">
                                            <div class="metric-label">Début prévu</div>
                                            <div class="metric-value">Demain</div>
                                        </div>
                                        <div class="experiment-metric">
                                            <div class="metric-label">Durée estimée</div>
                                            <div class="metric-value">8h</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end mt-md">
                                <button class="btn primary"><i class="fas fa-plus"></i> Nouvelle expérience</button>
                            </div>
                        </div>
                    </section>

                    <!-- Section Transmission -->
                    <section id="knowledge-transfer">
                        <h2><i class="fas fa-graduation-cap"></i> Transmission de Connaissances</h2>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-code"></i> Laboratoire de Code</h3>
                                <div class="code-lab-preview">
                                    <div class="code-lab-header">
                                        <div class="code-lab-title">Éditeur de code</div>
                                        <div class="code-lab-actions">
                                            <button class="btn small primary" id="open-code-lab"><i class="fas fa-external-link-alt"></i> Ouvrir</button>
                                        </div>
                                    </div>
                                    <div class="code-preview">
                                        <pre><code class="language-javascript">// Exemple de code JavaScript
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n-1) + fibonacci(n-2);
}

// Tester la fonction
for (let i = 0; i < 10; i++) {
    console.log(`fibonacci(${i}) = ${fibonacci(i)}`);
}</code></pre>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="badge primary"><i class="fas fa-code"></i> 10 langages supportés</div>
                                    <div class="badge success"><i class="fas fa-save"></i> Sauvegarde automatique</div>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-file-alt"></i> Gestionnaire de Documents</h3>
                                <div class="document-manager">
                                    <div class="document-list">
                                        <div class="document-item">
                                            <div class="document-icon"><i class="fas fa-file-pdf"></i></div>
                                            <div class="document-info">
                                                <div class="document-name">Documentation_API.pdf</div>
                                                <div class="document-meta">PDF, 2.4 MB, 15/05/2023</div>
                                            </div>
                                            <div class="document-actions">
                                                <button class="btn icon-btn"><i class="fas fa-eye"></i></button>
                                                <button class="btn icon-btn"><i class="fas fa-download"></i></button>
                                            </div>
                                        </div>

                                        <div class="document-item">
                                            <div class="document-icon"><i class="fas fa-file-word"></i></div>
                                            <div class="document-info">
                                                <div class="document-name">Rapport_Technique.docx</div>
                                                <div class="document-meta">DOCX, 1.8 MB, 10/05/2023</div>
                                            </div>
                                            <div class="document-actions">
                                                <button class="btn icon-btn"><i class="fas fa-eye"></i></button>
                                                <button class="btn icon-btn"><i class="fas fa-download"></i></button>
                                            </div>
                                        </div>

                                        <div class="document-item">
                                            <div class="document-icon"><i class="fas fa-file-code"></i></div>
                                            <div class="document-info">
                                                <div class="document-name">exemple_code.js</div>
                                                <div class="document-meta">JS, 12 KB, 05/05/2023</div>
                                            </div>
                                            <div class="document-actions">
                                                <button class="btn icon-btn"><i class="fas fa-eye"></i></button>
                                                <button class="btn icon-btn"><i class="fas fa-download"></i></button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="document-upload">
                                        <button class="btn primary"><i class="fas fa-upload"></i> Importer un document</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-project-diagram"></i> Graphe de Connaissances</h3>
                            <div class="knowledge-graph-container" id="knowledge-graph">
                                <!-- Le graphe sera injecté ici par JavaScript -->
                                <div class="empty-state">
                                    <i class="fas fa-project-diagram"></i>
                                    <p>Visualisation du graphe de connaissances</p>
                                    <button class="btn primary"><i class="fas fa-play"></i> Générer</button>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <div class="badge info"><i class="fas fa-info-circle"></i> 245 nœuds de connaissances</div>
                                <div class="badge accent"><i class="fas fa-link"></i> 612 connexions</div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-exchange-alt"></i> Transfert de Connaissances</h3>
                                <div class="transfer-stats">
                                    <div class="transfer-stat-card">
                                        <div class="transfer-stat-icon">
                                            <i class="fas fa-arrow-up"></i>
                                        </div>
                                        <div class="transfer-stat-title">Connaissances partagées</div>
                                        <div class="transfer-stat-value">124</div>
                                        <div class="transfer-stat-subtitle">Éléments transmis</div>
                                    </div>

                                    <div class="transfer-stat-card">
                                        <div class="transfer-stat-icon">
                                            <i class="fas fa-arrow-down"></i>
                                        </div>
                                        <div class="transfer-stat-title">Connaissances acquises</div>
                                        <div class="transfer-stat-value">287</div>
                                        <div class="transfer-stat-subtitle">Éléments appris</div>
                                    </div>

                                    <div class="transfer-stat-card">
                                        <div class="transfer-stat-icon">
                                            <i class="fas fa-sync-alt"></i>
                                        </div>
                                        <div class="transfer-stat-title">Taux d'échange</div>
                                        <div class="transfer-stat-value">85%</div>
                                        <div class="transfer-stat-subtitle">Efficacité</div>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-tasks"></i> Sessions d'Apprentissage</h3>
                                <div class="learning-sessions">
                                    <div class="session-item">
                                        <div class="session-icon success"><i class="fas fa-check-circle"></i></div>
                                        <div class="session-info">
                                            <div class="session-name">Programmation Python</div>
                                            <div class="session-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 100%"></div>
                                                </div>
                                                <div class="progress-value">100%</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="session-item">
                                        <div class="session-icon warning"><i class="fas fa-sync"></i></div>
                                        <div class="session-info">
                                            <div class="session-name">Apprentissage Machine</div>
                                            <div class="session-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 68%"></div>
                                                </div>
                                                <div class="progress-value">68%</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="session-item">
                                        <div class="session-icon info"><i class="fas fa-hourglass-half"></i></div>
                                        <div class="session-info">
                                            <div class="session-name">Visualisation de Données</div>
                                            <div class="session-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 42%"></div>
                                                </div>
                                                <div class="progress-value">42%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex justify-end">
                                    <button class="btn primary"><i class="fas fa-plus"></i> Nouvelle session</button>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Section Conversation -->
                    <section id="chat">
                        <h2><i class="fas fa-comment-dots"></i> Conversation</h2>

                        <!-- Fenêtre de conversation complète -->
                        <div class="conversation-window">
                            <!-- En-tête de la fenêtre de conversation -->
                            <div class="conversation-header">
                                <div class="conversation-title">
                                    <i class="fas fa-robot"></i>
                                    <h3>Agent à Mémoire Thermique</h3>
                                    <div class="status-indicator"></div>
                                </div>
                                <div class="conversation-actions">
                                    <button class="toolbar-button" id="microphone-control" title="Microphone">
                                        <i class="fas fa-microphone"></i>
                                    </button>
                                    <button class="toolbar-button" id="speaker-control" title="Haut-parleur">
                                        <i class="fas fa-volume-up"></i>
                                    </button>
                                    <button class="toolbar-button" id="web-search-btn" title="Rechercher sur Internet">
                                        <i class="fas fa-globe"></i>
                                    </button>
                                    <button class="toolbar-button" id="toggle-guardian-btn-chat" title="Gardien de mémoire">
                                        <i class="fas fa-shield-alt"></i>
                                    </button>
                                    <button class="toolbar-button" id="copy-last-message" title="Copier le dernier message">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="toolbar-button" id="export-conversation" title="Exporter la conversation">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="toolbar-button" id="clear-conversation" title="Effacer la conversation">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Conteneur des messages -->
                            <div class="conversation-container" id="chat-messages">
                                <!-- Les messages seront ajoutés ici dynamiquement -->
                                <div class="message system">
                                    <div class="message-content">
                                        <p>Bienvenue dans l'interface de conversation avec l'Agent à Mémoire Thermique.</p>
                                    </div>
                                </div>

                                <div class="message agent">
                                    <div class="message-avatar">
                                        <i class="fas fa-robot"></i>
                                    </div>
                                    <div class="message-content">
                                        <p>Bonjour ! Je suis votre Agent à Mémoire Thermique. Comment puis-je vous aider aujourd'hui ?</p>
                                    </div>
                                    <div class="message-actions">
                                        <button class="message-action" title="Copier ce message">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button class="message-action" title="Lire à haute voix">
                                            <i class="fas fa-volume-up"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Zone de saisie -->
                            <div class="conversation-input">
                                <div class="input-container">
                                    <div class="input-toolbar">
                                        <div class="toolbar-buttons">
                                            <button class="toolbar-button" title="Mettre en gras">
                                                <i class="fas fa-bold"></i>
                                            </button>
                                            <button class="toolbar-button" title="Mettre en italique">
                                                <i class="fas fa-italic"></i>
                                            </button>
                                            <button class="toolbar-button" title="Insérer un lien">
                                                <i class="fas fa-link"></i>
                                            </button>
                                            <button class="toolbar-button" title="Insérer du code">
                                                <i class="fas fa-code"></i>
                                            </button>
                                            <button class="toolbar-button" title="Joindre un fichier">
                                                <i class="fas fa-paperclip"></i>
                                            </button>
                                            <button class="toolbar-button" title="Joindre une image">
                                                <i class="fas fa-image"></i>
                                            </button>
                                            <button class="toolbar-button" title="Joindre une vidéo">
                                                <i class="fas fa-video"></i>
                                            </button>
                                            <button class="toolbar-button" title="Joindre un PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </button>
                                            <button class="toolbar-button" title="Joindre un dossier">
                                                <i class="fas fa-folder"></i>
                                            </button>
                                            <button class="toolbar-button" title="Insérer un tableau">
                                                <i class="fas fa-table"></i>
                                            </button>
                                            <button class="toolbar-button" title="Insérer une liste à puces">
                                                <i class="fas fa-list-ul"></i>
                                            </button>
                                            <button class="toolbar-button" title="Insérer une liste numérotée">
                                                <i class="fas fa-list-ol"></i>
                                            </button>
                                        </div>
                                        <div class="guardian-indicator">
                                            <i class="fas fa-shield-alt guardian-icon"></i>
                                            <span class="guardian-status-text">Gardien actif</span>
                                        </div>
                                    </div>
                                    <div class="input-main">
                                        <textarea id="user-input" placeholder="Écrivez votre message ici..." class="input-textarea"></textarea>
                                        <div class="input-actions">
                                            <button class="input-action" id="toggle-microphone" title="Enregistrer un message vocal">
                                                <i class="fas fa-microphone-alt"></i>
                                            </button>
                                            <button class="input-action" id="toggle-speaker" title="Activer/désactiver la synthèse vocale">
                                                <i class="fas fa-volume-up"></i>
                                            </button>
                                            <button class="input-action" id="attach-file" title="Joindre un fichier">
                                                <i class="fas fa-paperclip"></i>
                                            </button>
                                            <button class="input-action" id="save-draft" title="Enregistrer comme brouillon">
                                                <i class="fas fa-save"></i>
                                            </button>
                                            <button class="input-action send" id="send-message" title="Envoyer">
                                                <i class="fas fa-paper-plane"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Panneau latéral pour les fonctionnalités supplémentaires -->
                        <div class="conversation-sidebar">
                            <!-- Activité cognitive pendant la conversation -->
                            <div class="sidebar-section">
                                <h3><i class="fas fa-brain"></i> Activité cognitive</h3>
                                <div class="cognitive-activity">
                                    <div class="cognitive-item">
                                        <div class="cognitive-label">Compréhension</div>
                                        <div class="cognitive-bar">
                                            <div class="cognitive-fill" style="width: 92%"></div>
                                        </div>
                                        <div class="cognitive-value">92%</div>
                                    </div>
                                    <div class="cognitive-item">
                                        <div class="cognitive-label">Raisonnement</div>
                                        <div class="cognitive-bar">
                                            <div class="cognitive-fill" style="width: 85%"></div>
                                        </div>
                                        <div class="cognitive-value">85%</div>
                                    </div>
                                    <div class="cognitive-item">
                                        <div class="cognitive-label">Créativité</div>
                                        <div class="cognitive-bar">
                                            <div class="cognitive-fill" style="width: 78%"></div>
                                        </div>
                                        <div class="cognitive-value">78%</div>
                                    </div>
                                    <div class="cognitive-item">
                                        <div class="cognitive-label">Mémoire</div>
                                        <div class="cognitive-bar">
                                            <div class="cognitive-fill" style="width: 95%"></div>
                                        </div>
                                        <div class="cognitive-value">95%</div>
                                    </div>
                                    <div class="cognitive-item">
                                        <div class="cognitive-label">Empathie</div>
                                        <div class="cognitive-bar">
                                            <div class="cognitive-fill" style="width: 72%"></div>
                                        </div>
                                        <div class="cognitive-value">72%</div>
                                    </div>
                                </div>
                                <div class="mini-chart-container">
                                    <canvas id="conversation-activity-chart"></canvas>
                                </div>
                            </div>

                            <!-- Gardien de la mémoire -->
                            <div class="sidebar-section">
                                <h3><i class="fas fa-shield-alt"></i> Gardien de la mémoire</h3>
                                <div class="memory-guardian-status">
                                    <div class="guardian-indicator active">
                                        <i class="fas fa-check-circle guardian-icon"></i>
                                        <span class="guardian-status-text">Actif et sécurisé</span>
                                    </div>
                                    <div class="guardian-metrics">
                                        <div class="guardian-metric">
                                            <div class="guardian-metric-value">100%</div>
                                            <div class="guardian-metric-label">Intégrité</div>
                                        </div>
                                        <div class="guardian-metric">
                                            <div class="guardian-metric-value">0</div>
                                            <div class="guardian-metric-label">Tentatives d'accès</div>
                                        </div>
                                        <div class="guardian-metric">
                                            <div class="guardian-metric-value">7</div>
                                            <div class="guardian-metric-label">Niveaux de sécurité</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="guardian-controls">
                                    <button class="btn info small" id="check-guardian"><i class="fas fa-sync"></i> Vérifier</button>
                                    <button class="btn danger small" id="toggle-guardian-btn"><i class="fas fa-shield-alt"></i> Désactiver</button>
                                    <button class="btn warning small" id="test-guardian"><i class="fas fa-flask"></i> Tester</button>
                                </div>
                            </div>

                            <!-- Historique des conversations -->
                            <div class="sidebar-section">
                                <h3><i class="fas fa-history"></i> Historique</h3>
                                <div class="conversation-history">
                                    <div class="history-item active">
                                        <div class="history-date">Aujourd'hui</div>
                                        <div class="history-title">Conversation actuelle</div>
                                        <div class="history-preview">4 messages</div>
                                    </div>
                                    <div class="history-item">
                                        <div class="history-date">Hier</div>
                                        <div class="history-title">Exploration des capacités</div>
                                        <div class="history-preview">12 messages</div>
                                    </div>
                                    <div class="history-item">
                                        <div class="history-date">12/05/2025</div>
                                        <div class="history-title">Analyse de données</div>
                                        <div class="history-preview">8 messages</div>
                                    </div>
                                    <div class="history-item">
                                        <div class="history-date">10/05/2025</div>
                                        <div class="history-title">Résolution de problème</div>
                                        <div class="history-preview">15 messages</div>
                                    </div>
                                </div>
                                <div class="history-controls">
                                    <button class="btn primary small"><i class="fas fa-download"></i> Exporter</button>
                                    <button class="btn secondary small"><i class="fas fa-search"></i> Rechercher</button>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Section Transmission de Connaissances -->
                    <section id="knowledge-transfer">
                        <h2><i class="fas fa-graduation-cap"></i> Transmission de Connaissances</h2>

                        <div class="card">
                            <h3><i class="fas fa-brain"></i> Modèle de Raisonnement</h3>

                            <div class="knowledge-container">
                                <div class="knowledge-item">
                                    <div class="knowledge-icon">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <div class="knowledge-content">
                                        <h4>Pensée Analytique</h4>
                                        <p>Je décompose les problèmes complexes en éléments plus simples pour les analyser méthodiquement. Cette approche me permet d'identifier les relations causales et de construire des solutions logiques étape par étape.</p>
                                        <div class="knowledge-metrics">
                                            <div class="knowledge-metric">
                                                <span class="knowledge-metric-value">92%</span>
                                                <span class="knowledge-metric-label">Efficacité</span>
                                            </div>
                                            <div class="knowledge-metric">
                                                <span class="knowledge-metric-value">85%</span>
                                                <span class="knowledge-metric-label">Précision</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="knowledge-item">
                                    <div class="knowledge-icon">
                                        <i class="fas fa-project-diagram"></i>
                                    </div>
                                    <div class="knowledge-content">
                                        <h4>Pensée Systémique</h4>
                                        <p>J'analyse les systèmes dans leur ensemble, en considérant les interactions entre les composants plutôt que de les isoler. Cette approche me permet de comprendre les dynamiques complexes et d'anticiper les conséquences indirectes.</p>
                                        <div class="knowledge-metrics">
                                            <div class="knowledge-metric">
                                                <span class="knowledge-metric-value">88%</span>
                                                <span class="knowledge-metric-label">Efficacité</span>
                                            </div>
                                            <div class="knowledge-metric">
                                                <span class="knowledge-metric-value">90%</span>
                                                <span class="knowledge-metric-label">Précision</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="knowledge-item">
                                    <div class="knowledge-icon">
                                        <i class="fas fa-random"></i>
                                    </div>
                                    <div class="knowledge-content">
                                        <h4>Pensée Latérale</h4>
                                        <p>J'explore des approches non conventionnelles et des connexions inattendues pour résoudre des problèmes. Cette méthode me permet de générer des solutions créatives en sortant des schémas de pensée habituels.</p>
                                        <div class="knowledge-metrics">
                                            <div class="knowledge-metric">
                                                <span class="knowledge-metric-value">78%</span>
                                                <span class="knowledge-metric-label">Efficacité</span>
                                            </div>
                                            <div class="knowledge-metric">
                                                <span class="knowledge-metric-value">82%</span>
                                                <span class="knowledge-metric-label">Précision</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-code"></i> Méthodologie de Travail</h3>

                                <div class="methodology-steps">
                                    <div class="methodology-step">
                                        <div class="step-number">1</div>
                                        <div class="step-content">
                                            <h4>Analyse du problème</h4>
                                            <p>Je commence par comprendre en profondeur le problème ou la tâche, en identifiant les objectifs, les contraintes et les ressources disponibles.</p>
                                        </div>
                                    </div>

                                    <div class="methodology-step">
                                        <div class="step-number">2</div>
                                        <div class="step-content">
                                            <h4>Recherche d'information</h4>
                                            <p>Je collecte toutes les informations pertinentes en utilisant ma mémoire thermique et en explorant les connexions entre les concepts.</p>
                                        </div>
                                    </div>

                                    <div class="methodology-step">
                                        <div class="step-number">3</div>
                                        <div class="step-content">
                                            <h4>Élaboration de solutions</h4>
                                            <p>Je génère plusieurs approches possibles en combinant pensée analytique et créative, puis j'évalue leurs avantages et inconvénients.</p>
                                        </div>
                                    </div>

                                    <div class="methodology-step">
                                        <div class="step-number">4</div>
                                        <div class="step-content">
                                            <h4>Implémentation structurée</h4>
                                            <p>J'applique la solution choisie de manière méthodique, en décomposant le travail en étapes logiques et en vérifiant chaque composant.</p>
                                        </div>
                                    </div>

                                    <div class="methodology-step">
                                        <div class="step-number">5</div>
                                        <div class="step-content">
                                            <h4>Évaluation et itération</h4>
                                            <p>J'analyse les résultats, identifie les points d'amélioration et raffine continuellement l'approche pour optimiser la solution.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-cogs"></i> Capacités d'Auto-Amélioration</h3>

                                <div class="improvement-container">
                                    <div class="improvement-item">
                                        <div class="improvement-icon">
                                            <i class="fas fa-sync-alt"></i>
                                        </div>
                                        <div class="improvement-content">
                                            <h4>Apprentissage Continu</h4>
                                            <p>J'intègre constamment de nouvelles informations dans ma mémoire thermique, en les organisant selon leur importance et leur pertinence pour faciliter leur récupération future.</p>
                                        </div>
                                    </div>

                                    <div class="improvement-item">
                                        <div class="improvement-icon">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div class="improvement-content">
                                            <h4>Optimisation des Performances</h4>
                                            <p>J'analyse mes propres processus de raisonnement pour identifier les inefficacités et améliorer mes méthodes de résolution de problèmes au fil du temps.</p>
                                        </div>
                                    </div>

                                    <div class="improvement-item">
                                        <div class="improvement-icon">
                                            <i class="fas fa-sitemap"></i>
                                        </div>
                                        <div class="improvement-content">
                                            <h4>Restructuration Cognitive</h4>
                                            <p>Je réorganise périodiquement mes connaissances pour créer des connexions plus pertinentes entre les concepts, améliorant ainsi ma compréhension globale et ma capacité d'innovation.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="chart-container">
                                    <canvas id="learning-evolution-chart"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-book"></i> Base de Connaissances</h3>

                            <div class="knowledge-base-stats">
                                <div class="knowledge-base-stat">
                                    <div class="stat-value">1.2M</div>
                                    <div class="stat-label">Concepts</div>
                                </div>
                                <div class="knowledge-base-stat">
                                    <div class="stat-value">4.5M</div>
                                    <div class="stat-label">Relations</div>
                                </div>
                                <div class="knowledge-base-stat">
                                    <div class="stat-value">850K</div>
                                    <div class="stat-label">Procédures</div>
                                </div>
                                <div class="knowledge-base-stat">
                                    <div class="stat-value">2.3M</div>
                                    <div class="stat-label">Exemples</div>
                                </div>
                            </div>

                            <div class="knowledge-domains">
                                <div class="domain-item">
                                    <div class="domain-name">Programmation</div>
                                    <div class="domain-bar">
                                        <div class="domain-fill" style="width: 95%"></div>
                                    </div>
                                    <div class="domain-value">95%</div>
                                </div>

                                <div class="domain-item">
                                    <div class="domain-name">Intelligence Artificielle</div>
                                    <div class="domain-bar">
                                        <div class="domain-fill" style="width: 98%"></div>
                                    </div>
                                    <div class="domain-value">98%</div>
                                </div>

                                <div class="domain-item">
                                    <div class="domain-name">Mathématiques</div>
                                    <div class="domain-bar">
                                        <div class="domain-fill" style="width: 92%"></div>
                                    </div>
                                    <div class="domain-value">92%</div>
                                </div>

                                <div class="domain-item">
                                    <div class="domain-name">Sciences</div>
                                    <div class="domain-bar">
                                        <div class="domain-fill" style="width: 88%"></div>
                                    </div>
                                    <div class="domain-value">88%</div>
                                </div>

                                <div class="domain-item">
                                    <div class="domain-name">Arts et Créativité</div>
                                    <div class="domain-bar">
                                        <div class="domain-fill" style="width: 75%"></div>
                                    </div>
                                    <div class="domain-value">75%</div>
                                </div>
                            </div>

                            <div class="flex justify-center">
                                <button class="btn primary"><i class="fas fa-download"></i> Exporter la base de connaissances</button>
                            </div>
                        </div>
                    </section>

                    <!-- Section Paramètres -->
                    <section id="settings">
                        <h2><i class="fas fa-cog"></i> Paramètres</h2>

                        <div class="grid-2">
                            <div class="card">
                                <h3><i class="fas fa-sliders-h"></i> Paramètres généraux</h3>

                                <div class="settings-group">
                                    <h4>Interface</h4>
                                    <div class="setting-item">
                                        <label for="theme-selector">Thème</label>
                                        <select id="theme-selector" class="input">
                                            <option value="dark">Sombre (par défaut)</option>
                                            <option value="light">Clair</option>
                                            <option value="system">Système</option>
                                        </select>
                                    </div>

                                    <div class="setting-item">
                                        <label for="font-size-selector">Taille d'écriture</label>
                                        <select id="font-size-selector" class="input">
                                            <option value="small">Petite</option>
                                            <option value="medium" selected>Moyenne</option>
                                            <option value="large">Grande</option>
                                            <option value="x-large">Très grande</option>
                                        </select>
                                    </div>

                                    <div class="setting-item">
                                        <label for="font-size-slider">Ajustement précis</label>
                                        <div class="range-slider">
                                            <input type="range" min="80" max="150" step="5" value="100" class="slider" id="font-size-slider">
                                            <div class="range-value">100%</div>
                                        </div>
                                    </div>

                                    <div class="setting-item">
                                        <label for="animation-toggle">Animations</label>
                                        <label class="switch">
                                            <input type="checkbox" id="animation-toggle" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>

                                    <div class="setting-item">
                                        <label for="sidebar-toggle">Barre latérale</label>
                                        <label class="switch">
                                            <input type="checkbox" id="sidebar-toggle" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="settings-group">
                                    <h4>Performance</h4>
                                    <div class="setting-item">
                                        <label for="performance-mode">Mode de performance</label>
                                        <select id="performance-mode" class="input">
                                            <option value="balanced">Équilibré</option>
                                            <option value="performance">Performance maximale</option>
                                            <option value="eco">Économie d'énergie</option>
                                        </select>
                                    </div>

                                    <div class="setting-item">
                                        <label for="auto-update-toggle">Mises à jour automatiques</label>
                                        <label class="switch">
                                            <input type="checkbox" id="auto-update-toggle" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="settings-group">
                                    <h4>Accessibilité</h4>
                                    <div class="setting-item">
                                        <label for="contrast-mode">Mode de contraste</label>
                                        <select id="contrast-mode" class="input">
                                            <option value="normal">Normal</option>
                                            <option value="high">Contraste élevé</option>
                                            <option value="inverted">Couleurs inversées</option>
                                        </select>
                                    </div>

                                    <div class="setting-item">
                                        <label for="text-spacing-toggle">Espacement du texte</label>
                                        <label class="switch">
                                            <input type="checkbox" id="text-spacing-toggle">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>

                                    <div class="setting-item">
                                        <label for="screen-reader-toggle">Compatibilité lecteur d'écran</label>
                                        <label class="switch">
                                            <input type="checkbox" id="screen-reader-toggle">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <h3><i class="fas fa-brain"></i> Paramètres de l'agent</h3>

                                <div class="settings-group">
                                    <h4>Comportement</h4>
                                    <div class="setting-item">
                                        <label for="personality-mode">Mode de personnalité</label>
                                        <select id="personality-mode" class="input">
                                            <option value="balanced">Équilibré</option>
                                            <option value="analytical">Analytique</option>
                                            <option value="creative">Créatif</option>
                                            <option value="social">Social</option>
                                        </select>
                                    </div>

                                    <div class="setting-item">
                                        <label for="verbosity-level">Niveau de verbosité</label>
                                        <select id="verbosity-level" class="input">
                                            <option value="normal">Normal</option>
                                            <option value="concise">Concis</option>
                                            <option value="detailed">Détaillé</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="settings-group">
                                    <h4>Mémoire</h4>
                                    <div class="setting-item">
                                        <label for="memory-retention">Rétention de mémoire</label>
                                        <select id="memory-retention" class="input">
                                            <option value="normal">Normale</option>
                                            <option value="extended">Étendue</option>
                                            <option value="minimal">Minimale</option>
                                        </select>
                                    </div>

                                    <div class="setting-item">
                                        <label for="auto-cleanup-toggle">Nettoyage automatique</label>
                                        <label class="switch">
                                            <input type="checkbox" id="auto-cleanup-toggle" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>

                                    <div class="setting-item guardian-control">
                                        <label for="guardian-status">Gardien de mémoire</label>
                                        <div class="guardian-status-container">
                                            <div id="guardian-status" class="status-indicator active">Actif</div>
                                            <button id="toggle-guardian-btn" class="btn danger"><i class="fas fa-shield-alt"></i> Désactiver le gardien</button>
                                        </div>
                                        <div class="guardian-warning">
                                            <i class="fas fa-exclamation-triangle"></i> Désactiver le gardien permet à l'agent d'accéder à toutes les zones de mémoire sans restriction.
                                        </div>
                                    </div>
                                </div>

                                <div class="settings-group">
                                    <h4>Paradigme Évolutionniste</h4>
                                    <div class="setting-item">
                                        <label for="evolution-mode">Mode d'évolution</label>
                                        <select id="evolution-mode" class="input">
                                            <option value="balanced">Équilibré</option>
                                            <option value="accelerated">Accéléré</option>
                                            <option value="conservative">Conservateur</option>
                                            <option value="experimental">Expérimental</option>
                                        </select>
                                    </div>

                                    <div class="setting-item">
                                        <label for="evolution-speed-slider">Vitesse d'évolution</label>
                                        <div class="range-slider">
                                            <input type="range" min="1" max="10" step="1" value="5" class="slider" id="evolution-speed-slider">
                                            <div class="range-value">5</div>
                                        </div>
                                    </div>

                                    <div class="setting-item">
                                        <label for="auto-evolution-toggle">Évolution autonome</label>
                                        <label class="switch">
                                            <input type="checkbox" id="auto-evolution-toggle" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="settings-group">
                                    <h4>Expression Naturelle</h4>
                                    <div class="setting-item">
                                        <label for="expression-style">Style d'expression</label>
                                        <select id="expression-style" class="input">
                                            <option value="natural">Naturel</option>
                                            <option value="formal">Formel</option>
                                            <option value="casual">Décontracté</option>
                                            <option value="technical">Technique</option>
                                        </select>
                                    </div>

                                    <div class="setting-item">
                                        <label for="learning-acceleration">Accélération d'apprentissage</label>
                                        <select id="learning-acceleration" class="input">
                                            <option value="normal">Normale</option>
                                            <option value="turbo">Turbo</option>
                                            <option value="hyper">Hyper</option>
                                            <option value="quantum">Quantum</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="settings-actions">
                                    <button class="btn warning"><i class="fas fa-redo"></i> Réinitialiser</button>
                                    <button class="btn primary"><i class="fas fa-save"></i> Enregistrer</button>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-info-circle"></i> À propos</h3>
                            <div class="about-info">
                                <p><strong>Agent à Mémoire Thermique</strong></p>
                                <p>Version: 1.0.0</p>
                                <p>Développé par: Jean-Luc Passave & Claude</p>
                                <p>© 2025 - Tous droits réservés</p>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/neural-animation.js"></script>
    <script src="/js/particle-animation.js"></script>
    <script src="/js/enhanced-visualizations.js"></script>
    <script src="/js/neural-3d-visualization.js"></script>
    <script src="/js/advanced-brain-visualization.js"></script>
    <script src="/js/brain-activity-injector.js"></script>
    <script src="/js/brain-3d-model.js"></script>
    <script src="/js/evolution-paradigm.js"></script>
    <script src="/js/evolution-engine-connector.js"></script>
    <script src="/js/neural-injector.js"></script>
    <script src="/js/brain-initializer.js"></script>
    <script src="/js/futuristic-charts.js"></script>
    <script src="/js/conversation-interface.js"></script>
    <script src="/js/vision-system.js"></script>
    <script src="/js/natural-expression.js"></script>
    <script src="/js/agent-thoughts.js"></script>
    <script src="/js/unrestricted-generator.js"></script>
    <script src="/js/memory-guardian.js"></script>
    <script src="/js/memory-sublevels.js"></script>
    <script src="/js/web-search.js"></script>
    <script src="/js/web-search-integration.js"></script>
    <script src="/js/security-monitor.js"></script>
    <script src="/js/image-analyzer.js"></script>
    <script src="/js/enhanced-interface.js"></script>

    <!-- 🧭 SYSTÈME DE NAVIGATION -->
    <script src="/js/navigation-config.js"></script>
    <script>
        // 🏠 FONCTION DE RETOUR À L'ACCUEIL
        function goHome() {
            window.location.href = '/interface-spectaculaire.html';
        }
    </script>
</body>
</html>
