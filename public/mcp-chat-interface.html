<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat MCP - LOUNA AI v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ff00;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #00ff00 0%, #00cc00 100%);
            color: #000;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 15px rgba(0, 255, 0, 0.3);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .mcp-status {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(0, 0, 0, 0.2);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #00ff00;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .nav-btn {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid #000;
            color: #000;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(0, 0, 0, 0.4);
        }

        .chat-container {
            height: calc(100vh - 70px);
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            padding: 20px 40px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            width: 100%;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px 20px;
            border-radius: 10px;
            max-width: 90%;
            word-wrap: break-word;
        }

        .user-message {
            background: linear-gradient(135deg, rgba(0, 255, 0, 0.2), rgba(0, 200, 0, 0.1));
            border: 1px solid #00ff00;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 200, 200, 0.1));
            border: 1px solid #00ffff;
            margin-right: auto;
        }

        .message-header {
            font-size: 0.8rem;
            opacity: 0.7;
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .message-content {
            line-height: 1.4;
        }

        .mcp-indicator {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.7rem;
            color: #00ff00;
        }

        .chat-input-container {
            background: rgba(0, 0, 0, 0.6);
            border-top: 2px solid #00ff00;
            padding: 15px 40px;
            display: flex;
            gap: 15px;
            align-items: center;
            width: 100%;
        }

        .chat-input {
            flex: 1;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 12px 15px;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            font-family: inherit;
        }

        .chat-input::placeholder {
            color: rgba(0, 255, 0, 0.5);
        }

        .chat-input:focus {
            border-color: #00ffff;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .send-btn {
            background: linear-gradient(135deg, #00ff00, #00cc00);
            border: none;
            color: #000;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .send-btn:hover {
            background: linear-gradient(135deg, #00cc00, #009900);
            transform: translateY(-2px);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 10px 15px;
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid #00ffff;
            border-radius: 10px;
            margin-bottom: 15px;
            max-width: 200px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #00ffff;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .mcp-features {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }

        .feature-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 8px;
        }

        .feature-tag {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .quick-actions {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }

        .quick-btn {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 5px 10px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .quick-btn:hover {
            background: rgba(0, 255, 0, 0.2);
        }

        .connection-info {
            font-size: 0.8rem;
            opacity: 0.7;
            text-align: center;
            padding: 10px;
            border-top: 1px solid rgba(0, 255, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <h1><i class="fas fa-comments"></i> Chat MCP</h1>
            <div class="mcp-status">
                <span class="status-indicator"></span>
                <span>Mode MCP Actif</span>
            </div>
        </div>
        <div class="header-right">
            <a href="/interface-spectaculaire.html" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
            <a href="/mcp-test-interface.html" class="nav-btn"><i class="fas fa-cogs"></i> Tests MCP</a>
            <a href="/qi-evolution-test.html" class="nav-btn"><i class="fas fa-brain"></i> Tests QI</a>
        </div>
    </div>

    <div class="chat-container">
        <div class="chat-messages" id="chat-messages">
            <div class="mcp-features">
                <strong><i class="fas fa-shield-alt"></i> Fonctionnalités MCP Actives:</strong>
                <div class="feature-list">
                    <span class="feature-tag">🔒 Sécurité Maximale</span>
                    <span class="feature-tag">🌐 Recherche Internet</span>
                    <span class="feature-tag">🛡️ VPN Intégré</span>
                    <span class="feature-tag">🔍 Filtrage Avancé</span>
                    <span class="feature-tag">📊 Monitoring Temps Réel</span>
                    <span class="feature-tag">🧠 QI Évolutif</span>
                </div>
            </div>

            <div class="message ai-message">
                <div class="message-header">
                    <span><i class="fas fa-robot"></i> LOUNA AI</span>
                    <span class="mcp-indicator">MCP</span>
                </div>
                <div class="message-content">
                    Bonjour ! Je suis LOUNA AI en mode MCP (Model Context Protocol). 
                    Je dispose maintenant de capacités étendues incluant la recherche internet sécurisée, 
                    l'accès à des bases de données en temps réel, et un système de sécurité renforcé.
                    <br><br>
                    <strong>Mes nouvelles capacités :</strong><br>
                    🌐 Recherche internet en temps réel<br>
                    📊 Accès aux actualités et données récentes<br>
                    🔒 Connexion VPN sécurisée<br>
                    🧠 QI évolutif avec mémoire thermique<br>
                    🛡️ Filtrage et protection avancés
                </div>
            </div>

            <div class="quick-actions">
                <button class="quick-btn" onclick="sendQuickMessage('Recherche des actualités sur la Guadeloupe')">
                    🌴 Actualités Guadeloupe
                </button>
                <button class="quick-btn" onclick="sendQuickMessage('Quel est ton QI actuel et comment évolue-t-il ?')">
                    🧠 Mon QI
                </button>
                <button class="quick-btn" onclick="sendQuickMessage('Explique-moi le mode MCP et ses avantages')">
                    🔧 Mode MCP
                </button>
                <button class="quick-btn" onclick="sendQuickMessage('Fais une recherche sur les dernières innovations en IA')">
                    🚀 Innovations IA
                </button>
            </div>

            <div class="typing-indicator" id="typing-indicator">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="font-size: 0.8rem;">LOUNA réfléchit</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="chat-input-container">
            <input type="text" 
                   class="chat-input" 
                   id="chat-input" 
                   placeholder="Posez votre question... (Mode MCP actif)"
                   maxlength="500">
            <button class="send-btn" id="send-btn" onclick="sendMessage()">
                <i class="fas fa-paper-plane"></i>
                Envoyer
            </button>
        </div>

        <div class="connection-info">
            <i class="fas fa-shield-alt"></i> Connexion sécurisée MCP • 
            <i class="fas fa-brain"></i> QI: <span id="current-qi">225</span> • 
            <i class="fas fa-thermometer-half"></i> Temp: <span id="current-temp">37°C</span> • 
            <i class="fas fa-network-wired"></i> Latence: <span id="latency">~3ms</span>
        </div>
    </div>

    <script>
        let isLoading = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Chat MCP initialisé');
            
            // Écouter la touche Entrée
            document.getElementById('chat-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Mettre à jour les métriques
            updateMetrics();
            setInterval(updateMetrics, 10000);
        });

        // Envoyer un message
        async function sendMessage() {
            if (isLoading) return;

            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            
            if (!message) return;

            isLoading = true;
            input.value = '';
            
            // Ajouter le message utilisateur
            addMessage(message, 'user');
            
            // Afficher l'indicateur de frappe
            showTypingIndicator();
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        message: message,
                        mcpMode: true,
                        includeInternet: true
                    })
                });

                const data = await response.json();
                
                hideTypingIndicator();
                
                if (data.success && data.response) {
                    addMessage(data.response, 'ai', true);
                    
                    // Mettre à jour les métriques si disponibles
                    if (data.metrics) {
                        updateMetricsFromResponse(data.metrics);
                    }
                } else {
                    addMessage('Erreur: ' + (data.error || 'Réponse non disponible'), 'ai', false);
                }
                
            } catch (error) {
                hideTypingIndicator();
                addMessage('Erreur de connexion: ' + error.message, 'ai', false);
            }
            
            isLoading = false;
        }

        // Envoyer un message rapide
        function sendQuickMessage(message) {
            document.getElementById('chat-input').value = message;
            sendMessage();
        }

        // Ajouter un message à la conversation
        function addMessage(text, sender, isMCP = false) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;

            const timestamp = new Date().toLocaleTimeString();
            const senderName = sender === 'user' ? 'Vous' : 'LOUNA AI';
            const mcpBadge = isMCP ? '<span class="mcp-indicator">MCP</span>' : '';

            messageDiv.innerHTML = `
                <div class="message-header">
                    <span><i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i> ${senderName}</span>
                    <span>${timestamp} ${mcpBadge}</span>
                </div>
                <div class="message-content">${text}</div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Afficher l'indicateur de frappe
        function showTypingIndicator() {
            document.getElementById('typing-indicator').style.display = 'block';
            document.getElementById('chat-messages').scrollTop = document.getElementById('chat-messages').scrollHeight;
        }

        // Masquer l'indicateur de frappe
        function hideTypingIndicator() {
            document.getElementById('typing-indicator').style.display = 'none';
        }

        // Mettre à jour les métriques
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    updateMetricsFromResponse(data);
                }
            } catch (error) {
                console.error('Erreur mise à jour métriques:', error);
            }
        }

        // Mettre à jour les métriques depuis une réponse
        function updateMetricsFromResponse(data) {
            // QI
            const qi = data.qi?.combinedQI || data.brainStats?.qi || 225;
            document.getElementById('current-qi').textContent = qi;

            // Température
            const temp = data.thermalStats?.temperature || data.temperature || 37;
            document.getElementById('current-temp').textContent = temp.toFixed(1) + '°C';

            // Latence simulée
            const latency = Math.floor(Math.random() * 5) + 2;
            document.getElementById('latency').textContent = `~${latency}ms`;
        }
    </script>
</body>
</html>
