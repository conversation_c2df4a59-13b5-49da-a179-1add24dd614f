<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Interface Electron Optimisée</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .main-container {
            display: grid;
            grid-template-columns: 280px 1fr 320px;
            grid-template-rows: 70px 1fr 40px;
            height: 100vh;
            gap: 8px;
            padding: 8px;
        }

        .header {
            grid-column: 1 / -1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            backdrop-filter: blur(10px);
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b9d;
        }

        .status-indicators {
            display: flex;
            gap: 15px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4ade80;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .left-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        .right-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        .center-panel {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            max-height: calc(100vh - 300px);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 10px;
            max-width: 80%;
        }

        .message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin-left: auto;
            text-align: right;
        }

        .message.ai {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            margin-right: auto;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .chat-input {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            outline: none;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .send-button {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .panel-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ff6b9d;
            text-align: center;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        .stat-value {
            font-weight: bold;
            color: #4ade80;
        }

        .memory-entry {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .memory-type {
            color: #ff6b9d;
            font-weight: bold;
            font-size: 10px;
            text-transform: uppercase;
        }

        .footer {
            grid-column: 1 / -1;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ff6b9d;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .quality-indicator {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            margin-left: 8px;
        }

        .quality-excellent { background: #4ade80; color: #000; }
        .quality-good { background: #fbbf24; color: #000; }
        .quality-average { background: #f97316; color: #fff; }
        .quality-poor { background: #ef4444; color: #fff; }

        .thought-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 8px;
            border-radius: 8px;
            margin-bottom: 6px;
            border-left: 3px solid #ff6b9d;
            font-size: 11px;
        }

        .thought-time {
            font-size: 9px;
            color: #00d4aa;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .thought-content {
            line-height: 1.3;
            color: #ffffff;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <div class="logo">🧠 LOUNA AI Ultra-Autonome - Electron</div>
            <div style="display: flex; gap: 10px; align-items: center;">
                <button onclick="goToHome()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: 600; font-size: 12px;">
                    🏠 Accueil
                </button>
                <button onclick="toggleVoiceReflection()" id="voiceBtn" style="background: linear-gradient(135deg, #ff6b9d, #c44569); color: white; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: 600; font-size: 12px;">
                    🔊 Écouter
                </button>
                <button onclick="startChatGPTDialogue()" id="chatgptBtn" style="background: linear-gradient(135deg, #00d4aa, #01a3a4); color: white; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: 600; font-size: 12px;">
                    🌐 ChatGPT
                </button>
                <div class="status-indicators">
                    <div class="status-indicator">
                        <div class="status-dot" id="deepseek-status"></div>
                        <span>DeepSeek</span>
                    </div>
                    <div class="status-indicator">
                        <div class="status-dot" id="thermal-status"></div>
                        <span>Thermique</span>
                    </div>
                    <div class="status-indicator">
                        <div class="status-dot" id="electron-status"></div>
                        <span>IPC</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="left-panel">
            <div class="panel-title">📊 Statistiques IA</div>
            <div id="ai-stats">
                <div class="stat-item">
                    <span>QI Agent:</span>
                    <span class="stat-value" id="agent-iq">100</span>
                </div>
                <div class="stat-item">
                    <span>QI Mémoire:</span>
                    <span class="stat-value" id="memory-iq">0</span>
                </div>
                <div class="stat-item">
                    <span>QI Combiné:</span>
                    <span class="stat-value" id="combined-iq">100</span>
                </div>
                <div class="stat-item">
                    <span>Neurones:</span>
                    <span class="stat-value" id="neurons">240</span>
                </div>
                <div class="stat-item">
                    <span>Température:</span>
                    <span class="stat-value" id="temperature">37.0°C</span>
                </div>
                <div class="stat-item">
                    <span>Efficacité:</span>
                    <span class="stat-value" id="efficiency">95%</span>
                </div>
            </div>

            <!-- 🎨 GÉNÉRATEURS DE MÉDIAS -->
            <div style="margin-top: 20px;">
                <div class="panel-title">🎨 Générateurs Médias</div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-top: 10px;">
                    <button onclick="generateVideo()" style="background: linear-gradient(135deg, #ff6b9d, #c44569); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        🎬 Vidéo LT X
                    </button>
                    <button onclick="generateImage()" style="background: linear-gradient(135deg, #00d4aa, #01a3a4); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        🖼️ Image
                    </button>
                    <button onclick="generateAudio()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        🎵 Audio
                    </button>
                    <button onclick="generateMusic()" style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        🎼 Musique
                    </button>
                </div>
            </div>

            <!-- 🔧 OUTILS DE DIAGNOSTIC -->
            <div style="margin-top: 20px;">
                <div class="panel-title">🔧 Diagnostic</div>
                <div style="display: grid; grid-template-columns: 1fr; gap: 6px; margin-top: 10px;">
                    <button onclick="openDiagnostic()" style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        🔍 Tests QI
                    </button>
                    <button onclick="openBrainVisualization()" style="background: linear-gradient(135deg, #a8edea, #fed6e3); color: #333; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        🧠 Cerveau 3D
                    </button>
                    <button onclick="openThermalDashboard()" style="background: linear-gradient(135deg, #ffecd2, #fcb69f); color: #333; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        🌡️ Thermique
                    </button>
                    <button onclick="runSystemCheck()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        ⚡ Check Système
                    </button>
                    <button onclick="exportBrainData()" style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        💾 Export Cerveau
                    </button>
                </div>
            </div>

            <!-- 🎯 CONTRÔLES AVANCÉS -->
            <div style="margin-top: 20px;">
                <div class="panel-title">🎯 Contrôles</div>
                <div style="display: grid; grid-template-columns: 1fr; gap: 6px; margin-top: 10px;">
                    <button onclick="pauseThoughts()" id="pauseBtn" style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        ⏸️ Pause Pensées
                    </button>
                    <button onclick="boostCreativity()" style="background: linear-gradient(135deg, #9b59b6, #8e44ad); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        🎨 Boost Créatif
                    </button>
                    <button onclick="deepFocus()" style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        🎯 Focus Profond
                    </button>
                    <button onclick="restMode()" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 11px; font-weight: 600;">
                        😴 Mode Repos
                    </button>
                </div>
            </div>
        </div>

        <div class="center-panel">
            <div class="chat-container">
                <div class="chat-messages" id="chat-messages">
                    <div class="message ai">
                        🧠 Bonjour ! Je suis LOUNA AI avec DeepSeek R1 8B intégré directement dans Electron.
                        Ma mémoire thermique fonctionne en temps réel et j'apprends de chaque interaction.
                        Comment puis-je vous aider aujourd'hui ?
                    </div>
                </div>

                <!-- 🌐 ZONE D'APPRENTISSAGE CHATGPT -->
                <div id="chatgptLearningZone" style="display: none; margin: 15px 0; padding: 15px; background: linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(1, 163, 164, 0.1)); border-radius: 12px; border: 2px solid #00d4aa;">
                    <h4 style="color: #00d4aa; margin: 0 0 10px 0; font-size: 14px;">🧠 Zone d'Apprentissage ChatGPT</h4>
                    <p style="color: #fff; margin-bottom: 10px; font-size: 12px;">Collez ici la réponse de ChatGPT pour que LOUNA apprenne :</p>
                    <textarea id="chatgptResponseInput" placeholder="Collez la réponse de ChatGPT ici..." style="width: 100%; height: 100px; padding: 10px; border-radius: 8px; border: 1px solid #00d4aa; background: rgba(0, 0, 0, 0.3); color: #fff; font-size: 12px; resize: vertical; font-family: inherit;"></textarea>
                    <div style="margin-top: 10px; display: flex; gap: 8px;">
                        <button onclick="submitChatGPTResponse()" style="background: linear-gradient(135deg, #00d4aa, #01a3a4); color: white; border: none; padding: 8px 15px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 11px;">
                            🧠 Analyser
                        </button>
                        <button onclick="clearChatGPTResponse()" style="background: linear-gradient(135deg, #ff6b9d, #c44569); color: white; border: none; padding: 8px 15px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 11px;">
                            🗑️ Effacer
                        </button>
                    </div>
                    <div id="learningResult" style="margin-top: 10px; padding: 10px; background: rgba(0, 0, 0, 0.2); border-radius: 8px; display: none;">
                        <h5 style="color: #00d4aa; margin: 0 0 8px 0; font-size: 12px;">📊 Résultat :</h5>
                        <div id="learningDetails" style="font-size: 11px;"></div>
                        <div id="nextQuestionDisplay" style="margin-top: 10px; padding: 10px; background: linear-gradient(135deg, rgba(255, 107, 157, 0.2), rgba(196, 69, 105, 0.2)); border-radius: 8px;">
                            <h5 style="color: #ff6b9d; margin: 0 0 8px 0; font-size: 12px;">🎯 Prochaine Question :</h5>
                            <p id="nextQuestionText" style="color: #fff; margin: 0; font-style: italic; font-size: 11px;"></p>
                        </div>
                    </div>
                </div>

                <div class="chat-input-container">
                    <input type="text" class="chat-input" id="chat-input"
                           placeholder="Tapez votre message... (Entrée pour envoyer)">
                    <button class="send-button" id="send-button">Envoyer</button>
                </div>
            </div>

            <!-- 📊 MÉTRIQUES SYSTÈME EN TEMPS RÉEL -->
            <div style="margin-top: 20px; background: rgba(255, 255, 255, 0.05); border-radius: 12px; padding: 15px;">
                <h4 style="color: #ff6b9d; margin: 0 0 15px 0; font-size: 14px; text-align: center;">📊 Métriques Système</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <div style="background: rgba(255, 255, 255, 0.1); padding: 10px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 16px; font-weight: bold; color: #00d4aa;" id="neurons-display">1,064,000</div>
                        <div style="font-size: 10px; opacity: 0.8;">Neurones</div>
                    </div>
                    <div style="background: rgba(255, 255, 255, 0.1); padding: 10px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 16px; font-weight: bold; color: #ff6b9d;" id="synapses-display">7,448,000</div>
                        <div style="font-size: 10px; opacity: 0.8;">Synapses</div>
                    </div>
                    <div style="background: rgba(255, 255, 255, 0.1); padding: 10px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 16px; font-weight: bold; color: #f39c12;" id="temp-display">37.0°C</div>
                        <div style="font-size: 10px; opacity: 0.8;">Température</div>
                    </div>
                    <div style="background: rgba(255, 255, 255, 0.1); padding: 10px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 16px; font-weight: bold; color: #e74c3c;" id="qi-display">450</div>
                        <div style="font-size: 10px; opacity: 0.8;">QI Total</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="right-panel">
            <div class="panel-title">🌀 Pensées Continues</div>

            <!-- État Möbius -->
            <div style="background: rgba(255, 255, 255, 0.1); padding: 12px; border-radius: 10px; margin-bottom: 15px; font-size: 11px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span style="color: #00d4aa; font-weight: bold;">État Möbius:</span>
                    <span id="mobiusStatus" style="color: #ff6b9d;">Initialisation...</span>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 10px;">
                    <div>Position: <span id="mobiusPosition" style="color: #00d4aa;">0%</span></div>
                    <div>Phase: <span id="mobiusPhase" style="color: #ff6b9d;">exploration</span></div>
                    <div>Direction: <span id="mobiusDirection" style="color: #f39c12;">→</span></div>
                    <div>Chaos: <span id="chaosLevel" style="color: #e74c3c;">30%</span></div>
                </div>
                <div style="margin-top: 8px;">
                    <div style="background: rgba(0, 0, 0, 0.3); height: 6px; border-radius: 3px; overflow: hidden;">
                        <div id="mobiusProgress" style="background: linear-gradient(90deg, #ff6b9d, #00d4aa, #ff6b9d); height: 100%; width: 0%; transition: width 0.5s ease;"></div>
                    </div>
                </div>
            </div>

            <!-- Santé Mentale -->
            <div style="background: rgba(255, 255, 255, 0.05); padding: 10px; border-radius: 8px; margin-bottom: 15px; font-size: 11px;">
                <div style="font-weight: bold; margin-bottom: 6px; color: #ff6b9d;">🧠 Santé Mentale</div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px; font-size: 10px;">
                    <div>Fatigue: <span id="fatigueLevel" style="color: #f39c12;">0%</span></div>
                    <div>Stress: <span id="stressLevel" style="color: #e74c3c;">0%</span></div>
                    <div style="grid-column: 1 / -1;">État: <span id="mentalState" style="color: #00d4aa;">Actif</span></div>
                </div>
            </div>

            <!-- Liste des pensées -->
            <div id="thoughtsList" style="max-height: calc(100vh - 400px); overflow-y: auto;">
                <div class="memory-entry">
                    <div class="memory-type">Initialisation</div>
                    🌀 Chargement des pensées continues...
                </div>
            </div>
        </div>

        <div class="footer">
            🚀 LOUNA AI v3.0 - Interface Electron Optimisée - Systèmes IA intégrés
        </div>
    </div>

    <script>
        // 🤖 INTERFACE ELECTRON AVEC IPC
        const { ipcRenderer } = require('electron');

        let isProcessing = false;
        let voiceEnabled = false;
        let currentVoice = null;
        let lastSpokenThought = null;
        
        // Éléments DOM
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-button');
        
        // 📡 FONCTION D'ENVOI DE MESSAGE
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message || isProcessing) return;
            
            isProcessing = true;
            sendButton.disabled = true;
            sendButton.innerHTML = '<div class="loading"></div>';
            
            // Afficher le message utilisateur
            addMessage(message, 'user');
            chatInput.value = '';
            
            try {
                // Envoyer via IPC à DeepSeek
                const result = await ipcRenderer.invoke('deepseek-chat', message);
                
                if (result.success) {
                    // Afficher la réponse avec indicateur de qualité
                    const qualityClass = getQualityClass(result.quality.score);
                    const qualityText = result.quality.grade;
                    
                    addMessage(
                        result.response + 
                        `<span class="quality-indicator ${qualityClass}">${qualityText} ${result.quality.score}/100</span>`,
                        'ai'
                    );
                    
                    // Mettre à jour les stats
                    updateStats();
                    
                } else {
                    addMessage(`❌ Erreur: ${result.error}`, 'ai');
                }
                
            } catch (error) {
                console.error('Erreur chat:', error);
                addMessage(`❌ Erreur de communication: ${error.message}`, 'ai');
            }
            
            isProcessing = false;
            sendButton.disabled = false;
            sendButton.textContent = 'Envoyer';
        }
        
        // 💬 AJOUTER UN MESSAGE AU CHAT
        function addMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = content;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 🎨 CLASSE DE QUALITÉ
        function getQualityClass(score) {
            if (score >= 80) return 'quality-excellent';
            if (score >= 60) return 'quality-good';
            if (score >= 40) return 'quality-average';
            return 'quality-poor';
        }
        
        // 📊 METTRE À JOUR LES STATISTIQUES AVEC QI RESTAURÉ
        async function updateStats() {
            try {
                // 🚀 RÉCUPÉRER LES VRAIES MÉTRIQUES DU SERVEUR (QI RESTAURÉ)
                let serverMetrics = null;
                try {
                    const serverResponse = await fetch('/api/metrics');
                    const serverData = await serverResponse.json();
                    if (serverData.success) {
                        serverMetrics = serverData;
                        console.log(`🧮 QI SERVEUR RÉCUPÉRÉ: Agent=${serverData.qi?.agentIQ}, Mémoire=${serverData.qi?.memoryIQ}, Total=${serverData.qi?.combinedIQ}`);
                        console.log(`🧠 NEURONES SERVEUR: ${serverData.neurons} neurones`);
                    }
                } catch (fetchError) {
                    console.log('⚠️ Impossible de récupérer les métriques du serveur, utilisation d\'Electron');
                }

                const result = await ipcRenderer.invoke('get-ai-stats');

                if (result.success && result.stats) {
                    const { deepseek, thermalMemory, serverStats } = result.stats;

                    // 🎯 PRIORITÉ AUX MÉTRIQUES DU SERVEUR (QI RESTAURÉ)
                    if (serverMetrics && serverMetrics.qi) {
                        document.getElementById('agent-iq').textContent = serverMetrics.qi.agentIQ || 100;
                        document.getElementById('memory-iq').textContent = serverMetrics.qi.memoryIQ || 0;
                        document.getElementById('combined-iq').textContent = serverMetrics.qi.combinedIQ || 100;

                        console.log(`🧮 QI INTERFACE RESTAURÉ: Agent=${serverMetrics.qi.agentIQ}, Mémoire=${serverMetrics.qi.memoryIQ}, Total=${serverMetrics.qi.combinedIQ}`);
                    } else if (thermalMemory && thermalMemory.qi) {
                        // Fallback avec les stats Electron
                        document.getElementById('agent-iq').textContent = thermalMemory.qi.agentIQ || 100;
                        document.getElementById('memory-iq').textContent = thermalMemory.qi.memoryIQ || 0;
                        document.getElementById('combined-iq').textContent = thermalMemory.qi.combinedIQ || 100;
                    } else {
                        // Fallback par défaut
                        document.getElementById('agent-iq').textContent = '100';
                        document.getElementById('memory-iq').textContent = '0';
                        document.getElementById('combined-iq').textContent = '100';
                    }

                    // 🧠 NEURONES ET AUTRES MÉTRIQUES
                    if (serverMetrics) {
                        document.getElementById('neurons').textContent = (serverMetrics.neurons || 152000).toLocaleString();
                        document.getElementById('temperature').textContent = `${serverMetrics.temperature || 37.0}°C`;

                        // Mettre à jour les métriques du panneau central
                        document.getElementById('neurons-display').textContent = (serverMetrics.neurons || 152000).toLocaleString();
                        document.getElementById('temp-display').textContent = `${serverMetrics.temperature || 37.0}°C`;
                        document.getElementById('qi-display').textContent = serverMetrics.qi?.combinedIQ || 450;

                        // Calculer les synapses (estimation basée sur les neurones)
                        const synapses = Math.floor((serverMetrics.neurons || 152000) * 7);
                        document.getElementById('synapses-display').textContent = synapses.toLocaleString();

                    } else if (thermalMemory) {
                        document.getElementById('neurons').textContent = (thermalMemory.neurons || 152000).toLocaleString();
                        document.getElementById('temperature').textContent = `${thermalMemory.temperature || 37.0}°C`;
                        document.getElementById('efficiency').textContent = `${thermalMemory.efficiency || 95}%`;

                        // Mettre à jour les métriques du panneau central
                        document.getElementById('neurons-display').textContent = (thermalMemory.neurons || 152000).toLocaleString();
                        document.getElementById('temp-display').textContent = `${thermalMemory.temperature || 37.0}°C`;
                        document.getElementById('qi-display').textContent = thermalMemory.qi?.combinedIQ || 450;

                        const synapses = Math.floor((thermalMemory.neurons || 152000) * 7);
                        document.getElementById('synapses-display').textContent = synapses.toLocaleString();
                    }
                }

            } catch (error) {
                console.error('Erreur mise à jour stats:', error);
            }
        }
        
        // 🎯 ÉVÉNEMENTS
        sendButton.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 🏠 NAVIGATION VERS L'ACCUEIL
        function goToHome() {
            window.location.href = '/interface-spectaculaire.html';
        }

        // 🔊 INITIALISER LA SYNTHÈSE VOCALE
        function initializeVoice() {
            if ('speechSynthesis' in window) {
                const voices = speechSynthesis.getVoices();
                currentVoice = voices.find(voice =>
                    voice.lang.includes('fr') &&
                    (voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('femme'))
                ) || voices.find(voice => voice.lang.includes('fr')) || voices[0];
                console.log('🔊 Voix sélectionnée:', currentVoice?.name);
            }
        }

        // 🔊 ACTIVER/DÉSACTIVER L'ÉCOUTE DES PENSÉES
        function toggleVoiceReflection() {
            voiceEnabled = !voiceEnabled;
            const btn = document.getElementById('voiceBtn');

            if (voiceEnabled) {
                btn.textContent = '🔇 Arrêter';
                btn.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
                console.log('🔊 Écoute des pensées ACTIVÉE');
            } else {
                btn.textContent = '🔊 Écouter';
                btn.style.background = 'linear-gradient(135deg, #ff6b9d, #c44569)';
                console.log('🔇 Écoute des pensées DÉSACTIVÉE');
                if ('speechSynthesis' in window) {
                    speechSynthesis.cancel();
                }
            }
        }

        // 🗣️ PARLER UNE PENSÉE
        function speakThought(thoughtContent) {
            if (!voiceEnabled || !('speechSynthesis' in window)) return;

            speechSynthesis.cancel();
            const utterance = new SpeechSynthesisUtterance(thoughtContent);

            if (currentVoice) {
                utterance.voice = currentVoice;
            }

            utterance.rate = 0.8;
            utterance.pitch = 1.1;
            utterance.volume = 0.7;

            utterance.onstart = () => {
                console.log('🗣️ LOUNA pense à voix haute:', thoughtContent);
            };

            speechSynthesis.speak(utterance);
        }

        // 🌀 CHARGER LES PENSÉES CONTINUES MÖBIUS
        async function loadContinuousThoughts() {
            try {
                const response = await fetch('/api/thoughts/continuous');
                const data = await response.json();

                if (data.success && data.thoughts && data.thoughts.length > 0) {
                    const thoughtsList = document.getElementById('thoughtsList');
                    thoughtsList.innerHTML = '';

                    // METTRE À JOUR L'ÉTAT DE LA BANDE DE MÖBIUS
                    if (data.mobiusState) {
                        const position = (data.mobiusState.position * 100).toFixed(1);
                        const phase = data.mobiusState.phase;
                        const direction = data.mobiusState.direction > 0 ? '→' : '←';

                        document.getElementById('mobiusPosition').textContent = position + '%';
                        document.getElementById('mobiusPhase').textContent = phase;
                        document.getElementById('mobiusDirection').textContent = direction;
                        document.getElementById('mobiusProgress').style.width = position + '%';

                        if (data.chaosLevel !== undefined) {
                            document.getElementById('chaosLevel').textContent = (data.chaosLevel * 100).toFixed(0) + '%';
                        }

                        // Statut coloré selon la phase
                        const phaseColors = {
                            'exploration': '🔍 Exploration',
                            'analysis': '🧮 Analyse',
                            'synthesis': '⚗️ Synthèse',
                            'reflection': '🪞 Réflexion',
                            'integration': '🔗 Intégration',
                            'transformation': '🦋 Transformation',
                            'emergence': '✨ Émergence',
                            'convergence': '🎯 Convergence'
                        };
                        document.getElementById('mobiusStatus').textContent = phaseColors[phase] || phase;
                    }

                    // 🧠 METTRE À JOUR LA SANTÉ MENTALE
                    if (data.brainHealth) {
                        const health = data.brainHealth;
                        document.getElementById('fatigueLevel').textContent = (health.fatigue * 100).toFixed(0) + '%';
                        document.getElementById('stressLevel').textContent = (health.stress * 100).toFixed(0) + '%';

                        let mentalState = 'Actif';
                        let stateColor = '#00d4aa';

                        if (health.isDreaming) {
                            mentalState = '🌙 Rêve';
                            stateColor = '#9b59b6';
                        } else if (health.isResting) {
                            mentalState = '😴 Repos';
                            stateColor = '#3498db';
                        } else if (health.needsRest) {
                            mentalState = '😴 Fatigué';
                            stateColor = '#f39c12';
                        } else if (data.userInteractionActive) {
                            mentalState = '🎯 Focus';
                            stateColor = '#e74c3c';
                        } else if (data.thinkingMode === 'creative_chaos') {
                            mentalState = '🎨 Créatif';
                            stateColor = '#ff6b9d';
                        }

                        const stateElement = document.getElementById('mentalState');
                        stateElement.textContent = mentalState;
                        stateElement.style.color = stateColor;
                    }

                    // Vérifier s'il y a une nouvelle pensée à dire
                    const latestThought = data.thoughts[data.thoughts.length - 1];
                    if (voiceEnabled && latestThought && latestThought.id !== lastSpokenThought) {
                        speakThought(latestThought.content);
                        lastSpokenThought = latestThought.id;
                    }

                    // Afficher les 6 dernières pensées
                    data.thoughts.slice(-6).forEach(thought => {
                        const thoughtDiv = document.createElement('div');
                        thoughtDiv.className = 'thought-item';

                        // Icône selon le type
                        const typeIcons = {
                            'mobius_thought': '🌀',
                            'auto_dialogue': '🤖',
                            'rest_initiation': '😴',
                            'dream': '🌙',
                            'wake_up': '☀️',
                            'creative_chaos': '🎪',
                            'chatgpt_dialogue_start': '🌐',
                            'chatgpt_question': '❓',
                            'chatgpt_response_analysis': '🧠',
                            'automatic_inertia': '⚡',
                            'agent_surveillance': '🕵️'
                        };
                        const icon = typeIcons[thought.type] || '🧠';

                        thoughtDiv.innerHTML = `
                            <div class="thought-time">[${thought.time}] ${icon} ${thought.type}</div>
                            <div class="thought-content">${thought.content}</div>
                        `;
                        thoughtsList.appendChild(thoughtDiv);
                    });

                } else {
                    // Message par défaut
                    const thoughtsList = document.getElementById('thoughtsList');
                    thoughtsList.innerHTML = `
                        <div class="thought-item">
                            <div class="thought-time">Bande de Möbius active</div>
                            <div class="thought-content">🌀 LOUNA AI pense en continu...</div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erreur chargement pensées:', error);
            }
        }

        // 🌐 DÉMARRER DIALOGUE CHATGPT
        async function startChatGPTDialogue() {
            try {
                const response = await fetch('/api/start-chatgpt-dialogue', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                if (data.success) {
                    console.log('🌐 Dialogue ChatGPT démarré !');
                    document.getElementById('chatgptBtn').textContent = '🌐 Actif';
                    document.getElementById('chatgptBtn').style.background = 'linear-gradient(135deg, #27ae60, #2ecc71)';

                    // Afficher la zone d'apprentissage
                    document.getElementById('chatgptLearningZone').style.display = 'block';
                    addMessage('🌐 Dialogue ChatGPT démarré ! Vous pouvez maintenant coller les réponses de ChatGPT dans la zone d\'apprentissage ci-dessus.', 'ai');
                } else {
                    console.error('❌ Erreur dialogue ChatGPT:', data.error);
                    addMessage(`❌ Erreur dialogue ChatGPT: ${data.error}`, 'ai');
                }
            } catch (error) {
                console.error('❌ Erreur démarrage ChatGPT:', error);
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        // 🧠 SOUMETTRE RÉPONSE CHATGPT POUR APPRENTISSAGE
        async function submitChatGPTResponse() {
            const responseText = document.getElementById('chatgptResponseInput').value.trim();

            if (!responseText) {
                addMessage('❌ Veuillez coller une réponse de ChatGPT avant d\'analyser.', 'ai');
                return;
            }

            try {
                addMessage('🧠 Analyse de la réponse ChatGPT en cours...', 'ai');

                const response = await fetch('/api/chatgpt-response', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ response: responseText })
                });

                const data = await response.json();

                if (data.success) {
                    // Afficher le résultat de l'apprentissage
                    document.getElementById('learningResult').style.display = 'block';
                    document.getElementById('learningDetails').innerHTML = `
                        <div style="color: #00d4aa;">✅ Réponse analysée avec succès !</div>
                        <div style="margin-top: 5px; font-size: 10px;">Question: ${data.question || 'N/A'}</div>
                    `;

                    if (data.nextQuestion) {
                        document.getElementById('nextQuestionText').textContent = data.nextQuestion;
                    }

                    addMessage(`🧠 Apprentissage terminé ! LOUNA a analysé la réponse de ChatGPT et généré une nouvelle question de suivi.`, 'ai');

                    // Effacer le textarea
                    document.getElementById('chatgptResponseInput').value = '';
                } else {
                    addMessage(`❌ Erreur analyse: ${data.message}`, 'ai');
                }
            } catch (error) {
                console.error('❌ Erreur soumission ChatGPT:', error);
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        // 🗑️ EFFACER LA ZONE CHATGPT
        function clearChatGPTResponse() {
            document.getElementById('chatgptResponseInput').value = '';
            document.getElementById('learningResult').style.display = 'none';
            addMessage('🗑️ Zone d\'apprentissage effacée.', 'ai');
        }

        // 🎨 GÉNÉRATEURS DE MÉDIAS
        async function generateVideo() {
            try {
                addMessage('🎬 Génération de vidéo LT X en cours...', 'ai');
                const response = await fetch('/api/generate-media', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'video', model: 'luma-dream-machine' })
                });
                const data = await response.json();

                if (data.success) {
                    addMessage(`🎬 Vidéo générée avec succès ! ${data.url || 'Prête à télécharger'}`, 'ai');
                } else {
                    addMessage(`❌ Erreur génération vidéo: ${data.error}`, 'ai');
                }
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        async function generateImage() {
            try {
                addMessage('🖼️ Génération d\'image en cours...', 'ai');
                const response = await fetch('/api/generate-media', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'image', model: 'dall-e-3' })
                });
                const data = await response.json();

                if (data.success) {
                    addMessage(`🖼️ Image générée avec succès ! ${data.url || 'Prête à télécharger'}`, 'ai');
                } else {
                    addMessage(`❌ Erreur génération image: ${data.error}`, 'ai');
                }
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        async function generateAudio() {
            try {
                addMessage('🎵 Génération d\'audio en cours...', 'ai');
                const response = await fetch('/api/generate-media', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'audio', model: 'elevenlabs' })
                });
                const data = await response.json();

                if (data.success) {
                    addMessage(`🎵 Audio généré avec succès ! ${data.url || 'Prêt à télécharger'}`, 'ai');
                } else {
                    addMessage(`❌ Erreur génération audio: ${data.error}`, 'ai');
                }
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        async function generateMusic() {
            try {
                addMessage('🎼 Génération de musique en cours...', 'ai');
                const response = await fetch('/api/generate-media', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'music', model: 'suno-ai' })
                });
                const data = await response.json();

                if (data.success) {
                    addMessage(`🎼 Musique générée avec succès ! ${data.url || 'Prête à télécharger'}`, 'ai');
                } else {
                    addMessage(`❌ Erreur génération musique: ${data.error}`, 'ai');
                }
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        // 🔧 OUTILS DE DIAGNOSTIC
        function openDiagnostic() {
            window.open('/diagnostic.html', '_blank');
        }

        function openBrainVisualization() {
            window.open('/brain-3d.html', '_blank');
        }

        function openThermalDashboard() {
            window.open('/thermal-dashboard.html', '_blank');
        }

        // ⚡ CHECK SYSTÈME COMPLET
        async function runSystemCheck() {
            try {
                addMessage('⚡ Vérification système en cours...', 'ai');

                const response = await fetch('/api/system-check', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                if (data.success) {
                    addMessage(`✅ Check système terminé ! Neurones: ${data.neurons}, QI: ${data.qi}, Mémoire: ${data.memoryEntries} entrées`, 'ai');
                } else {
                    addMessage(`❌ Erreur check système: ${data.error}`, 'ai');
                }
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        // 💾 EXPORT DES DONNÉES DU CERVEAU
        async function exportBrainData() {
            try {
                addMessage('💾 Export des données du cerveau...', 'ai');

                const response = await fetch('/api/export-brain');
                const data = await response.json();

                if (data.success) {
                    // Créer un fichier de téléchargement
                    const blob = new Blob([JSON.stringify(data.brainData, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `louna-brain-${new Date().toISOString().split('T')[0]}.json`;
                    a.click();
                    URL.revokeObjectURL(url);

                    addMessage('💾 Données du cerveau exportées avec succès !', 'ai');
                } else {
                    addMessage(`❌ Erreur export: ${data.error}`, 'ai');
                }
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        // ⏸️ PAUSE/REPRENDRE LES PENSÉES
        let thoughtsPaused = false;
        async function pauseThoughts() {
            try {
                const action = thoughtsPaused ? 'resume' : 'pause';
                const response = await fetch('/api/thoughts/control', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: action })
                });
                const data = await response.json();

                if (data.success) {
                    thoughtsPaused = !thoughtsPaused;
                    const btn = document.getElementById('pauseBtn');
                    if (thoughtsPaused) {
                        btn.textContent = '▶️ Reprendre';
                        btn.style.background = 'linear-gradient(135deg, #27ae60, #2ecc71)';
                        addMessage('⏸️ Pensées mises en pause.', 'ai');
                    } else {
                        btn.textContent = '⏸️ Pause Pensées';
                        btn.style.background = 'linear-gradient(135deg, #f39c12, #e67e22)';
                        addMessage('▶️ Pensées reprises.', 'ai');
                    }
                } else {
                    addMessage(`❌ Erreur contrôle pensées: ${data.error}`, 'ai');
                }
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        // 🎨 BOOST CRÉATIF
        async function boostCreativity() {
            try {
                addMessage('🎨 Activation du boost créatif...', 'ai');

                const response = await fetch('/api/boost-creativity', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                if (data.success) {
                    addMessage('🎨 Boost créatif activé ! Niveau de chaos créatif augmenté.', 'ai');
                } else {
                    addMessage(`❌ Erreur boost créatif: ${data.error}`, 'ai');
                }
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        // 🎯 FOCUS PROFOND
        async function deepFocus() {
            try {
                addMessage('🎯 Activation du focus profond...', 'ai');

                const response = await fetch('/api/deep-focus', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                if (data.success) {
                    addMessage('🎯 Focus profond activé ! Concentration maximale.', 'ai');
                } else {
                    addMessage(`❌ Erreur focus profond: ${data.error}`, 'ai');
                }
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        // 😴 MODE REPOS
        async function restMode() {
            try {
                addMessage('😴 Activation du mode repos...', 'ai');

                const response = await fetch('/api/rest-mode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                if (data.success) {
                    addMessage('😴 Mode repos activé. LOUNA va se reposer.', 'ai');
                } else {
                    addMessage(`❌ Erreur mode repos: ${data.error}`, 'ai');
                }
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'ai');
            }
        }

        // 🔄 INITIALISATION
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Interface Electron Ultra-Autonome chargée');
            initializeVoice();
            updateStats();
            loadContinuousThoughts();

            // Mettre à jour les stats toutes les 5 secondes
            setInterval(updateStats, 5000);

            // Charger les pensées continues toutes les 2 secondes
            setInterval(loadContinuousThoughts, 2000);
        });
    </script>
</body>
</html>
