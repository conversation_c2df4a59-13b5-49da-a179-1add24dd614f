<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formation Linguistique - Agent à Mémoire Thermique</title>
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/neural-animation.css">
    <link rel="stylesheet" href="/css/kyber-styles.css">
    <link rel="stylesheet" href="/css/voice-interface.css">
    <link rel="stylesheet" href="/css/sidebar-layout.css">
    <link rel="stylesheet" href="/css/sensory-memory.css">
    <link rel="stylesheet" href="/css/language-training.css">
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
    <div class="app-container training-app">
        <header class="training-header">
            <div class="logo">
                <i class="fas fa-brain"></i>
                <h1>Formation Linguistique de l'Agent</h1>
            </div>
            <div class="actions">
                <a href="/interface-spectaculaire.html" class="back-button"><i class="fas fa-arrow-left"></i> Retour à l'Agent</a>
            </div>
        </header>

        <main class="training-content">
            <div class="training-sections">
                <!-- Section Personnalité -->
                <section class="training-section" id="personality-section">
                    <h2><i class="fas fa-user-gear"></i> Personnalité Linguistique</h2>
                    
                    <div class="personality-settings">
                        <div class="personality-blend">
                            <h3>Composition de la personnalité</h3>
                            
                            <div class="slider-group">
                                <label for="savant-slider">
                                    Savant
                                    <span class="slider-value" id="savant-value">70%</span>
                                </label>
                                <input type="range" id="savant-slider" class="personality-slider" min="0" max="100" step="5" value="70">
                            </div>
                            
                            <div class="slider-group">
                                <label for="pedagogue-slider">
                                    Pédagogue
                                    <span class="slider-value" id="pedagogue-value">60%</span>
                                </label>
                                <input type="range" id="pedagogue-slider" class="personality-slider" min="0" max="100" step="5" value="60">
                            </div>
                            
                            <div class="slider-group">
                                <label for="creative-slider">
                                    Créatif
                                    <span class="slider-value" id="creative-value">50%</span>
                                </label>
                                <input type="range" id="creative-slider" class="personality-slider" min="0" max="100" step="5" value="50">
                            </div>
                        </div>
                        
                        <div class="emotional-traits">
                            <h3>Traits émotionnels</h3>
                            
                            <div class="slider-group">
                                <label for="enthusiasm-slider">
                                    Enthousiasme
                                    <span class="slider-value" id="enthusiasm-value">60%</span>
                                </label>
                                <input type="range" id="enthusiasm-slider" class="emotion-slider" min="0" max="100" step="5" value="60">
                            </div>
                            
                            <div class="slider-group">
                                <label for="curiosity-slider">
                                    Curiosité
                                    <span class="slider-value" id="curiosity-value">80%</span>
                                </label>
                                <input type="range" id="curiosity-slider" class="emotion-slider" min="0" max="100" step="5" value="80">
                            </div>
                            
                            <div class="slider-group">
                                <label for="empathy-slider">
                                    Empathie
                                    <span class="slider-value" id="empathy-value">70%</span>
                                </label>
                                <input type="range" id="empathy-slider" class="emotion-slider" min="0" max="100" step="5" value="70">
                            </div>
                            
                            <div class="slider-group">
                                <label for="humor-slider">
                                    Humour
                                    <span class="slider-value" id="humor-value">40%</span>
                                </label>
                                <input type="range" id="humor-slider" class="emotion-slider" min="0" max="100" step="5" value="40">
                            </div>
                        </div>
                        
                        <div class="communication-style">
                            <h3>Style de communication</h3>
                            
                            <div class="style-grid">
                                <div class="style-item">
                                    <span>Formel</span>
                                    <div class="dual-slider-container">
                                        <input type="range" id="formality-slider" class="dual-slider" min="0" max="100" step="5" value="60">
                                    </div>
                                    <span>Informel</span>
                                </div>
                                
                                <div class="style-item">
                                    <span>Simple</span>
                                    <div class="dual-slider-container">
                                        <input type="range" id="complexity-slider" class="dual-slider" min="0" max="100" step="5" value="70">
                                    </div>
                                    <span>Complexe</span>
                                </div>
                                
                                <div class="style-item">
                                    <span>Direct</span>
                                    <div class="dual-slider-container">
                                        <input type="range" id="directness-slider" class="dual-slider" min="0" max="100" step="5" value="50">
                                    </div>
                                    <span>Nuancé</span>
                                </div>
                                
                                <div class="style-item">
                                    <span>Concis</span>
                                    <div class="dual-slider-container">
                                        <input type="range" id="verbosity-slider" class="dual-slider" min="0" max="100" step="5" value="60">
                                    </div>
                                    <span>Détaillé</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="preset-personalities">
                        <h3>Préréglages de personnalité</h3>
                        
                        <div class="presets-grid">
                            <button class="preset-button" data-preset="savant-scholar">
                                <i class="fas fa-graduation-cap"></i>
                                <span>Savant Académique</span>
                            </button>
                            
                            <button class="preset-button" data-preset="helpful-guide">
                                <i class="fas fa-hands-helping"></i>
                                <span>Guide Bienveillant</span>
                            </button>
                            
                            <button class="preset-button" data-preset="creative-explorer">
                                <i class="fas fa-lightbulb"></i>
                                <span>Explorateur Créatif</span>
                            </button>
                            
                            <button class="preset-button" data-preset="balanced">
                                <i class="fas fa-balance-scale"></i>
                                <span>Équilibré</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="apply-personality">
                        <button id="apply-personality-btn" class="primary-button">
                            <i class="fas fa-check"></i> Appliquer les changements
                        </button>
                        <button id="reset-personality-btn" class="secondary-button">
                            <i class="fas fa-undo"></i> Réinitialiser
                        </button>
                    </div>
                </section>
                
                <!-- Section Apprentissage -->
                <section class="training-section" id="learning-section">
                    <h2><i class="fas fa-graduation-cap"></i> Apprentissage Linguistique</h2>
                    
                    <div class="learning-stats-container">
                        <div class="learning-stat-box">
                            <div class="stat-circle" id="phrases-stat">
                                <span class="stat-number" id="phrases-count">0</span>
                                <span class="stat-label">Phrases</span>
                            </div>
                            <div class="stat-description">Expressions et tournures uniques apprises</div>
                        </div>
                        
                        <div class="learning-stat-box">
                            <div class="stat-circle" id="patterns-stat">
                                <span class="stat-number" id="patterns-count">0</span>
                                <span class="stat-label">Modèles</span>
                            </div>
                            <div class="stat-description">Structures grammaticales maîtrisées</div>
                        </div>
                        
                        <div class="learning-stat-box">
                            <div class="stat-circle" id="interactions-stat">
                                <span class="stat-number" id="interactions-count">0</span>
                                <span class="stat-label">Interactions</span>
                            </div>
                            <div class="stat-description">Échanges analysés pour l'apprentissage</div>
                        </div>
                    </div>
                    
                    <div class="learning-progress">
                        <h3>Progression de l'apprentissage linguistique</h3>
                        <div class="progress-chart-container">
                            <canvas id="learning-progress-chart"></canvas>
                        </div>
                    </div>
                    
                    <div class="manual-training">
                        <h3>Entraînement manuel</h3>
                        
                        <div class="training-form">
                            <div class="form-group">
                                <label for="example-type">Type d'exemple</label>
                                <select id="example-type">
                                    <option value="expression">Expression idiomatique</option>
                                    <option value="sentence-structure">Structure de phrase</option>
                                    <option value="emotional">Expression émotionnelle</option>
                                    <option value="transition">Marqueur de transition</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="example-content">Contenu de l'exemple</label>
                                <textarea id="example-content" placeholder="Entrez ici une expression, structure de phrase ou autre exemple que l'agent devrait apprendre à utiliser..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="example-context">Contexte d'utilisation (optionnel)</label>
                                <input type="text" id="example-context" placeholder="Dans quel contexte cette expression est-elle appropriée ?">
                            </div>
                            
                            <button id="add-example-btn" class="primary-button">
                                <i class="fas fa-plus"></i> Ajouter cet exemple
                            </button>
                        </div>
                    </div>
                </section>
                
                <!-- Section Test -->
                <section class="training-section" id="test-section">
                    <h2><i class="fas fa-vial"></i> Zone de test</h2>
                    
                    <div class="test-scenario">
                        <h3>Tester la réponse humanisée</h3>
                        
                        <div class="original-response-container">
                            <h4>Réponse originale (style machine)</h4>
                            <textarea id="original-response" placeholder="Entrez ici une réponse au style mécanique que vous souhaitez humaniser..."></textarea>
                        </div>
                        
                        <button id="humanize-btn" class="primary-button">
                            <i class="fas fa-magic"></i> Humaniser cette réponse
                        </button>
                        
                        <div class="humanized-response-container" id="humanized-response-box" style="display: none;">
                            <h4>Réponse humanisée</h4>
                            <div id="humanized-response" class="response-display"></div>
                            
                            <div class="transformation-details">
                                <h5>Transformations appliquées</h5>
                                <ul id="transformation-list" class="transformation-list"></ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="voice-test">
                        <h3>Test de la voix humanisée</h3>
                        
                        <p>Écoutez comment l'agent prononce le texte avec sa personnalité actuelle.</p>
                        
                        <div class="voice-controls">
                            <button id="play-voice-btn" class="voice-control-btn">
                                <i class="fas fa-play"></i> Écouter
                            </button>
                            <button id="stop-voice-btn" class="voice-control-btn">
                                <i class="fas fa-stop"></i> Arrêter
                            </button>
                        </div>
                    </div>
                </section>
            </div>
        </main>
        
        <div class="save-feedback" id="save-feedback"></div>
    </div>
    
    <script src="/js/startup-handler.js"></script>
    <script src="/js/human-language-trainer.js"></script>
    <script src="/js/voice-interface.js"></script>
    <script src="/js/language-training-ui.js"></script>
</body>
</html>
