<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 LOUNA AI - Application Complète</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav-container {
            background: rgba(0, 0, 0, 0.1);
            padding: 15px;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #764ba2, #667eea);
        }

        .nav-btn.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .main-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .interface-frame {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 70vh;
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-active { background: #4ade80; }
        .status-warning { background: #fbbf24; }
        .status-error { background: #ef4444; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .chat-interface {
            display: grid;
            grid-template-rows: 1fr auto;
            height: 60vh;
            gap: 20px;
        }

        .chat-messages {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 20px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message-info {
            font-size: 0.8em;
            opacity: 0.7;
            margin-top: 5px;
        }

        .input-container {
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 0 0 15px 15px;
        }

        .input-row {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }

        .input-controls {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 15px 20px;
            color: white;
            font-size: 16px;
            min-width: 300px;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .message-input:focus {
            border-color: #c084fc;
            box-shadow: 0 0 10px rgba(192, 132, 252, 0.3);
            outline: none;
        }

        .send-btn {
            background: linear-gradient(45deg, #c084fc, #f472b6);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(192, 132, 252, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .voice-btn {
            background: rgba(244, 114, 182, 0.2);
            border: 1px solid #f472b6;
            color: #f472b6;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .voice-btn:hover {
            background: rgba(244, 114, 182, 0.4);
            transform: scale(1.1);
        }

        .voice-btn.recording {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            animation: pulse 1s infinite;
        }

        .control-btn {
            background: rgba(192, 132, 252, 0.2);
            border: 1px solid #c084fc;
            color: #c084fc;
            padding: 8px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .control-btn:hover {
            background: rgba(192, 132, 252, 0.4);
            transform: translateY(-1px);
        }

        .control-btn.active {
            background: linear-gradient(45deg, #c084fc, #f472b6);
            color: white;
        }

        .camera-preview {
            width: 200px;
            height: 150px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #c084fc;
            border-radius: 10px;
            margin: 10px 0;
            display: none;
            object-fit: cover;
        }

        .auto-mode-panel {
            background: rgba(192, 132, 252, 0.1);
            border: 1px solid #c084fc;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            display: none;
        }

        .auto-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .auto-input {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid #c084fc;
            border-radius: 5px;
            padding: 5px 10px;
            color: #c084fc;
            width: 80px;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .code-container {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #c084fc;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }

        .code-header {
            background: rgba(192, 132, 252, 0.2);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #c084fc;
        }

        .code-title {
            color: #c084fc;
            font-weight: bold;
            font-size: 14px;
        }

        .code-content {
            padding: 15px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            color: #e5e7eb;
            font-size: 14px;
            line-height: 1.5;
        }

        .copy-btn {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid #10b981;
            color: #10b981;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: rgba(16, 185, 129, 0.4);
            transform: translateY(-1px);
        }

        .copy-btn.copied {
            background: rgba(16, 185, 129, 0.6);
            color: white;
        }

        .app-btn {
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid #3b82f6;
            color: #3b82f6;
            padding: 8px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .app-btn:hover {
            background: rgba(59, 130, 246, 0.4);
            transform: translateY(-1px);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }

        .status-speaking {
            background: #f59e0b;
            animation: pulse 1s infinite;
        }

        .status-listening {
            background: #ef4444;
            animation: pulse 1s infinite;
        }

        .status-processing {
            background: #8b5cf6;
            animation: pulse 1s infinite;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
            text-shadow: 0 0 10px currentColor;
        }

        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Styles pour les boutons de sécurité */
        .security-button {
            background: linear-gradient(135deg, #FF6B6B, #FF5252);
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .security-button.connect {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .security-button.emergency {
            background: linear-gradient(135deg, #F44336, #D32F2F);
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4);
            animation: pulse-emergency 2s infinite;
        }

        @keyframes pulse-emergency {
            0%, 100% { box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4); }
            50% { box-shadow: 0 4px 25px rgba(244, 67, 54, 0.8); }
        }

        .security-button.antivirus {
            background: linear-gradient(135deg, #9C27B0, #7B1FA2);
            box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
        }

        .security-button.clean {
            background: linear-gradient(135deg, #00BCD4, #0097A7);
            box-shadow: 0 4px 15px rgba(0, 188, 212, 0.3);
        }

        .security-button.status {
            background: linear-gradient(135deg, #607D8B, #455A64);
            box-shadow: 0 4px 15px rgba(96, 125, 139, 0.3);
        }

        .security-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        /* Styles pour les popups et overlays */
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }

        .popup-content {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            border-radius: 15px;
            padding: 30px;
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            color: white;
            position: relative;
        }

        .popup-content h2 {
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.5em;
        }

        .close-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 20px;
            display: block;
            margin-left: auto;
            margin-right: auto;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .label {
            font-weight: bold;
            color: #c084fc;
        }

        .value {
            color: #4ade80;
            font-weight: bold;
        }

        .status-clean { color: #4ade80; }
        .status-infected { color: #ef4444; }
        .status-cleaned { color: #f59e0b; }

        .threat-low { color: #4ade80; }
        .threat-medium { color: #f59e0b; }
        .threat-high { color: #ef4444; }

        .active { color: #4ade80; }
        .inactive { color: #ef4444; }

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        /* Masquer le code JavaScript qui pourrait s'afficher */
        .code-leak {
            display: none !important;
            visibility: hidden !important;
        }

        /* Masquer tout texte qui ressemble à du code JavaScript */
        body *:not(script):not(style) {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        }

        /* Masquer les éléments contenant du code JavaScript visible */
        *:contains("function"),
        *:contains("async function"),
        *:contains("showNotification"),
        *:contains("catch (error)"),
        *:contains("const response"),
        *:contains("await fetch") {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
        }

        /* Style pour masquer le code qui s'affiche */
        .interface-frame *:not(.nav-btn):not(.security-button):not(.control-btn):not(.send-btn):not(.voice-btn):not(.app-btn):not(.test-btn):not(.close-btn):not(.copy-btn) {
            white-space: normal !important;
        }

        /* Masquer spécifiquement les erreurs de code qui s'affichent */
        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            z-index: -1;
        }

        /* Nettoyer l'affichage des erreurs */
        .error-text,
        .js-error,
        .code-error {
            display: none !important;
            visibility: hidden !important;
        }

        /* Masquer spécifiquement le code JavaScript qui s'affiche */
        body *:not(script):not(style):not(.nav-btn):not(.security-button):not(.control-btn):not(.send-btn):not(.voice-btn):not(.app-btn):not(.test-btn):not(.close-btn):not(.copy-btn):not(h1):not(h2):not(h3):not(p):not(.status-item):not(.metric-value):not(.metric-label) {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            font-size: inherit !important;
            color: inherit !important;
        }

        /* Masquer les éléments avec du texte de code long */
        *[style*="font-family: monospace"],
        *[style*="font-family: courier"],
        *[style*="font-family: 'Courier New'"] {
            display: none !important;
        }

        /* Masquer les éléments contenant des mots-clés de code */
        body *:not(script):not(style) {
            white-space: normal !important;
        }

        /* Style pour forcer l'affichage normal */
        .interface-frame,
        .interface-frame * {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            white-space: normal !important;
        }

        /* Masquer tout ce qui ressemble à du code */
        pre, code {
            display: none !important;
        }

        /* Forcer le style normal pour tous les textes */
        body, body * {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        }

        /* Styles pour les notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10001;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        /* Styles pour les sections de capacités */
        .capability-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .capability-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .app-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .app-item {
            background: rgba(255, 255, 255, 0.05);
            margin: 8px 0;
            padding: 12px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-left: 3px solid #667eea;
        }

        .test-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.4);
        }
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
        }

        /* Styles pour les statuts de sécurité */
        .security-status .status-item,
        .cleanup-results .result-item,
        .scan-results .result-item,
        .clean-results .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            border-left: 4px solid #2196F3;
        }

        .value.connected { background: #4CAF50; color: white; }
        .value.disconnected { background: #F44336; color: white; }
        .value.emergency { background: #FF5722; color: white; animation: blink 1s infinite; }
        .value.normal { background: #4CAF50; color: white; }
        .value.active { background: #2196F3; color: white; }
        .value.inactive { background: #9E9E9E; color: white; }

        .value.threat-low { background: #4CAF50; color: white; }
        .value.threat-medium { background: #FF9800; color: white; }
        .value.threat-high { background: #F44336; color: white; animation: blink 1s infinite; }

        .value.status-clean { background: #4CAF50; color: white; }
        .value.status-infected { background: #F44336; color: white; }
        .value.status-cleaned { background: #FF9800; color: white; }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        /* Styles pour les popups */
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }

        .popup-content {
            background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
            border-radius: 15px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            border: 2px solid #333;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            color: white;
        }

        .popup-content h2 {
            color: #4ade80;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.5em;
        }

        .emergency-details,
        .training-details {
            margin: 20px 0;
        }

        .emergency-details p,
        .training-details .result-item {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            border-left: 4px solid #4ade80;
        }

        .close-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 20px;
            width: 100%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                align-items: center;
            }

            .nav-btn {
                width: 100%;
                max-width: 300px;
                text-align: center;
            }

            .metrics-grid {
                grid-template-columns: 1fr 1fr;
            }

            .message {
                max-width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 LOUNA AI</h1>
        <p>Intelligence Artificielle avec Mémoire Thermique Vivante</p>
        <div class="navigation-buttons" style="margin-top: 15px;">
            <button onclick="window.location.href='/thoughts-monitor.html'" class="nav-btn" style="margin: 5px;">💭 Pensées Complètes</button>
            <button onclick="window.location.href='/evolution-tracker.html'" class="nav-btn" style="margin: 5px;">📈 Évolution Tracker</button>
            <button onclick="window.location.href='/brain-monitoring-complete.html'" class="nav-btn" style="margin: 5px;">🧠 Monitoring Cerveau</button>
            <button onclick="window.location.href='/brain-visualization.html'" class="nav-btn" style="margin: 5px;">🎨 Visualisation 3D</button>
            <button onclick="showMemoriesSection()" class="nav-btn" style="margin: 5px;">🧠 Mémoires Détaillées</button>
            <button onclick="showSystemDiagnostics()" class="nav-btn" style="margin: 5px;">🔧 Diagnostics Système</button>
            <button onclick="optimizePerformance()" class="nav-btn" style="margin: 5px;">⚡ Optimiser Performance</button>
            <button onclick="togglePhysicalMemory()" class="nav-btn" style="margin: 5px;" id="memoryToggleBtn">🧠 Activer Mémoire Physique</button>
            <button onclick="testMCPSafari()" class="nav-btn" style="margin: 5px;">🌐 Test MCP Safari</button>
            <button onclick="showMCPCapabilities()" class="nav-btn" style="margin: 5px;">🔧 Capacités MCP</button>
            <button onclick="performFullMachineScan()" class="nav-btn" style="margin: 5px;">🔍 Scanner Machine Complète</button>
            <button onclick="showMachineProfile()" class="nav-btn" style="margin: 5px;">🖥️ Profil Machine Appris</button>
            <button onclick="connectMemorySecurely()" class="nav-btn" style="margin: 5px;">🔌 Connexion Sécurisée</button>
            <button onclick="emergencyShutdown()" class="nav-btn" style="margin: 5px;">🚨 Coupure d'Urgence</button>
            <button onclick="scanMemoryAntivirus()" class="nav-btn" style="margin: 5px;">🦠 Scan Antivirus</button>
            <button onclick="cleanMemorySecurely()" class="nav-btn" style="margin: 5px;">🧹 Nettoyage Sécurisé</button>
            <button onclick="showSecurityStatus()" class="nav-btn" style="margin: 5px;">🛡️ Statut Sécurité</button>
            <button onclick="cleanErrorLogs()" class="nav-btn" style="margin: 5px;">🔧 Nettoyer Logs</button>
            <button onclick="scanRepairLogs()" class="nav-btn" style="margin: 5px;">🔍 Scanner Logs</button>
            <button onclick="generateNeurons()" class="nav-btn" style="margin: 5px;">🧠 Générer Neurones</button>
            <button onclick="accelerateTraining()" class="nav-btn" style="margin: 5px;">⚡ Formation Accélérée</button>
        </div>
    </div>

    <div class="nav-container">
        <a href="/interface-spectaculaire.html" class="nav-btn active" onclick="showInterface('chat')">💬 Chat Principal</a>
        <a href="thoughts-monitor.html" class="nav-btn" target="_blank">💭 Pensées Complètes</a>
        <a href="evolution-tracker.html" class="nav-btn" target="_blank">📈 Évolution Intelligence</a>
        <a href="training-interface.html" class="nav-btn" target="_blank">⚡ Formation Agents</a>
        <a href="/thermal-memory-dashboard.html" class="nav-btn" target="_blank">🌡️ Mémoire Thermique</a>
        <a href="brain-visualization.html" class="nav-btn" target="_blank">🧠 Visualisation 3D</a>
        <a href="brain-monitoring-complete.html" class="nav-btn" target="_blank">📊 Monitoring Complet</a>
        <a href="test-interface.html" class="nav-btn" target="_blank">🔧 Interface Test</a>
    </div>

    <div class="main-container">
        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator status-active" id="server-status"></div>
                <span id="server-text">Serveur</span>
            </div>
            <div class="status-item">
                <div class="status-indicator status-active" id="memory-status"></div>
                <span id="memory-text">Mémoire Thermique</span>
            </div>
            <div class="status-item">
                <div class="status-indicator status-active" id="brain-status"></div>
                <span id="brain-text">Cerveau Artificiel</span>
            </div>
            <div class="status-item">
                <div class="status-indicator status-active" id="agent-status"></div>
                <span id="agent-text">DeepSeek R1</span>
            </div>
        </div>

        <div class="interface-frame">
            <div class="chat-interface">
                <div class="chat-messages" id="chatMessages">
                    <div class="message ai-message">
                        <div>🤖 Bonjour ! Je suis LOUNA AI avec mémoire thermique vivante et cerveau artificiel évolutif.</div>
                        <div class="message-info">Agent: DeepSeek R1-0528-8B • Neurones: 78 • Température: 77.8°C</div>
                    </div>
                </div>

                <div class="input-container">
                    <!-- Ligne principale d'entrée -->
                    <div class="input-row">
                        <button class="voice-btn" id="voiceBtn" onclick="toggleVoiceInput()" title="Reconnaissance vocale">
                            🎤
                        </button>
                        <input type="text" class="message-input" id="messageInput"
                               placeholder="Posez votre question à LOUNA AI ou parlez..."
                               onkeypress="handleKeyPress(event)">
                        <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                            <span id="sendBtnText">Envoyer</span>
                            <span class="status-indicator" id="statusIndicator" style="display: none;"></span>
                        </button>
                        <button class="voice-btn" id="speakBtn" onclick="toggleSpeech()" title="Synthèse vocale">
                            🔊
                        </button>
                    </div>

                    <!-- Contrôles avancés -->
                    <div class="input-controls">
                        <button class="control-btn" id="cameraBtn" onclick="toggleCamera()">📹 Caméra</button>
                        <button class="control-btn" id="codeBtn" onclick="toggleCodeMode()">💻 Mode Code</button>
                        <button class="control-btn" id="autoBtn" onclick="toggleAutoMode()">🤖 Auto-Formation</button>
                        <button class="app-btn" onclick="openVSCode()">🖥️ Visual Studio</button>
                        <button class="app-btn" onclick="openApp('Terminal')">⚡ Terminal</button>
                        <button class="app-btn" onclick="openApp('Finder')">📁 Finder</button>
                    </div>

                    <!-- Prévisualisation caméra -->
                    <video class="camera-preview" id="cameraPreview" autoplay muted></video>

                    <!-- Panel mode automatique -->
                    <div class="auto-mode-panel" id="autoModePanel">
                        <div style="margin-bottom: 10px; color: #c084fc; font-weight: bold;">🤖 Mode Formation Automatique</div>
                        <div class="auto-controls">
                            <label style="color: #f472b6;">Sujet:</label>
                            <input type="text" class="auto-input" id="autoTopic" placeholder="ex: JavaScript" style="width: 150px;">

                            <label style="color: #f472b6;">Questions:</label>
                            <input type="number" class="auto-input" id="autoQuestions" value="5" min="1" max="20">

                            <label style="color: #f472b6;">Délai (s):</label>
                            <input type="number" class="auto-input" id="autoDelay" value="3" min="1" max="10">

                            <button class="control-btn" onclick="startAutoTraining()">🚀 Démarrer</button>
                            <button class="control-btn" onclick="stopAutoTraining()">🛑 Arrêter</button>

                            <span id="autoStatus" style="color: #10b981; margin-left: 10px;"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="metrics-grid" id="metricsGrid">
                <!-- Métriques chargées dynamiquement -->
            </div>
        </div>
    </div>

    <script>
        let isConnected = false;
        let currentMetrics = null;

        // 🧹 NETTOYEUR AUTOMATIQUE DE CODE AFFICHÉ
        function cleanDisplayedCode() {
            // Nettoyer tout texte qui ressemble à du code JavaScript
            const allElements = document.querySelectorAll('*:not(script):not(style)');
            allElements.forEach(element => {
                if (element.textContent && element.textContent.includes('function') &&
                    (element.textContent.includes('showNotification') ||
                     element.textContent.includes('catch (error)') ||
                     element.textContent.includes('await fetch') ||
                     element.textContent.includes('const response'))) {
                    element.style.display = 'none';
                    element.style.visibility = 'hidden';
                    element.style.opacity = '0';
                    element.style.height = '0';
                    element.style.overflow = 'hidden';
                }
            });

            // Nettoyer les éléments avec du code JavaScript visible
            const codePatterns = [
                /function\s+\w+\s*\(/,
                /async\s+function/,
                /showNotification\(/,
                /catch\s*\(\s*error\s*\)/,
                /const\s+response\s*=/,
                /await\s+fetch\(/,
                /\.then\(/,
                /\.catch\(/
            ];

            document.querySelectorAll('*:not(script):not(style)').forEach(element => {
                const text = element.textContent || '';
                if (codePatterns.some(pattern => pattern.test(text))) {
                    element.style.display = 'none !important';
                    element.remove();
                }
            });
        }

        // 🔧 NETTOYEUR AVANCÉ POUR MASQUER LE CODE
        function advancedCodeCleaner() {
            // Masquer tous les éléments contenant du code JavaScript
            const textNodes = [];
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let node;
            while (node = walker.nextNode()) {
                if (node.textContent.includes('function') ||
                    node.textContent.includes('showNotification') ||
                    node.textContent.includes('catch') ||
                    node.textContent.includes('await') ||
                    node.textContent.includes('const response') ||
                    node.textContent.includes('async function')) {
                    textNodes.push(node);
                }
            }

            textNodes.forEach(node => {
                if (node.parentElement && !node.parentElement.matches('script, style')) {
                    node.parentElement.style.display = 'none';
                    node.parentElement.remove();
                }
            });

            // Nettoyer les éléments avec attributs suspects
            document.querySelectorAll('*').forEach(element => {
                if (element.textContent && element.textContent.length > 100 &&
                    (element.textContent.includes('function') || element.textContent.includes('catch'))) {
                    element.style.display = 'none';
                    element.remove();
                }
            });
        }

        // Exécuter le nettoyage au chargement et périodiquement
        document.addEventListener('DOMContentLoaded', () => {
            cleanDisplayedCode();
            advancedCodeCleaner();
        });

        setInterval(() => {
            cleanDisplayedCode();
            advancedCodeCleaner();
        }, 500); // Nettoyer toutes les 500ms

        // 🚨 NETTOYEUR RADICAL POUR MASQUER TOUT CODE
        function radicalCodeCleaner() {
            // Supprimer tous les éléments contenant du code JavaScript
            const allElements = document.querySelectorAll('*:not(script):not(style):not(.nav-btn):not(.security-button):not(.control-btn):not(.send-btn):not(.voice-btn):not(.app-btn):not(.test-btn):not(.close-btn):not(.copy-btn):not(h1):not(h2):not(h3):not(p):not(.status-item):not(.metric-value):not(.metric-label):not(.header):not(.nav-container):not(.main-container):not(.interface-frame)');

            allElements.forEach(element => {
                const text = element.textContent || '';

                // Vérifier si l'élément contient du code JavaScript
                if (text.length > 50 && (
                    text.includes('function') ||
                    text.includes('showNotification') ||
                    text.includes('catch (error)') ||
                    text.includes('await fetch') ||
                    text.includes('const response') ||
                    text.includes('async function') ||
                    text.includes('.then(') ||
                    text.includes('.catch(') ||
                    text.includes('try {') ||
                    text.includes('} catch') ||
                    text.includes('document.') ||
                    text.includes('window.') ||
                    text.includes('console.') ||
                    text.includes('alert(') ||
                    text.includes('JSON.stringify') ||
                    text.includes('JSON.parse')
                )) {
                    // Masquer complètement l'élément
                    element.style.display = 'none !important';
                    element.style.visibility = 'hidden !important';
                    element.style.opacity = '0 !important';
                    element.style.height = '0 !important';
                    element.style.width = '0 !important';
                    element.style.overflow = 'hidden !important';
                    element.style.position = 'absolute !important';
                    element.style.left = '-9999px !important';
                    element.style.top = '-9999px !important';

                    // Supprimer complètement l'élément
                    try {
                        element.remove();
                    } catch (e) {
                        // Si on ne peut pas le supprimer, le vider
                        element.innerHTML = '';
                        element.textContent = '';
                    }
                }
            });

            // Nettoyer spécifiquement les éléments avec du texte de code
            document.querySelectorAll('*').forEach(element => {
                if (element.textContent && element.textContent.includes('❌ Erreur') && element.textContent.includes('function')) {
                    element.remove();
                }
            });
        }

        // Nettoyer immédiatement et de manière agressive
        setTimeout(() => {
            cleanDisplayedCode();
            advancedCodeCleaner();
            radicalCodeCleaner();
        }, 100);

        setTimeout(() => {
            radicalCodeCleaner();
        }, 500);

        setTimeout(() => {
            radicalCodeCleaner();
        }, 1000);

        // 👁️ OBSERVATEUR DE MUTATIONS POUR DÉTECTER NOUVEAU CODE
        const codeObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.TEXT_NODE || node.nodeType === Node.ELEMENT_NODE) {
                            const text = node.textContent || '';
                            if (text.includes('function') || text.includes('showNotification') || text.includes('catch (error)')) {
                                // Nouveau code détecté, le supprimer immédiatement
                                setTimeout(() => {
                                    radicalCodeCleaner();
                                }, 10);
                            }
                        }
                    });
                }
            });
        });

        // Démarrer l'observation
        codeObserver.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });

        // Variables pour les nouvelles fonctionnalités
        let speechSynthesis = window.speechSynthesis;
        let speechRecognition = null;
        let isListening = false;
        let isSpeaking = false;
        let cameraStream = null;
        let isCameraActive = false;
        let isCodeMode = false;
        let isAutoMode = false;
        let autoTrainingInterval = null;
        let autoQuestionCount = 0;

        // Vérifier la connexion et charger les métriques
        async function checkConnection() {
            try {
                console.log('🔍 Vérification connexion serveur...');
                const response = await fetch('/api/metrics');
                const data = await response.json();

                if (data.success) {
                    isConnected = true;
                    currentMetrics = data.metrics;
                    updateStatusBar(true);
                    updateMetrics(data.metrics);
                    console.log('✅ Connexion serveur établie');

                    // Activer le bouton d'envoi
                    const sendBtn = document.getElementById('sendBtn');
                    if (sendBtn) {
                        sendBtn.disabled = false;
                        sendBtn.style.opacity = '1';
                    }
                } else {
                    throw new Error('Serveur non disponible');
                }
            } catch (error) {
                isConnected = false;
                updateStatusBar(false);
                console.error('❌ Erreur connexion:', error);

                // Désactiver le bouton d'envoi
                const sendBtn = document.getElementById('sendBtn');
                if (sendBtn) {
                    sendBtn.disabled = true;
                    sendBtn.style.opacity = '0.5';
                }
            }
        }

        // Mettre à jour la barre de statut
        function updateStatusBar(connected) {
            const serverStatus = document.getElementById('server-status');
            const serverText = document.getElementById('server-text');
            
            if (connected) {
                serverStatus.className = 'status-indicator status-active';
                serverText.textContent = 'Serveur Connecté';
                
                // Mettre à jour les autres statuts si on a les métriques
                if (currentMetrics) {
                    document.getElementById('memory-status').className = 'status-indicator status-active';
                    document.getElementById('memory-text').textContent = `Mémoire: ${currentMetrics.memoryStats?.totalMemories || 0} entrées`;
                    
                    document.getElementById('brain-status').className = 'status-indicator status-active';
                    document.getElementById('brain-text').textContent = `Cerveau: ${currentMetrics.brainStats?.activeNeurons || 0} neurones`;
                    
                    document.getElementById('agent-status').className = 'status-indicator status-active';
                    document.getElementById('agent-text').textContent = 'DeepSeek R1 Actif';
                }
            } else {
                serverStatus.className = 'status-indicator status-error';
                serverText.textContent = 'Serveur Déconnecté';
                
                document.getElementById('memory-status').className = 'status-indicator status-error';
                document.getElementById('brain-status').className = 'status-indicator status-error';
                document.getElementById('agent-status').className = 'status-indicator status-error';
            }
        }

        // Mettre à jour les métriques avec QI en temps réel et évolution
        function updateMetrics(metrics) {
            const grid = document.getElementById('metricsGrid');

            // 🧠 UTILISER LE QI EN TEMPS RÉEL DES MÉTRIQUES
            const currentIQ = metrics.iqAnalysis || window.lastIQData;

            const metricsData = [
                {
                    label: 'QI Agent',
                    value: currentIQ?.agentIQ || 'N/A',
                    color: '#ff6b6b',
                    evolution: getEvolutionIndicator('agentIQ', currentIQ?.agentIQ)
                },
                {
                    label: 'QI Mémoire',
                    value: currentIQ?.memoryIQ || 'N/A',
                    color: '#60a5fa',
                    evolution: getEvolutionIndicator('memoryIQ', currentIQ?.memoryIQ)
                },
                {
                    label: 'QI Combiné',
                    value: currentIQ?.combinedIQ || 'N/A',
                    color: '#ff00ff',
                    evolution: getEvolutionIndicator('combinedIQ', currentIQ?.combinedIQ)
                },
                {
                    label: 'Neurones',
                    value: metrics.brainStats?.activeNeurons || 'N/A',
                    color: '#4ade80',
                    evolution: getEvolutionIndicator('neurons', metrics.brainStats?.activeNeurons)
                },
                {
                    label: 'Synapses',
                    value: metrics.brainStats?.synapticConnections || 'N/A',
                    color: '#fbbf24',
                    evolution: getEvolutionIndicator('synapses', metrics.brainStats?.synapticConnections)
                },
                {
                    label: 'Mémoires',
                    value: metrics.memoryStats?.totalMemories || 'N/A',
                    color: '#a78bfa',
                    evolution: getEvolutionIndicator('memories', metrics.memoryStats?.totalMemories)
                }
            ];

            grid.innerHTML = metricsData.map(metric => `
                <div class="metric-card">
                    <div class="metric-value" style="color: ${metric.color}">
                        ${metric.value}
                        ${metric.evolution}
                    </div>
                    <div class="metric-label">${metric.label}</div>
                </div>
            `).join('');

            // 🧠 SAUVEGARDER LE QI POUR L'ÉVOLUTION
            if (currentIQ) {
                window.lastIQData = currentIQ;
                // Sauvegarder dans localStorage pour les autres interfaces
                localStorage.setItem('lounaIQData', JSON.stringify(currentIQ));
            }
        }

        // Indicateur d'évolution
        function getEvolutionIndicator(key, currentValue) {
            if (!window.previousMetrics || currentValue === undefined || currentValue === 'N/A') {
                return '';
            }

            let previousValue;
            switch(key) {
                case 'agentIQ':
                    previousValue = window.previousMetrics.agentIQ;
                    break;
                case 'memoryIQ':
                    previousValue = window.previousMetrics.memoryIQ;
                    break;
                case 'combinedIQ':
                    previousValue = window.previousMetrics.combinedIQ;
                    break;
                case 'neurons':
                    previousValue = window.previousMetrics.neurons;
                    break;
                case 'synapses':
                    previousValue = window.previousMetrics.synapses;
                    break;
                case 'memories':
                    previousValue = window.previousMetrics.memories;
                    break;
                case 'temperature':
                    previousValue = window.previousMetrics.temperature;
                    break;
                default:
                    return '';
            }

            if (previousValue === undefined) return '';

            const diff = currentValue - previousValue;

            if (diff > 0) {
                return `<span style="color: #00ff00; font-size: 0.8em; margin-left: 5px;">↗ +${diff > 1 ? Math.round(diff) : diff.toFixed(1)}</span>`;
            } else if (diff < 0) {
                return `<span style="color: #ff0000; font-size: 0.8em; margin-left: 5px;">↘ ${diff < -1 ? Math.round(diff) : diff.toFixed(1)}</span>`;
            } else {
                return `<span style="color: #ffff00; font-size: 0.8em; margin-left: 5px;">→</span>`;
            }
        }

        // Envoyer un message
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const message = input.value.trim();

            if (!message) {
                updateStatus('error', 'Veuillez saisir un message');
                return;
            }

            if (!isConnected) {
                updateStatus('error', 'Connexion au serveur perdue - Reconnexion...');
                await checkConnection();
                return;
            }

            // Désactiver l'interface
            input.disabled = true;
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<div class="loading"></div>';

            // Afficher le message utilisateur
            addMessage(message, 'user');
            input.value = '';

            // Afficher l'indicateur de réflexion
            const loadingId = addMessage('🤔 Réflexion en cours avec mémoire thermique...', 'ai', true);

            try {
                // Préparer les données à envoyer
                const requestData = {
                    message: message,
                    codeMode: isCodeMode,
                    autoMode: isAutoMode
                };

                // Ajouter l'image de la caméra si active
                if (isCameraActive) {
                    const imageData = captureImage();
                    if (imageData) {
                        requestData.image = imageData;
                        requestData.hasVision = true;
                    }
                }

                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                
                // Supprimer l'indicateur de chargement
                document.getElementById(loadingId).remove();

                if (data.success) {
                    // Sauvegarder les métriques précédentes pour l'évolution
                    if (window.lastIQData || data.iqAnalysis) {
                        const currentIQ = data.iqAnalysis || window.lastIQData;
                        window.previousMetrics = {
                            agentIQ: window.lastIQData?.agentIQ,
                            memoryIQ: window.lastIQData?.memoryIQ,
                            combinedIQ: window.lastIQData?.combinedIQ,
                            neurons: data.brainStats?.activeNeurons,
                            synapses: data.brainStats?.synapticConnections,
                            memories: data.memoryStats?.totalMemories,
                            temperature: data.memoryStats?.globalTemperature
                        };
                    }

                    // Sauvegarder les nouvelles données QI
                    window.lastIQData = data.iqAnalysis;

                    // Extraire les pensées si présentes
                    const thinkMatch = data.response.match(/<think>([\s\S]*?)<\/think>/);
                    const thoughts = thinkMatch ? thinkMatch[1].trim() : null;
                    const cleanResponse = data.response.replace(/<think>[\s\S]*?<\/think>/, '').trim();

                    // Afficher les pensées si présentes
                    if (thoughts) {
                        addMessage('💭 PENSÉES INTERNES: ' + thoughts, 'ai', false, {
                            type: 'thoughts',
                            agent: 'Réflexion interne'
                        });
                    }

                    // Détecter et traiter le code dans la réponse
                    const codeBlocks = extractCodeBlocks(cleanResponse);
                    let finalResponse = cleanResponse;

                    if (codeBlocks.length > 0 && isCodeMode) {
                        // Remplacer les blocs de code par des conteneurs interactifs
                        codeBlocks.forEach((block, index) => {
                            const codeContainer = createCodeContainer(block.code, block.language);
                            const placeholder = `__CODE_BLOCK_${index}__`;
                            finalResponse = finalResponse.replace(block.original, placeholder);
                        });
                    }

                    // Afficher la réponse finale
                    const messageId = addMessage(finalResponse, 'ai', false, {
                        agent: data.agent,
                        neurons: data.brainStats?.activeNeurons,
                        temperature: data.memoryStats?.globalTemperature?.toFixed(1),
                        iq: data.iqAnalysis?.agentIQ,
                        memoryIQ: data.iqAnalysis?.memoryIQ,
                        combinedIQ: data.iqAnalysis?.combinedIQ
                    });

                    // Insérer les conteneurs de code
                    if (codeBlocks.length > 0 && isCodeMode) {
                        const messageElement = document.getElementById(messageId);
                        codeBlocks.forEach((block, index) => {
                            const placeholder = `__CODE_BLOCK_${index}__`;
                            const codeContainer = createCodeContainer(block.code, block.language);
                            messageElement.innerHTML = messageElement.innerHTML.replace(
                                placeholder,
                                codeContainer.outerHTML
                            );
                        });
                    }

                    // Synthèse vocale automatique si activée
                    if (isSpeaking || document.getElementById('speakBtn').classList.contains('active')) {
                        setTimeout(() => {
                            speakText(cleanResponse);
                        }, 500);
                    }

                    // Mettre à jour les métriques si disponibles
                    if (data.memoryStats || data.brainStats) {
                        updateMetrics({
                            memoryStats: data.memoryStats,
                            brainStats: data.brainStats
                        });
                    }
                } else {
                    addMessage('❌ Erreur: ' + (data.error || 'Erreur inconnue'), 'ai');
                }
            } catch (error) {
                document.getElementById(loadingId).remove();
                addMessage('❌ Erreur de communication: ' + error.message, 'ai');
            } finally {
                // Réactiver l'interface
                input.disabled = false;
                sendBtn.disabled = false;
                sendBtn.textContent = 'Envoyer';
                input.focus();
            }
        }

        // Ajouter un message au chat
        function addMessage(text, sender, isLoading = false, info = null) {
            const container = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            messageDiv.id = messageId;
            messageDiv.className = `message ${sender}-message`;

            // Style spécial pour les pensées
            if (info && info.type === 'thoughts') {
                messageDiv.style.background = 'rgba(255, 255, 0, 0.1)';
                messageDiv.style.border = '1px solid rgba(255, 255, 0, 0.3)';
                messageDiv.style.fontFamily = 'monospace';
                messageDiv.style.fontSize = '0.9em';
            }

            let content = `<div>${text}</div>`;

            if (info && !isLoading && info.type !== 'thoughts') {
                const infoText = [
                    info.agent ? `Agent: ${info.agent.split(' ')[0]} ${info.agent.split(' ')[1]}` : null,
                    info.neurons ? `Neurones: ${info.neurons}` : null,
                    info.temperature ? `Temp: ${info.temperature}°C` : null,
                    info.iq ? `QI Agent: ${info.iq}` : null,
                    info.memoryIQ ? `QI Mémoire: ${info.memoryIQ}` : null,
                    info.combinedIQ ? `QI Combiné: ${info.combinedIQ}` : null
                ].filter(Boolean).join(' • ');

                content += `<div class="message-info">${infoText}</div>`;
            }

            messageDiv.innerHTML = content;
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;

            return messageId;
        }

        // Gérer la touche Entrée
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // Afficher la section mémoires détaillées
        async function showMemoriesSection() {
            try {
                const response = await fetch('/api/memories/detailed');
                const data = await response.json();

                if (data.success) {
                    const memories = data.memories || [];

                    // Créer une fenêtre popup avec les mémoires
                    const popup = window.open('', 'memories', 'width=800,height=600,scrollbars=yes');
                    popup.document.write(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>🧠 Mémoires Détaillées - LOUNA AI</title>
                            <style>
                                body { font-family: Arial, sans-serif; background: #1a1a2e; color: #fff; padding: 20px; }
                                .memory-item { background: rgba(255,255,255,0.1); margin: 10px 0; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea; }
                                .memory-key { font-weight: bold; color: #667eea; margin-bottom: 5px; }
                                .memory-data { margin: 8px 0; }
                                .memory-meta { font-size: 0.9em; opacity: 0.7; margin-top: 10px; }
                                .zone-indicator { display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 0.8em; margin-right: 10px; }
                                .zone-1 { background: #ff6b6b; }
                                .zone-2 { background: #ffa726; }
                                .zone-3 { background: #ffeb3b; color: #000; }
                                .zone-4 { background: #66bb6a; }
                                .zone-5 { background: #42a5f5; }
                                .zone-6 { background: #ab47bc; }
                                h1 { text-align: center; color: #667eea; }
                                .stats { background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin-bottom: 20px; }
                            </style>
                        </head>
                        <body>
                            <h1>🧠 Mémoires Détaillées - LOUNA AI</h1>
                            <div class="stats">
                                <strong>📊 Statistiques:</strong><br>
                                Total des mémoires: \${memories.length}<br>
                                Température globale: \${data.globalTemperature?.toFixed(1) || 'N/A'}°C<br>
                                Efficacité: \${data.efficiency?.toFixed(1) || 'N/A'}%
                            </div>
                            ${memories.map(memory => `
                                <div class="memory-item">
                                    <div class="memory-key">
                                        <span class="zone-indicator zone-${memory.zone || 1}">Zone ${memory.zone || 1}</span>
                                        ${memory.key || 'Clé inconnue'}
                                    </div>
                                    <div class="memory-data">${memory.data || 'Données non disponibles'}</div>
                                    <div class="memory-meta">
                                        🌡️ ${memory.temperature?.toFixed(1) || 'N/A'}°C |
                                        ⭐ Importance: ${memory.importance?.toFixed(2) || 'N/A'} |
                                        📅 ${memory.timestamp ? new Date(memory.timestamp).toLocaleString() : 'Date inconnue'} |
                                        📂 ${memory.category || 'Catégorie inconnue'}
                                    </div>
                                </div>
                            `).join('')}
                        </body>
                        </html>
                    `);
                } else {
                    alert('Erreur lors du chargement des mémoires: ' + (data.error || 'Erreur inconnue'));
                }
            } catch (error) {
                console.error('Erreur:', error);
                alert('Erreur de connexion lors du chargement des mémoires');
            }
        }

        // Changer d'interface
        function showInterface(type) {
            // Mettre à jour les boutons de navigation
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // ===== FONCTIONS VOCALES =====

        // Initialiser la reconnaissance vocale
        function initSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                speechRecognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
                speechRecognition.continuous = false;
                speechRecognition.interimResults = false;
                speechRecognition.lang = 'fr-FR';

                speechRecognition.onstart = function() {
                    isListening = true;
                    document.getElementById('voiceBtn').classList.add('recording');
                    updateStatus('listening', 'Écoute en cours...');
                };

                speechRecognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('messageInput').value = transcript;
                    updateStatus('processing', 'Texte reconnu: ' + transcript);

                    // Envoyer automatiquement si le mode auto est activé
                    if (isAutoMode) {
                        setTimeout(() => sendMessage(), 1000);
                    }
                };

                speechRecognition.onerror = function(event) {
                    console.error('Erreur reconnaissance vocale:', event.error);
                    updateStatus('error', 'Erreur: ' + event.error);
                };

                speechRecognition.onend = function() {
                    isListening = false;
                    document.getElementById('voiceBtn').classList.remove('recording');
                    updateStatus('ready', 'Prêt');
                };
            } else {
                console.warn('Reconnaissance vocale non supportée');
            }
        }

        // Basculer la reconnaissance vocale
        function toggleVoiceInput() {
            if (!speechRecognition) {
                initSpeechRecognition();
            }

            if (isListening) {
                speechRecognition.stop();
            } else {
                speechRecognition.start();
            }
        }

        // Synthèse vocale
        function speakText(text) {
            if (speechSynthesis.speaking) {
                speechSynthesis.cancel();
                return;
            }

            // Nettoyer le texte des balises et caractères spéciaux
            const cleanText = text.replace(/<[^>]*>/g, '').replace(/[*#`]/g, '');

            const utterance = new SpeechSynthesisUtterance(cleanText);
            utterance.lang = 'fr-FR';
            utterance.rate = 0.9;
            utterance.pitch = 1;
            utterance.volume = 0.8;

            utterance.onstart = function() {
                isSpeaking = true;
                document.getElementById('speakBtn').style.background = 'linear-gradient(45deg, #f59e0b, #d97706)';
                updateStatus('speaking', 'Lecture en cours...');
            };

            utterance.onend = function() {
                isSpeaking = false;
                document.getElementById('speakBtn').style.background = '';
                updateStatus('ready', 'Prêt');
            };

            speechSynthesis.speak(utterance);
        }

        // Basculer la synthèse vocale
        function toggleSpeech() {
            if (isSpeaking) {
                speechSynthesis.cancel();
            } else {
                const lastMessage = document.querySelector('.ai-message:last-child div:first-child');
                if (lastMessage) {
                    speakText(lastMessage.textContent);
                }
            }
        }

        // ===== FONCTIONS CAMÉRA =====

        // Basculer la caméra
        async function toggleCamera() {
            const preview = document.getElementById('cameraPreview');
            const btn = document.getElementById('cameraBtn');

            if (isCameraActive) {
                // Arrêter la caméra
                if (cameraStream) {
                    cameraStream.getTracks().forEach(track => track.stop());
                    cameraStream = null;
                }
                preview.style.display = 'none';
                btn.classList.remove('active');
                isCameraActive = false;
                updateStatus('ready', 'Caméra désactivée');
            } else {
                // Démarrer la caméra
                try {
                    cameraStream = await navigator.mediaDevices.getUserMedia({
                        video: { width: 640, height: 480 }
                    });
                    preview.srcObject = cameraStream;
                    preview.style.display = 'block';
                    btn.classList.add('active');
                    isCameraActive = true;
                    updateStatus('ready', 'Caméra activée');
                } catch (error) {
                    console.error('Erreur caméra:', error);
                    updateStatus('error', 'Erreur caméra: ' + error.message);
                }
            }
        }

        // Capturer une image de la caméra
        function captureImage() {
            if (!cameraStream) return null;

            const canvas = document.createElement('canvas');
            const video = document.getElementById('cameraPreview');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            return canvas.toDataURL('image/jpeg', 0.8);
        }

        // ===== FONCTIONS MODE CODE =====

        // Basculer le mode code
        function toggleCodeMode() {
            const btn = document.getElementById('codeBtn');
            const input = document.getElementById('messageInput');

            isCodeMode = !isCodeMode;

            if (isCodeMode) {
                btn.classList.add('active');
                input.placeholder = 'Demandez du code, des corrections, ou collez votre code à améliorer...';
                updateStatus('ready', 'Mode code activé');
            } else {
                btn.classList.remove('active');
                input.placeholder = 'Posez votre question à LOUNA AI ou parlez...';
                updateStatus('ready', 'Mode normal');
            }
        }

        // Créer un conteneur de code avec bouton copier
        function createCodeContainer(code, language = 'javascript') {
            const container = document.createElement('div');
            container.className = 'code-container';

            const header = document.createElement('div');
            header.className = 'code-header';

            const title = document.createElement('div');
            title.className = 'code-title';
            title.textContent = `💻 Code ${language.toUpperCase()}`;

            const copyBtn = document.createElement('button');
            copyBtn.className = 'copy-btn';
            copyBtn.textContent = '📋 Copier';
            copyBtn.onclick = () => copyCode(code, copyBtn);

            header.appendChild(title);
            header.appendChild(copyBtn);

            const content = document.createElement('div');
            content.className = 'code-content';
            content.textContent = code;

            container.appendChild(header);
            container.appendChild(content);

            return container;
        }

        // Copier le code dans le presse-papiers
        async function copyCode(code, button) {
            try {
                await navigator.clipboard.writeText(code);
                const originalText = button.textContent;
                button.textContent = '✅ Copié !';
                button.classList.add('copied');

                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);

                updateStatus('ready', 'Code copié dans le presse-papiers');
            } catch (error) {
                console.error('Erreur copie:', error);
                updateStatus('error', 'Erreur lors de la copie');
            }
        }

        // ===== FONCTIONS OUVERTURE D'APPLICATIONS =====

        // Ouvrir Visual Studio Code
        async function openVSCode() {
            try {
                updateStatus('processing', 'Ouverture de Visual Studio Code...');

                const response = await fetch('/api/desktop/open-app', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        app: 'Visual Studio Code',
                        action: 'open'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    updateStatus('ready', 'Visual Studio Code ouvert');
                    addMessage('🖥️ Visual Studio Code a été ouvert avec succès !', 'ai', false, {
                        type: 'system',
                        agent: 'Système'
                    });
                } else {
                    updateStatus('error', 'Erreur ouverture VS Code');
                    addMessage('❌ Erreur lors de l\'ouverture de Visual Studio Code: ' + (data.error || 'Erreur inconnue'), 'ai');
                }
            } catch (error) {
                console.error('Erreur VS Code:', error);
                updateStatus('error', 'Erreur connexion');
                addMessage('❌ Erreur de connexion lors de l\'ouverture de Visual Studio Code', 'ai');
            }
        }

        // Ouvrir une application générique
        async function openApp(appName) {
            try {
                updateStatus('processing', `Ouverture de ${appName}...`);

                const response = await fetch('/api/desktop/open-app', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        app: appName,
                        action: 'open'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    updateStatus('ready', `${appName} ouvert`);
                    addMessage(`🖥️ ${appName} a été ouvert avec succès !`, 'ai', false, {
                        type: 'system',
                        agent: 'Système'
                    });
                } else {
                    updateStatus('error', `Erreur ouverture ${appName}`);
                    addMessage(`❌ Erreur lors de l'ouverture de ${appName}: ` + (data.error || 'Erreur inconnue'), 'ai');
                }
            } catch (error) {
                console.error(`Erreur ${appName}:`, error);
                updateStatus('error', 'Erreur connexion');
                addMessage(`❌ Erreur de connexion lors de l'ouverture de ${appName}`, 'ai');
            }
        }

        // Extraire les blocs de code de la réponse
        function extractCodeBlocks(text) {
            const codeBlocks = [];

            // Regex pour détecter les blocs de code avec langage
            const codeRegex = /```(\w+)?\n?([\s\S]*?)```/g;
            let match;

            while ((match = codeRegex.exec(text)) !== null) {
                codeBlocks.push({
                    original: match[0],
                    language: match[1] || 'text',
                    code: match[2].trim()
                });
            }

            // Regex pour détecter le code inline
            const inlineCodeRegex = /`([^`]+)`/g;
            while ((match = inlineCodeRegex.exec(text)) !== null) {
                if (match[1].length > 10) { // Seulement pour le code plus long
                    codeBlocks.push({
                        original: match[0],
                        language: 'text',
                        code: match[1]
                    });
                }
            }

            return codeBlocks;
        }

        // Mettre à jour le statut
        function updateStatus(type, message) {
            console.log(`Status: ${type} - ${message}`);

            const indicator = document.getElementById('statusIndicator');
            const sendBtnText = document.getElementById('sendBtnText');
            const sendBtn = document.getElementById('sendBtn');

            // Vérifier que les éléments existent
            if (indicator) {
                indicator.style.display = 'inline-block';
                indicator.className = `status-indicator status-${type}`;
            }

            // Mettre à jour le bouton d'envoi
            if (sendBtn) {
                switch(type) {
                    case 'listening':
                        sendBtn.textContent = '🟡 Écoute';
                        sendBtn.disabled = true;
                        break;
                    case 'speaking':
                        sendBtn.textContent = '🟠 Lecture';
                        sendBtn.disabled = false;
                        break;
                    case 'processing':
                        sendBtn.textContent = '🟣 Traitement';
                        sendBtn.disabled = true;
                        break;
                    case 'ready':
                        sendBtn.textContent = '🟢 Envoyer';
                        sendBtn.disabled = false;
                        break;
                    case 'error':
                        sendBtn.textContent = '🔴 Erreur';
                        sendBtn.disabled = false;
                        break;
                    default:
                        sendBtn.textContent = 'Envoyer';
                        sendBtn.disabled = false;
                }
            } else if (sendBtnText) {
                // Fallback pour l'ancien système
                const originalText = sendBtnText.textContent;
                sendBtnText.textContent = message || originalText;

                setTimeout(() => {
                    sendBtnText.textContent = originalText;
                    if (type === 'ready' && indicator) {
                        indicator.style.display = 'none';
                    }
                }, 3000);
            }
        }

        // ===== FONCTIONS MODE AUTO-FORMATION =====

        // Basculer le mode auto-formation
        function toggleAutoMode() {
            const btn = document.getElementById('autoBtn');
            const panel = document.getElementById('autoModePanel');

            isAutoMode = !isAutoMode;

            if (isAutoMode) {
                btn.classList.add('active');
                panel.style.display = 'block';
                updateStatus('ready', 'Mode auto-formation activé');
            } else {
                btn.classList.remove('active');
                panel.style.display = 'none';
                stopAutoTraining();
                updateStatus('ready', 'Mode auto-formation désactivé');
            }
        }

        // Démarrer la formation automatique
        async function startAutoTraining() {
            const topic = document.getElementById('autoTopic').value.trim();
            const maxQuestions = parseInt(document.getElementById('autoQuestions').value);
            const delay = parseInt(document.getElementById('autoDelay').value) * 1000;

            if (!topic) {
                updateStatus('error', 'Veuillez spécifier un sujet');
                return;
            }

            if (autoTrainingInterval) {
                stopAutoTraining();
            }

            autoQuestionCount = 0;
            document.getElementById('autoStatus').textContent = `Démarrage formation sur: ${topic}`;
            updateStatus('processing', `Formation automatique démarrée sur ${topic}`);

            // Questions de formation prédéfinies et intelligentes
            const trainingQuestions = [
                `Explique-moi les concepts fondamentaux de ${topic} de manière détaillée`,
                `Quels sont les avantages et inconvénients principaux de ${topic} ?`,
                `Donne-moi un exemple pratique concret d'utilisation de ${topic}`,
                `Quelles sont les meilleures pratiques recommandées pour ${topic} ?`,
                `Comment un débutant peut-il apprendre ${topic} efficacement ?`,
                `Quels outils et ressources recommandes-tu pour ${topic} ?`,
                `Explique les erreurs courantes à éviter absolument avec ${topic}`,
                `Comment optimiser les performances et l'efficacité avec ${topic} ?`,
                `Quelles sont les tendances actuelles et futures de ${topic} ?`,
                `Peux-tu créer un exercice pratique pour maîtriser ${topic} ?`,
                `Comment ${topic} s'intègre-t-il avec d'autres technologies ?`,
                `Quels sont les défis techniques majeurs de ${topic} ?`,
                `Peux-tu comparer ${topic} avec ses alternatives ?`,
                `Comment déboguer et résoudre les problèmes avec ${topic} ?`,
                `Quelles sont les certifications ou formations pour ${topic} ?`
            ];

            let currentQuestion = 0;
            let waitingForResponse = false;

            // Fonction pour poser la prochaine question
            const askNextQuestion = async () => {
                if (currentQuestion >= maxQuestions || currentQuestion >= trainingQuestions.length || !autoTrainingInterval) {
                    stopAutoTraining();
                    return;
                }

                if (waitingForResponse) {
                    // Attendre que la réponse précédente soit terminée
                    setTimeout(askNextQuestion, 1000);
                    return;
                }

                const question = trainingQuestions[currentQuestion];
                document.getElementById('messageInput').value = question;
                document.getElementById('autoStatus').textContent =
                    `Question ${currentQuestion + 1}/${maxQuestions}: ${question.substring(0, 50)}...`;

                updateStatus('processing', `Envoi question ${currentQuestion + 1}/${maxQuestions}`);

                try {
                    waitingForResponse = true;

                    // Envoyer la question
                    await sendMessage();

                    currentQuestion++;
                    autoQuestionCount++;

                    // Attendre avant la prochaine question
                    setTimeout(() => {
                        waitingForResponse = false;
                        if (autoTrainingInterval) {
                            // Générer une question de suivi intelligente
                            if (Math.random() > 0.7 && currentQuestion < maxQuestions) {
                                generateFollowUpQuestion(topic);
                            }
                        }
                    }, delay);

                } catch (error) {
                    console.error('Erreur envoi question:', error);
                    waitingForResponse = false;
                    updateStatus('error', 'Erreur lors de l\'envoi de la question');
                }
            };

            // Démarrer la première question après un délai initial
            autoTrainingInterval = setTimeout(() => {
                autoTrainingInterval = setInterval(askNextQuestion, delay + 2000);
                askNextQuestion(); // Première question immédiate
            }, 1000);
        }

        // Générer une question de suivi
        function generateFollowUpQuestion(topic) {
            const followUpQuestions = [
                `Peux-tu approfondir ce point sur ${topic} ?`,
                `Comment cela s'applique-t-il concrètement avec ${topic} ?`,
                `Quels sont les défis liés à cette approche de ${topic} ?`,
                `Peux-tu donner un autre exemple pour ${topic} ?`,
                `Comment résoudre les problèmes courants avec ${topic} ?`
            ];

            const randomQuestion = followUpQuestions[Math.floor(Math.random() * followUpQuestions.length)];

            setTimeout(() => {
                if (autoTrainingInterval) {
                    document.getElementById('messageInput').value = randomQuestion;
                    sendMessage();
                }
            }, 2000);
        }

        // Arrêter la formation automatique
        function stopAutoTraining() {
            if (autoTrainingInterval) {
                clearInterval(autoTrainingInterval);
                autoTrainingInterval = null;
            }

            document.getElementById('autoStatus').textContent =
                autoQuestionCount > 0 ? `Formation terminée: ${autoQuestionCount} questions posées` : '';

            updateStatus('ready', 'Formation automatique arrêtée');
        }

        // Initialisation
        window.addEventListener('load', () => {
            checkConnection();
            initSpeechRecognition();
            // Monitoring intelligent avec fréquence adaptative
            let monitoringInterval = 60000; // Démarrer à 60 secondes
            let consecutiveErrors = 0;

            function adaptiveMonitoring() {
                checkConnection().then(() => {
                    consecutiveErrors = 0;
                    // Système stable, réduire la fréquence
                    monitoringInterval = Math.min(monitoringInterval * 1.1, 120000); // Max 2 minutes
                }).catch(() => {
                    consecutiveErrors++;
                    // Problèmes détectés, augmenter la fréquence
                    monitoringInterval = Math.max(monitoringInterval * 0.8, 10000); // Min 10 secondes
                });

                setTimeout(adaptiveMonitoring, monitoringInterval);
            }

            adaptiveMonitoring(); // Démarrer le monitoring adaptatif

        // 🔧 DIAGNOSTICS SYSTÈME AVANCÉS
        async function showSystemDiagnostics() {
            try {
                const [metricsResponse, memoriesResponse] = await Promise.all([
                    fetch('/api/metrics'),
                    fetch('/api/memories/detailed')
                ]);

                const metrics = await metricsResponse.json();
                const memories = await memoriesResponse.json();

                const diagnostics = {
                    timestamp: new Date().toISOString(),
                    system: {
                        server: 'Actif',
                        agent: metrics.agent || 'Non défini',
                        thermalMemory: memories.success ? 'Opérationnelle' : 'Erreur',
                        accelerators: metrics.kyberAccelerators || 'Non défini'
                    },
                    performance: {
                        iq: metrics.iq || 'Calcul en cours',
                        memoryEntries: memories.memories?.length || 0,
                        efficiency: memories.efficiency || 'N/A',
                        temperature: memories.globalTemperature || 'N/A'
                    },
                    health: {
                        errors: 0,
                        warnings: 0,
                        status: 'Optimal'
                    }
                };

                const popup = window.open('', 'diagnostics', 'width=900,height=700,scrollbars=yes');
                popup.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>🔧 Diagnostics Système - LOUNA AI</title>
                        <style>
                            body { font-family: 'Courier New', monospace; background: #0a0a0a; color: #00ff00; padding: 20px; }
                            .diagnostic-section { background: rgba(0,255,0,0.1); margin: 15px 0; padding: 15px; border: 1px solid #00ff00; border-radius: 5px; }
                            .status-ok { color: #00ff00; }
                            .status-warning { color: #ffaa00; }
                            .status-error { color: #ff0000; }
                            .metric { margin: 8px 0; padding: 5px; background: rgba(0,0,0,0.5); }
                            h1, h2 { color: #00ffff; text-align: center; }
                            .timestamp { text-align: center; color: #888; margin-bottom: 20px; }
                        </style>
                    </head>
                    <body>
                        <h1>🔧 DIAGNOSTICS SYSTÈME COMPLETS</h1>
                        <div class="timestamp">Généré le: \${diagnostics.timestamp}</div>

                        <div class="diagnostic-section">
                            <h2>🖥️ ÉTAT DU SYSTÈME</h2>
                            <div class="metric">Serveur: <span class="status-ok">\${diagnostics.system.server}</span></div>
                            <div class="metric">Agent IA: <span class="status-ok">\${diagnostics.system.agent}</span></div>
                            <div class="metric">Mémoire Thermique: <span class="status-ok">\${diagnostics.system.thermalMemory}</span></div>
                            <div class="metric">Accélérateurs: <span class="status-ok">\${diagnostics.system.accelerators}</span></div>
                        </div>

                        <div class="diagnostic-section">
                            <h2>⚡ PERFORMANCES</h2>
                            <div class="metric">QI Calculé: <span class="status-ok">\${diagnostics.performance.iq}</span></div>
                            <div class="metric">Entrées Mémoire: <span class="status-ok">\${diagnostics.performance.memoryEntries}</span></div>
                            <div class="metric">Efficacité: <span class="status-ok">\${diagnostics.performance.efficiency}%</span></div>
                            <div class="metric">Température: <span class="status-ok">\${diagnostics.performance.temperature}°C</span></div>
                        </div>

                        <div class="diagnostic-section">
                            <h2>🏥 SANTÉ SYSTÈME</h2>
                            <div class="metric">Erreurs: <span class="status-ok">\${diagnostics.health.errors}</span></div>
                            <div class="metric">Avertissements: <span class="status-ok">\${diagnostics.health.warnings}</span></div>
                            <div class="metric">État Global: <span class="status-ok">\${diagnostics.health.status}</span></div>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button onclick="window.close()" style="background: #00ff00; color: #000; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">Fermer</button>
                        </div>
                    </body>
                    </html>
                `);
            } catch (error) {
                alert('Erreur lors du chargement des diagnostics: ' + error.message);
            }
        }

        // ⚡ OPTIMISATION AUTOMATIQUE DES PERFORMANCES
        async function optimizePerformance() {
            const optimizationSteps = [
                'Analyse des performances actuelles...',
                'Optimisation de la mémoire thermique...',
                'Calibrage des accélérateurs Kyber...',
                'Synchronisation des neurones...',
                'Nettoyage des connexions synaptiques...',
                'Finalisation de l\'optimisation...'
            ];

            const popup = window.open('', 'optimization', 'width=600,height=400');
            popup.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>⚡ Optimisation Performance - LOUNA AI</title>
                    <style>
                        body { font-family: Arial, sans-serif; background: #1a1a2e; color: #fff; padding: 20px; text-align: center; }
                        .progress-container { background: rgba(0,0,0,0.5); padding: 20px; border-radius: 10px; margin: 20px 0; }
                        .progress-bar { width: 100%; height: 20px; background: rgba(255,255,255,0.2); border-radius: 10px; overflow: hidden; }
                        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea, #764ba2); width: 0%; transition: width 0.5s; }
                        .step { margin: 10px 0; opacity: 0.5; transition: opacity 0.3s; }
                        .step.active { opacity: 1; color: #667eea; }
                        .step.completed { opacity: 1; color: #00ff00; }
                    </style>
                </head>
                <body>
                    <h1>⚡ OPTIMISATION EN COURS</h1>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div id="progressText">0%</div>
                    </div>
                    <div id="steps"></div>
                    <div id="result" style="margin-top: 20px; display: none;">
                        <h2 style="color: #00ff00;">✅ OPTIMISATION TERMINÉE</h2>
                        <p>Performances améliorées avec succès !</p>
                        <button onclick="window.close()" style="background: #667eea; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">Fermer</button>
                    </div>
                </body>
                </html>
            `);

            // Simulation d'optimisation
            const stepsContainer = popup.document.getElementById('steps');
            const progressFill = popup.document.getElementById('progressFill');
            const progressText = popup.document.getElementById('progressText');
            const result = popup.document.getElementById('result');

            optimizationSteps.forEach((step, index) => {
                const stepDiv = popup.document.createElement('div');
                stepDiv.className = 'step';
                stepDiv.textContent = step;
                stepDiv.id = `step${index}`;
                stepsContainer.appendChild(stepDiv);
            });

            for (let i = 0; i < optimizationSteps.length; i++) {
                await new Promise(resolve => setTimeout(resolve, 1000));

                const progress = ((i + 1) / optimizationSteps.length) * 100;
                progressFill.style.width = progress + '%';
                progressText.textContent = Math.round(progress) + '%';

                const currentStep = popup.document.getElementById(`step${i}`);
                currentStep.classList.add('active');

                await new Promise(resolve => setTimeout(resolve, 500));
                currentStep.classList.remove('active');
                currentStep.classList.add('completed');
            }

            await new Promise(resolve => setTimeout(resolve, 500));
            stepsContainer.style.display = 'none';
            result.style.display = 'block';
        }

        // 🧠 GESTION PHYSIQUE DE LA MÉMOIRE
        let physicalMemoryActive = false;

        async function togglePhysicalMemory() {
            const btn = document.getElementById('memoryToggleBtn');

            try {
                if (!physicalMemoryActive) {
                    // Activer la mémoire physique
                    const response = await fetch('/api/memory/physical/activate', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ mode: 'full_integration' })
                    });

                    const result = await response.json();

                    if (result.success) {
                        physicalMemoryActive = true;
                        btn.textContent = '🧠 Désactiver Mémoire Physique';
                        btn.style.background = '#ff4444';
                        showNotification('✅ Mémoire physique activée !', 'success');
                    } else {
                        showNotification('❌ Erreur activation mémoire physique', 'error');
                    }
                } else {
                    // Désactiver la mémoire physique
                    const response = await fetch('/api/memory/physical/deactivate', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    const result = await response.json();

                    if (result.success) {
                        physicalMemoryActive = false;
                        btn.textContent = '🧠 Activer Mémoire Physique';
                        btn.style.background = '#667eea';
                        showNotification('✅ Mémoire physique désactivée !', 'success');
                    } else {
                        showNotification('❌ Erreur désactivation mémoire physique', 'error');
                    }
                }
            } catch (error) {
                showNotification('❌ Erreur gestion mémoire physique: ' + error.message, 'error');
            }
        }

        // 🌐 TEST MCP AVEC SAFARI
        async function testMCPSafari() {
            try {
                showNotification('🌐 Lancement de Safari via MCP...', 'info');

                const response = await fetch('/api/desktop/launch-app', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        appName: 'safari',
                        args: ['https://www.apple.com']
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('✅ Safari lancé avec succès via MCP !', 'success');

                    // Ouvrir une popup avec les détails
                    const popup = window.open('', 'safariTest', 'width=600,height=400');
                    popup.document.write(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>🌐 Test MCP Safari - Réussi</title>
                            <style>
                                body { font-family: Arial, sans-serif; background: #1a1a2e; color: #fff; padding: 20px; text-align: center; }
                                .success { color: #00ff00; font-size: 24px; margin: 20px 0; }
                                .details { background: rgba(0,255,0,0.1); padding: 15px; border-radius: 10px; margin: 20px 0; text-align: left; }
                                .close-btn { background: #00ff00; color: #000; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-top: 20px; }
                            </style>
                        </head>
                        <body>
                            <h1>🌐 TEST MCP SAFARI</h1>
                            <div class="success">✅ SUCCÈS COMPLET !</div>
                            <div class="details">
                                <h3>📊 Détails de l'exécution :</h3>
                                <p><strong>Application :</strong> Safari</p>
                                <p><strong>URL ouverte :</strong> https://www.apple.com</p>
                                <p><strong>Méthode :</strong> MCP Desktop Actions</p>
                                <p><strong>Statut :</strong> \${result.status || 'Lancé avec succès'}</p>
                                <p><strong>Timestamp :</strong> \${new Date().toLocaleString()}</p>
                            </div>
                            <button class="close-btn" onclick="window.close()">Fermer</button>
                        </body>
                        </html>
                    `);
                } else {
                    showNotification('❌ Erreur lancement Safari: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur test MCP Safari: ' + error.message, 'error');
            }
        }

        // 🔧 AFFICHER LES CAPACITÉS MCP
        async function showMCPCapabilities() {
            try {
                const [appsResponse, internetResponse] = await Promise.all([
                    fetch('/api/desktop/applications'),
                    fetch('/api/internet/stats')
                ]);

                const apps = await appsResponse.json();
                const internetStats = await internetResponse.json();

                const popup = window.open('', 'mcpCapabilities', 'width=900,height=700,scrollbars=yes');
                popup.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>🔧 Capacités MCP - LOUNA AI</title>
                        <style>
                            body { font-family: 'Courier New', monospace; background: #0a0a0a; color: #00ff00; padding: 20px; }
                            .capability-section { background: rgba(0,255,0,0.1); margin: 15px 0; padding: 15px; border: 1px solid #00ff00; border-radius: 5px; }
                            .app-list { max-height: 200px; overflow-y: auto; background: rgba(0,0,0,0.5); padding: 10px; border-radius: 5px; }
                            .app-item { margin: 5px 0; padding: 5px; background: rgba(0,255,0,0.1); border-radius: 3px; }
                            .status-ok { color: #00ff00; }
                            .status-warning { color: #ffaa00; }
                            h1, h2 { color: #00ffff; text-align: center; }
                            .test-btn { background: #00ff00; color: #000; padding: 8px 15px; border: none; border-radius: 3px; cursor: pointer; margin: 5px; }
                        </style>
                    </head>
                    <body>
                        <h1>🔧 CAPACITÉS MCP COMPLÈTES</h1>

                        <div class="capability-section">
                            <h2>🌐 CAPACITÉS INTERNET</h2>
                            <p>Recherches totales: <span class="status-ok">\${internetStats.success ? internetStats.stats.totalSearches : 'N/A'}</span></p>
                            <p>Recherches réussies: <span class="status-ok">\${internetStats.success ? internetStats.stats.successfulSearches : 'N/A'}</span></p>
                            <p>Connexions VPN: <span class="status-ok">\${internetStats.success ? internetStats.stats.vpnConnections : 'N/A'}</span></p>
                            <p>Cache hits: <span class="status-ok">\${internetStats.success ? internetStats.stats.cacheHits : 'N/A'}</span></p>
                            <button class="test-btn" onclick="testInternetSearch()">🔍 Tester Recherche</button>
                        </div>

                        <div class="capability-section">
                            <h2>🖥️ APPLICATIONS DISPONIBLES (${apps.success ? apps.count : 0})</h2>
                            <div class="app-list">
                                ${apps.success ? apps.applications.map(app => `
                                    <div class="app-item">
                                        <strong>${app.name}</strong> - ${app.type}
                                        <button class="test-btn" onclick="launchApp('${app.name.toLowerCase()}')" style="float: right;">🚀 Lancer</button>
                                    </div>
                                `).join('') : '<p>Aucune application détectée</p>'}
                            </div>
                        </div>

                        <div class="capability-section">
                            <h2>⚡ ACTIONS RAPIDES</h2>
                            <button class="test-btn" onclick="launchApp('safari')">🌐 Safari</button>
                            <button class="test-btn" onclick="launchApp('chrome')">🌐 Chrome</button>
                            <button class="test-btn" onclick="launchApp('firefox')">🌐 Firefox</button>
                            <button class="test-btn" onclick="launchApp('finder')">📁 Finder</button>
                            <button class="test-btn" onclick="launchApp('terminal')">💻 Terminal</button>
                            <button class="test-btn" onclick="launchApp('vscode')">📝 VS Code</button>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button onclick="window.close()" style="background: #ff4444; color: #fff; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">Fermer</button>
                        </div>

                        <scr` + `ipt>
                            async function launchApp(appName) {
                                try {
                                    const response = await fetch('/api/desktop/launch-app', {
                                        method: 'POST',
                                        headers: { 'Content-Type': 'application/json' },
                                        body: JSON.stringify({ appName })
                                    });
                                    const result = await response.json();
                                    alert(result.success ? \`✅ \${appName} lancé avec succès !\` : \`❌ Erreur: \${result.error}\`);
                                } catch (error) {
                                    alert('❌ Erreur: ' + error.message);
                                }
                            }

                            async function testInternetSearch() {
                                try {
                                    const query = prompt('Entrez votre recherche:', 'actualités Guadeloupe');
                                    if (!query) return;

                                    const response = await fetch('/api/internet/search', {
                                        method: 'POST',
                                        headers: { 'Content-Type': 'application/json' },
                                        body: JSON.stringify({ query, maxResults: 5 })
                                    });
                                    const result = await response.json();

                                    if (result.success) {
                                        alert(\`✅ Recherche réussie ! \${result.count} résultats trouvés.\`);
                                    } else {
                                        alert('❌ Erreur recherche: ' + result.error);
                                    }
                                } catch (error) {
                                    alert('❌ Erreur: ' + error.message);
                                }
                            }
                        </scr` + `ipt>
                    </body>
                    </html>
                `);
            } catch (error) {
                showNotification('❌ Erreur chargement capacités MCP: ' + error.message, 'error');
            }
        }

        // 📢 SYSTÈME DE NOTIFICATIONS
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 400px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;

            switch (type) {
                case 'success':
                    notification.style.background = '#4CAF50';
                    break;
                case 'error':
                    notification.style.background = '#f44336';
                    break;
                case 'warning':
                    notification.style.background = '#ff9800';
                    break;
                default:
                    notification.style.background = '#2196F3';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // Animation d'entrée
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 100);

            // Suppression automatique
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // 🔒 SÉCURITÉ MÉMOIRE THERMIQUE

        // Connexion sécurisée mémoire
        async function connectMemorySecurely() {
            try {
                showNotification('🔌 Connexion sécurisée de la mémoire thermique...', 'info');

                const response = await fetch('/api/memory/security/connect', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('✅ Mémoire thermique connectée de manière sécurisée !', 'success');
                } else {
                    showNotification(`❌ Échec connexion sécurisée: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur connexion sécurisée: ' + error.message, 'error');
            }
        }

        // Déconnexion sécurisée mémoire
        async function disconnectMemorySecurely() {
            try {
                showNotification('🔌 Déconnexion sécurisée de la mémoire thermique...', 'info');

                const response = await fetch('/api/memory/security/disconnect', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('✅ Mémoire thermique déconnectée de manière sécurisée !', 'success');
                } else {
                    showNotification(`❌ Échec déconnexion sécurisée: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur déconnexion sécurisée: ' + error.message, 'error');
            }
        }

        // Coupure d'urgence
        async function emergencyShutdown() {
            if (!confirm('🚨 ATTENTION ! Voulez-vous vraiment activer la coupure d\'urgence de la mémoire thermique ?')) {
                return;
            }

            try {
                showNotification('🚨 COUPURE D\'URGENCE ACTIVÉE !', 'error');

                const response = await fetch('/api/memory/security/emergency-shutdown', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ reason: 'Coupure manuelle utilisateur' })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('🚨 Coupure d\'urgence activée ! Mémoire thermique déconnectée.', 'error');

                    // Afficher les détails
                    const popup = document.createElement('div');
                    popup.className = 'popup-overlay';
                    popup.innerHTML = `
                        <div class="popup-content">
                            <h2>🚨 Coupure d'Urgence Activée</h2>
                            <div class="emergency-details">
                                <p><strong>Statut:</strong> ${result.result.status}</p>
                                <p><strong>Raison:</strong> ${result.result.reason}</p>
                                <p><strong>Timestamp:</strong> ${result.result.timestamp}</p>
                                <p><strong>Mode Urgence:</strong> Activé</p>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="close-btn">Fermer</button>
                        </div>
                    `;
                    document.body.appendChild(popup);
                } else {
                    showNotification(`❌ Erreur coupure d'urgence: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur coupure d\'urgence: ' + error.message, 'error');
            }
        }

        // Scan antivirus mémoire
        async function scanMemoryAntivirus() {
            try {
                showNotification('🦠 Démarrage du scan antivirus de la mémoire thermique...', 'info');

                const response = await fetch('/api/memory/security/antivirus-scan', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    const scanResults = result.scanResults;

                    showNotification(`✅ Scan antivirus terminé ! Statut: ${scanResults.status}`, 'success');

                    // Afficher les résultats détaillés
                    const popup = document.createElement('div');
                    popup.className = 'popup-overlay';
                    popup.innerHTML = `
                        <div class="popup-content">
                            <h2>🦠 Résultats Scan Antivirus Mémoire</h2>
                            <div class="scan-results">
                                <div class="result-item">
                                    <span class="label">📊 Statut:</span>
                                    <span class="value status-${scanResults.status}">${scanResults.status.toUpperCase()}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">📝 Entrées Mémoire:</span>
                                    <span class="value">${scanResults.memoryEntries}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">🚨 Menaces Trouvées:</span>
                                    <span class="value">${scanResults.threatsFound}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">🧹 Menaces Supprimées:</span>
                                    <span class="value">${scanResults.threatsRemoved}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">💔 Entrées Corrompues:</span>
                                    <span class="value">${scanResults.corruptedEntries}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">🔧 Entrées Réparées:</span>
                                    <span class="value">${scanResults.cleanedEntries}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">⏰ Timestamp:</span>
                                    <span class="value">${new Date(scanResults.timestamp).toLocaleString()}</span>
                                </div>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="close-btn">Fermer</button>
                        </div>
                    `;
                    document.body.appendChild(popup);
                } else {
                    showNotification(`❌ Erreur scan antivirus: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur scan antivirus: ' + error.message, 'error');
            }
        }

        // Nettoyage sécurisé mémoire
        async function cleanMemorySecurely() {
            if (!confirm('🧹 Voulez-vous vraiment nettoyer la mémoire thermique de manière sécurisée ?')) {
                return;
            }

            try {
                showNotification('🧹 Démarrage du nettoyage sécurisé de la mémoire thermique...', 'info');

                const response = await fetch('/api/memory/security/clean', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    const cleanResults = result.cleanResults;

                    showNotification(`✅ Nettoyage sécurisé terminé ! ${cleanResults.entriesRepaired} réparées, ${cleanResults.entriesRemoved} supprimées`, 'success');

                    // Afficher les résultats détaillés
                    const popup = document.createElement('div');
                    popup.className = 'popup-overlay';
                    popup.innerHTML = `
                        <div class="popup-content">
                            <h2>🧹 Résultats Nettoyage Sécurisé</h2>
                            <div class="clean-results">
                                <div class="result-item">
                                    <span class="label">📊 Entrées Traitées:</span>
                                    <span class="value">${cleanResults.entriesProcessed}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">🗑️ Entrées Supprimées:</span>
                                    <span class="value">${cleanResults.entriesRemoved}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">🔧 Entrées Réparées:</span>
                                    <span class="value">${cleanResults.entriesRepaired}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">💾 Données Préservées:</span>
                                    <span class="value">${cleanResults.dataPreserved ? '✅ Oui' : '❌ Non'}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">💾 Sauvegarde Créée:</span>
                                    <span class="value">${cleanResults.backupCreated ? '✅ Oui' : '❌ Non'}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">⏰ Timestamp:</span>
                                    <span class="value">${new Date(cleanResults.timestamp).toLocaleString()}</span>
                                </div>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="close-btn">Fermer</button>
                        </div>
                    `;
                    document.body.appendChild(popup);
                } else {
                    showNotification(`❌ Erreur nettoyage sécurisé: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur nettoyage sécurisé: ' + error.message, 'error');
            }
        }

        // 🔍 SCANNER MACHINE COMPLÈTE
        async function performFullMachineScan() {
            try {
                showNotification('🔍 Démarrage du scan complet de la machine...', 'info');

                const response = await fetch('/api/system/full-scan', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        includeDeepScan: true,
                        learnFromFiles: true,
                        analyzeSystem: true
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const stats = result.scanResults.stats;
                    showNotification(
                        `✅ Scan terminé ! ${stats.totalApplications} apps, ${stats.totalFiles} fichiers, ${stats.learningPoints} points d'apprentissage en ${stats.scanDuration}ms`,
                        'success'
                    );

                    // Afficher les résultats détaillés
                    const popup = window.open('', 'scanResults', 'width=900,height=700,scrollbars=yes');
                    popup.document.write(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>🔍 Résultats Scan Machine - LOUNA AI</title>
                            <style>
                                body { font-family: Arial, sans-serif; background: #1a1a2e; color: #fff; padding: 20px; }
                                .section { background: rgba(255,255,255,0.1); margin: 15px 0; padding: 20px; border-radius: 10px; }
                                .section h3 { color: #667eea; margin-top: 0; }
                                .stat-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
                                .stat-card { background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; text-align: center; }
                                .stat-value { font-size: 2em; font-weight: bold; color: #4ade80; }
                                .app-list { max-height: 300px; overflow-y: auto; }
                                .app-item { background: rgba(255,255,255,0.05); margin: 5px 0; padding: 10px; border-radius: 5px; }
                                h1 { text-align: center; color: #667eea; }
                                .learning-data { background: rgba(255, 215, 0, 0.1); border-left: 4px solid gold; }
                            </style>
                        </head>
                        <body>
                            <h1>🔍 Résultats Scan Machine Complet</h1>

                            <div class="section">
                                <h3>📊 Statistiques Générales</h3>
                                <div class="stat-grid">
                                    <div class="stat-card">
                                        <div class="stat-value">${stats.totalApplications}</div>
                                        <div>Applications</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-value">${stats.totalFiles}</div>
                                        <div>Fichiers</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-value">${stats.learningPoints}</div>
                                        <div>Points d'Apprentissage</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-value">${stats.scanDuration}ms</div>
                                        <div>Durée Scan</div>
                                    </div>
                                </div>
                            </div>

                            <div class="section">
                                <h3>🖥️ Informations Système</h3>
                                <p><strong>Plateforme:</strong> ${result.scanResults.results.systemInfo.platform}</p>
                                <p><strong>Architecture:</strong> ${result.scanResults.results.systemInfo.arch}</p>
                                <p><strong>Hostname:</strong> ${result.scanResults.results.systemInfo.hostname}</p>
                                <p><strong>Mémoire Totale:</strong> ${Math.round(result.scanResults.results.systemInfo.totalmem / (1024*1024*1024))} GB</p>
                                <p><strong>CPUs:</strong> ${result.scanResults.results.systemInfo.cpus.length} cœurs</p>
                            </div>

                            <div class="section learning-data">
                                <h3>🧠 Données d'Apprentissage</h3>
                                <p><strong>Type Machine:</strong> ${result.scanResults.results.learning.machineProfile.type}</p>
                                <p><strong>Niveau Puissance:</strong> ${result.scanResults.results.learning.machineProfile.powerLevel}</p>
                                <p><strong>Puissance CPU:</strong> ${result.scanResults.results.learning.machineProfile.cpuPower}</p>
                                <p><strong>Utilisateur:</strong> ${result.scanResults.results.learning.userProfile.username}</p>
                                <p><strong>Apps Préférées:</strong> ${result.scanResults.results.learning.userProfile.preferredApps.join(', ')}</p>
                            </div>

                            <div class="section">
                                <h3>📱 Applications Détectées (Top 20)</h3>
                                <div class="app-list">
                                    ${result.scanResults.results.applications.slice(0, 20).map(app => `
                                        <div class="app-item">
                                            <strong>${app.name}</strong> - ${app.type}
                                            ${app.path ? `<br><small>${app.path}</small>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </body>
                        </html>
                    `);
                } else {
                    showNotification('❌ Erreur scan: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur scan machine: ' + error.message, 'error');
            }
        }

        // 🖥️ AFFICHER PROFIL MACHINE APPRIS
        async function showMachineProfile() {
            try {
                const response = await fetch('/api/system/machine-profile');
                const result = await response.json();

                if (result.success) {
                    const profile = result.profile;

                    if (!profile.hasLearned) {
                        showNotification('⚠️ Aucun profil machine appris. Effectuez d\'abord un scan complet.', 'warning');
                        return;
                    }

                    // Afficher le profil dans une popup
                    const popup = window.open('', 'machineProfile', 'width=800,height=600,scrollbars=yes');
                    popup.document.write(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>🖥️ Profil Machine Appris - LOUNA AI</title>
                            <style>
                                body { font-family: Arial, sans-serif; background: #1a1a2e; color: #fff; padding: 20px; }
                                .section { background: rgba(255,255,255,0.1); margin: 15px 0; padding: 20px; border-radius: 10px; }
                                .section h3 { color: #667eea; margin-top: 0; }
                                .learning-badge { background: #4CAF50; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em; }
                                .profile-item { margin: 10px 0; padding: 10px; background: rgba(0,0,0,0.2); border-radius: 5px; }
                                .profile-label { font-weight: bold; color: #ffa726; }
                                .profile-value { margin-left: 10px; }
                                h1 { text-align: center; color: #667eea; }
                                .stats { background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin-bottom: 20px; }
                                .brain-stats { background: rgba(76, 175, 80, 0.1); border-left: 4px solid #4CAF50; }
                            </style>
                        </head>
                        <body>
                            <h1>🖥️ Profil Machine Appris</h1>

                            <div class="stats">
                                <span class="learning-badge">✅ Machine Apprise</span>
                                <br><br>
                                <strong>📊 Statistiques d'Apprentissage:</strong><br>
                                Entrées d'apprentissage: ${profile.learningEntries}<br>
                                Dernier apprentissage: ${profile.lastLearning ? new Date(profile.lastLearning).toLocaleString() : 'N/A'}
                            </div>

                            ${profile.learnedData.machine ? `
                                <div class="section">
                                    <h3>🖥️ Profil Machine</h3>
                                    <div class="profile-item">
                                        <span class="profile-label">Type:</span>
                                        <span class="profile-value">${profile.learnedData.machine.type}</span>
                                    </div>
                                    <div class="profile-item">
                                        <span class="profile-label">Niveau de Puissance:</span>
                                        <span class="profile-value">${profile.learnedData.machine.powerLevel}</span>
                                    </div>
                                    <div class="profile-item">
                                        <span class="profile-label">Puissance CPU:</span>
                                        <span class="profile-value">${profile.learnedData.machine.cpuPower}</span>
                                    </div>
                                </div>
                            ` : ''}

                            ${profile.learnedData.user ? `
                                <div class="section">
                                    <h3>👤 Profil Utilisateur</h3>
                                    <div class="profile-item">
                                        <span class="profile-label">Nom d'utilisateur:</span>
                                        <span class="profile-value">${profile.learnedData.user.username}</span>
                                    </div>
                                    <div class="profile-item">
                                        <span class="profile-label">Répertoire Home:</span>
                                        <span class="profile-value">${profile.learnedData.user.homeDirectory}</span>
                                    </div>
                                    <div class="profile-item">
                                        <span class="profile-label">Applications Préférées:</span>
                                        <span class="profile-value">${profile.learnedData.user.preferredApps.join(', ')}</span>
                                    </div>
                                    <div class="profile-item">
                                        <span class="profile-label">Langue Système:</span>
                                        <span class="profile-value">${profile.learnedData.user.systemLanguage}</span>
                                    </div>
                                </div>
                            ` : ''}

                            ${profile.learnedData.capabilities ? `
                                <div class="section">
                                    <h3>⚡ Capacités Système</h3>
                                    <div class="profile-item">
                                        <span class="profile-label">Applications Lourdes:</span>
                                        <span class="profile-value">${profile.learnedData.capabilities.canRunHeavyApps ? '✅ Supportées' : '❌ Non supportées'}</span>
                                    </div>
                                    <div class="profile-item">
                                        <span class="profile-label">CPU Performant:</span>
                                        <span class="profile-value">${profile.learnedData.capabilities.hasGoodCPU ? '✅ Oui' : '❌ Non'}</span>
                                    </div>
                                    <div class="profile-item">
                                        <span class="profile-label">Système Moderne:</span>
                                        <span class="profile-value">${profile.learnedData.capabilities.isModernSystem ? '✅ Oui' : '❌ Non'}</span>
                                    </div>
                                    <div class="profile-item">
                                        <span class="profile-label">Formats Supportés:</span>
                                        <span class="profile-value">${profile.learnedData.capabilities.supportedFormats.join(', ')}</span>
                                    </div>
                                </div>
                            ` : ''}

                            ${result.brainStats ? `
                                <div class="section brain-stats">
                                    <h3>🧠 Impact sur le Cerveau Artificiel</h3>
                                    <div class="profile-item">
                                        <span class="profile-label">Neurones Actifs:</span>
                                        <span class="profile-value">${result.brainStats.activeNeurons}</span>
                                    </div>
                                    <div class="profile-item">
                                        <span class="profile-label">Connexions Synaptiques:</span>
                                        <span class="profile-value">${result.brainStats.synapticConnections}</span>
                                    </div>
                                    <div class="profile-item">
                                        <span class="profile-label">Capacité d'Apprentissage:</span>
                                        <span class="profile-value">${(result.brainStats.learningCapacity * 100).toFixed(1)}%</span>
                                    </div>
                                </div>
                            ` : ''}
                        </body>
                        </html>
                    `);
                } else {
                    showNotification('❌ Erreur récupération profil: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur profil machine: ' + error.message, 'error');
            }
        }

        // Statut sécurité mémoire
        async function showSecurityStatus() {
            try {
                showNotification('🛡️ Récupération du statut de sécurité...', 'info');

                const response = await fetch('/api/memory/security/status');
                const result = await response.json();

                if (result.success) {
                    const status = result.securityStatus;

                    // Afficher le statut détaillé
                    const popup = document.createElement('div');
                    popup.className = 'popup-overlay';
                    popup.innerHTML = `
                        <div class="popup-content">
                            <h2>🛡️ Statut Sécurité Mémoire Thermique</h2>
                            <div class="security-status">
                                <div class="status-item">
                                    <span class="label">🔌 Connexion:</span>
                                    <span class="value ${status.isConnected ? 'connected' : 'disconnected'}">
                                        ${status.isConnected ? '✅ Connectée' : '❌ Déconnectée'}
                                    </span>
                                </div>
                                <div class="status-item">
                                    <span class="label">🚨 Mode Urgence:</span>
                                    <span class="value ${status.emergencyMode ? 'emergency' : 'normal'}">
                                        ${status.emergencyMode ? '🚨 Activé' : '✅ Normal'}
                                    </span>
                                </div>
                                <div class="status-item">
                                    <span class="label">⚠️ Niveau Menace:</span>
                                    <span class="value threat-${status.threatLevel.toLowerCase()}">
                                        ${status.threatLevel}
                                    </span>
                                </div>
                                <div class="status-item">
                                    <span class="label">🦠 Antivirus:</span>
                                    <span class="value ${status.antivirusActive ? 'active' : 'inactive'}">
                                        ${status.antivirusActive ? '🔄 Actif' : '⏸️ Inactif'}
                                    </span>
                                </div>
                                <div class="status-item">
                                    <span class="label">🚨 Alertes:</span>
                                    <span class="value">${status.alertsCount}</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">🔍 Dernier Scan:</span>
                                    <span class="value">${status.lastScan ? new Date(status.lastScan.timestamp).toLocaleString() : 'Aucun'}</span>
                                </div>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="close-btn">Fermer</button>
                        </div>
                    `;
                    document.body.appendChild(popup);
                } else {
                    showNotification(`❌ Erreur récupération statut: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur statut sécurité: ' + error.message, 'error');
            }
        }

        // Nettoyer les logs d'erreur
        async function cleanErrorLogs() {
            try {
                showNotification('🔧 Nettoyage des logs d\'erreur en cours...', 'info');

                const response = await fetch('/api/logs/clean-errors', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    const cleanupResults = result.cleanupResults;

                    showNotification(`✅ Nettoyage terminé ! ${cleanupResults.totalCleaned} entrées nettoyées`, 'success');

                    // Afficher les résultats détaillés
                    const popup = document.createElement('div');
                    popup.className = 'popup-overlay';
                    popup.innerHTML = `
                        <div class="popup-content">
                            <h2>🔧 Résultats Nettoyage Logs d'Erreur</h2>
                            <div class="cleanup-results">
                                <div class="result-item">
                                    <span class="label">❌ Erreurs Trouvées:</span>
                                    <span class="value">${cleanupResults.errorsFound}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">✅ Erreurs Corrigées:</span>
                                    <span class="value">${cleanupResults.errorsFixed}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">⚠️ Avertissements Trouvés:</span>
                                    <span class="value">${cleanupResults.warningsFound}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">✅ Avertissements Corrigés:</span>
                                    <span class="value">${cleanupResults.warningsFixed}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">🧹 Total Nettoyé:</span>
                                    <span class="value">${cleanupResults.totalCleaned}</span>
                                </div>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="close-btn">Fermer</button>
                        </div>
                    `;
                    document.body.appendChild(popup);
                } else {
                    showNotification(`❌ Erreur nettoyage logs: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur nettoyage logs: ' + error.message, 'error');
            }
        }

        // Scanner et réparer les logs
        async function scanRepairLogs() {
            try {
                showNotification('🔍 Scan et réparation des logs en cours...', 'info');

                const response = await fetch('/api/logs/scan-repair', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    const scanResults = result.scanResults;

                    showNotification(`✅ Scan terminé ! Statut: ${scanResults.status}`, 'success');

                    // Afficher les résultats détaillés
                    const popup = document.createElement('div');
                    popup.className = 'popup-overlay';
                    popup.innerHTML = `
                        <div class="popup-content">
                            <h2>🔍 Résultats Scan & Réparation Logs</h2>
                            <div class="scan-results">
                                <div class="result-item">
                                    <span class="label">📊 Statut:</span>
                                    <span class="value status-${scanResults.status}">${scanResults.status.toUpperCase()}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">📝 Logs Scannés:</span>
                                    <span class="value">${scanResults.totalScanned}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">🚨 Problèmes Trouvés:</span>
                                    <span class="value">${scanResults.issuesFound}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">🔧 Problèmes Réparés:</span>
                                    <span class="value">${scanResults.issuesRepaired}</span>
                                </div>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="close-btn">Fermer</button>
                        </div>
                    `;
                    document.body.appendChild(popup);
                } else {
                    showNotification(`❌ Erreur scan logs: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur scan logs: ' + error.message, 'error');
            }
        }

        // Générer des neurones
        async function generateNeurons() {
            try {
                showNotification('🧠 Génération de nouveaux neurones...', 'info');

                const response = await fetch('/api/brain/generate-neurons', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ count: 10 })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ ${result.neuronsGenerated} nouveaux neurones générés !`, 'success');

                    // Mettre à jour les métriques
                    if (result.brainStats) {
                        updateMetrics({
                            brainStats: result.brainStats
                        });
                    }
                } else {
                    showNotification(`❌ Erreur génération neurones: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur génération neurones: ' + error.message, 'error');
            }
        }

        // Formation accélérée
        async function accelerateTraining() {
            if (!confirm('⚡ Voulez-vous vraiment démarrer la formation accélérée ? Cela va générer massivement des neurones.')) {
                return;
            }

            try {
                showNotification('⚡ Démarrage de la formation accélérée...', 'info');

                const response = await fetch('/api/training/accelerated', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        mode: 'intensive',
                        duration: 30 // 30 secondes
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ Formation accélérée démarrée ! Mode: ${result.mode}`, 'success');

                    // Afficher les détails
                    const popup = document.createElement('div');
                    popup.className = 'popup-overlay';
                    popup.innerHTML = `
                        <div class="popup-content">
                            <h2>⚡ Formation Accélérée Active</h2>
                            <div class="training-details">
                                <div class="result-item">
                                    <span class="label">🎯 Mode:</span>
                                    <span class="value">${result.mode}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">⏱️ Durée:</span>
                                    <span class="value">${result.duration}s</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">🧠 Neurones/sec:</span>
                                    <span class="value">${result.neuronsPerSecond}</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">📊 Statut:</span>
                                    <span class="value">En cours...</span>
                                </div>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="close-btn">Fermer</button>
                        </div>
                    `;
                    document.body.appendChild(popup);
                } else {
                    showNotification(`❌ Erreur formation accélérée: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur formation accélérée: ' + error.message, 'error');
            }
        }

            document.getElementById('messageInput').focus();
        });

        // ===== INITIALISATION AUTOMATIQUE =====

        // Initialisation automatique au chargement de la page
        async function initializeApp() {
            console.log('🚀 Initialisation de LOUNA AI...');

            try {
                // Vérifier la connexion
                await checkConnection();

                // Initialiser la reconnaissance vocale
                initSpeechRecognition();

                // Vérifier la connexion périodiquement
                setInterval(checkConnection, 30000); // Toutes les 30 secondes

                // Focus sur le champ de saisie
                document.getElementById('messageInput').focus();

                console.log('✅ LOUNA AI initialisée et prête !');
                updateStatus('ready', 'LOUNA AI prête - Vous pouvez maintenant envoyer des messages');

            } catch (error) {
                console.error('❌ Erreur initialisation:', error);
                updateStatus('error', 'Erreur d\'initialisation');
            }
        }

        // Démarrer l'initialisation quand la page est chargée
        document.addEventListener('DOMContentLoaded', initializeApp);

        // Fallback pour les navigateurs plus anciens
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            // Si la page est déjà chargée, initialiser immédiatement
            setTimeout(initializeApp, 100);
        }

    </script>
</body>
</html>
