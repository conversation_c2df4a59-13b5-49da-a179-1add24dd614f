#!/bin/bash

# 🛑 LOUNA AI - ARRÊT MASTER
# Script d'arrêt propre du serveur master

echo "🛑 ================================"
echo "🛑 ARRÊT LOUNA AI MASTER"
echo "🛑 ================================"

# 🛑 ARRÊTER LE SERVEUR MASTER
echo "🛑 Arrêt du serveur master..."
pkill -f "server-master.js" 2>/dev/null || true

# 🧹 NETTOYAGE DES PORTS
echo "🧹 Libération du port 52796..."
lsof -ti:52796 | xargs kill -9 2>/dev/null || true

# 🗑️ SUPPRIMER LE FICHIER PID
if [ -f "louna-master.pid" ]; then
    rm -f louna-master.pid
    echo "🗑️ Fichier PID supprimé"
fi

# ⏱️ ATTENDRE
sleep 2

# 🔍 VÉRIFICATION
if ! curl -s http://localhost:52796/api/metrics > /dev/null 2>&1; then
    echo "✅ LOUNA AI Master arrêté avec succès"
    echo "🛑 ================================"
else
    echo "⚠️ Le serveur semble encore actif"
    echo "🔧 Essayez: pkill -f node"
fi
