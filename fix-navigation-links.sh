#!/bin/bash

echo "🔧 ================================"
echo "🛠️ CORRECTION LIENS NAVIGATION"
echo "🔧 ================================"

# Fonction pour afficher les corrections
show_fix() {
    echo "✅ CORRECTION: $1"
}

echo "🔍 RECHERCHE DES LIENS PROBLÉMATIQUES..."
echo ""

# 1. CORRIGER TOUS LES LIENS VERS FUTURISTIC-INTERFACE
show_fix "Correction liens futuristic-interface.html"

# Rechercher tous les fichiers avec des liens problématiques
echo "📁 Fichiers avec liens problématiques:"
grep -l "futuristic-interface\|enhanced-interface" public/*.html | while read file; do
    echo "   - $file"
done

echo ""

# 2. CORRIGER LES LIENS VERS L'ACCUEIL
show_fix "Correction liens vers accueil"

# Remplacer tous les liens vers l'ancienne interface
find public -name "*.html" -exec sed -i '' 's|href="/futuristic-interface.html"|href="/interface-spectaculaire.html"|g' {} \;
find public -name "*.html" -exec sed -i '' 's|href="futuristic-interface.html"|href="/interface-spectaculaire.html"|g' {} \;
find public -name "*.html" -exec sed -i '' "s|window.open('futuristic-interface.html'|window.open('/interface-spectaculaire.html'|g" {} \;

echo "✅ Liens futuristic-interface corrigés"

# 3. CORRIGER LES LIENS VERS L'ACCUEIL BASIQUE
find public -name "*.html" -exec sed -i '' 's|href="/"|href="/interface-spectaculaire.html"|g' {} \;
find public -name "*.html" -exec sed -i '' 's|href="#"|href="/interface-spectaculaire.html"|g' {} \;

echo "✅ Liens accueil basique corrigés"

# 4. AJOUTER BOUTONS DE RETOUR MANQUANTS
show_fix "Ajout boutons de retour manquants"

# Liste des fichiers qui ont besoin de boutons de retour
FILES_NEED_HOME=(
    "public/brain-monitoring-complete.html"
    "public/brain-visualization.html"
    "public/brain-3d-spectacular.html"
    "public/kyber-dashboard.html"
    "public/qi-test-ultra-avance.html"
    "public/image-generator.html"
    "public/monitoring-ultra-avance.html"
)

for file in "${FILES_NEED_HOME[@]}"; do
    if [ -f "$file" ]; then
        # Vérifier si le bouton de retour existe déjà
        if ! grep -q "Accueil Spectaculaire" "$file"; then
            echo "🔧 Ajout bouton retour dans: $file"
            
            # Ajouter le bouton de retour dans le header ou body
            if grep -q "<header>" "$file"; then
                # Ajouter dans le header
                sed -i '' '/<header>/a\
                <button onclick="window.location.href='"'"'/interface-spectaculaire.html'"'"'" style="position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 10px 20px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 20px; cursor: pointer; font-weight: 600; box-shadow: 0 4px 15px rgba(0,0,0,0.3);">\
                    🏠 Accueil Spectaculaire\
                </button>' "$file"
            elif grep -q "<body>" "$file"; then
                # Ajouter dans le body
                sed -i '' '/<body>/a\
                <button onclick="window.location.href='"'"'/interface-spectaculaire.html'"'"'" style="position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 10px 20px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 20px; cursor: pointer; font-weight: 600; box-shadow: 0 4px 15px rgba(0,0,0,0.3);">\
                    🏠 Accueil Spectaculaire\
                </button>' "$file"
            fi
        else
            echo "✅ Bouton retour déjà présent dans: $file"
        fi
    fi
done

# 5. VÉRIFICATION FINALE
echo ""
echo "🔍 VÉRIFICATION FINALE:"
echo "======================="

# Vérifier qu'il ne reste plus de liens problématiques
REMAINING_BAD_LINKS=$(grep -r "futuristic-interface" public/*.html 2>/dev/null | wc -l)
echo "🔍 Liens futuristic-interface restants: $REMAINING_BAD_LINKS"

if [ "$REMAINING_BAD_LINKS" -eq 0 ]; then
    echo "✅ Tous les liens futuristic-interface corrigés"
else
    echo "⚠️ Il reste $REMAINING_BAD_LINKS liens à corriger"
    echo "📋 Liens restants:"
    grep -r "futuristic-interface" public/*.html 2>/dev/null | head -5
fi

# 6. VÉRIFIER LES LIENS VERS L'ACCUEIL SPECTACULAIRE
SPECTACULAIRE_LINKS=$(grep -r "interface-spectaculaire.html" public/*.html 2>/dev/null | wc -l)
echo "🏠 Liens vers interface spectaculaire: $SPECTACULAIRE_LINKS"

# 7. TESTER LES PAGES PRINCIPALES
echo ""
echo "🧪 TEST RAPIDE DES PAGES PRINCIPALES:"
echo "===================================="

BASE_URL="http://localhost:52796"

test_page() {
    local url="$1"
    local name="$2"
    
    if curl -s -f "$BASE_URL$url" > /dev/null 2>&1; then
        echo "✅ $name: OK"
    else
        echo "❌ $name: ERREUR"
    fi
}

test_page "/interface-spectaculaire.html" "Interface Spectaculaire"
test_page "/chat-cognitif-ultra-optimise.html" "Chat Ultra-Optimisé"
test_page "/enhanced-interface.html" "Interface Ultra-Avancée"
test_page "/thermal-memory-dashboard.html" "Mémoire Thermique"

echo ""
echo "📊 RÉSUMÉ DES CORRECTIONS:"
echo "========================="

echo "✅ Corrections appliquées:"
echo "   1. Liens futuristic-interface → interface-spectaculaire"
echo "   2. Liens accueil basique → interface-spectaculaire"
echo "   3. Boutons de retour ajoutés aux pages principales"
echo "   4. Navigation cohérente vers interfaces modernes"

echo ""
echo "🎯 ACTIONS RECOMMANDÉES:"
echo "========================"
echo "• Redémarrer l'application Electron si nécessaire"
echo "• Tester la navigation depuis chaque interface"
echo "• Vérifier que tous les boutons de retour fonctionnent"

echo ""
echo "🔧 ================================"
echo "✅ CORRECTION LIENS TERMINÉE"
echo "🔧 ================================"

# 8. VÉRIFIER LE SYSTÈME THERMIQUE
echo ""
echo "🌡️ VÉRIFICATION SYSTÈME THERMIQUE:"
echo "=================================="

if curl -s "http://localhost:52796/api/thermal-status" > /dev/null 2>&1; then
    echo "✅ Système thermique accessible"
    
    # Récupérer les métriques thermiques
    THERMAL_DATA=$(curl -s "http://localhost:52796/api/thermal-status" 2>/dev/null)
    if [ ! -z "$THERMAL_DATA" ]; then
        echo "🌡️ Données thermiques récupérées"
    else
        echo "⚠️ Données thermiques vides"
    fi
else
    echo "⚠️ Système thermique non accessible"
fi

echo ""
echo "🎉 TOUTES LES CORRECTIONS TERMINÉES !"
echo "🏠 Navigation corrigée vers interface-spectaculaire.html"
echo "🔧 Système thermique préservé"
