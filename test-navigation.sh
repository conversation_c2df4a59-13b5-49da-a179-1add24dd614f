#!/bin/bash

echo "🧭 ================================"
echo "🔍 TEST NAVIGATION LOUNA AI"
echo "🧭 ================================"

# URL de base
BASE_URL="http://localhost:52796"

# Fonction pour tester une URL
test_url() {
    local url="$1"
    local name="$2"
    
    echo -n "🔍 Test $name... "
    
    if curl -s -f "$BASE_URL$url" > /dev/null; then
        echo "✅ OK"
        return 0
    else
        echo "❌ ÉCHEC"
        return 1
    fi
}

# Vérifier que le serveur fonctionne
echo "🌐 Vérification serveur..."
if ! curl -s "$BASE_URL" > /dev/null; then
    echo "❌ Serveur non accessible sur $BASE_URL"
    echo "💡 Démarrez le serveur avec: npm start"
    exit 1
fi
echo "✅ Serveur accessible"
echo ""

# Tests des pages principales
echo "🏠 TEST PAGES PRINCIPALES:"
echo "=========================="

test_url "/interface-spectaculaire.html" "🏠 Accueil Spectaculaire"

echo ""
echo "💬 TEST INTERFACES CHAT:"
echo "========================"

test_url "/chat-cognitif-complet.html" "💬 Chat IA Cognitif"
test_url "/simple-chat.html" "💬 Chat Simple"
test_url "/mcp-chat-interface.html" "💬 Chat MCP"
test_url "/streaming-chat.html" "💬 Chat Streaming"

echo ""
echo "🧠 TEST INTERFACES CERVEAU:"
echo "==========================="

test_url "/brain-monitoring-complete.html" "🧠 Monitoring Cerveau"
test_url "/brain-3d-spectacular.html" "🧠 Cerveau 3D Spectaculaire"
test_url "/brain-visualization.html" "🧠 Visualisation Cerveau"
test_url "/neural-interface.html" "🧠 Interface Neurale"

echo ""
echo "🔧 TEST DIAGNOSTIC:"
echo "=================="

test_url "/kyber-dashboard.html" "🔧 Kyber Dashboard"
test_url "/monitoring-ultra-avance.html" "🔧 Monitoring Ultra-Avancé"
test_url "/control-dashboard.html" "🔧 Dashboard Contrôle"

echo ""
echo "🧠 TEST QI:"
echo "==========="

test_url "/qi-test-ultra-avance.html" "🧠 Tests QI Ultra-Avancés"
test_url "/qi-evolution-test.html" "🧠 Tests Évolution QI"
test_url "/qi-manager.html" "🧠 Gestionnaire QI"

echo ""
echo "✨ TEST GÉNÉRATEURS:"
echo "==================="

test_url "/image-generator.html" "✨ Générateur Images"
test_url "/virtual-code-studio.html" "✨ Studio Code Virtuel"
test_url "/advanced-voice-system.html" "✨ Système Vocal Avancé"

echo ""
echo "🧠 TEST MÉMOIRE THERMIQUE:"
echo "=========================="

test_url "/thermal-memory-dashboard.html" "🧠 Dashboard Mémoire Thermique"
test_url "/thermal-paradigm-explorer.html" "🧠 Explorateur Paradigme"
test_url "/neuron-recovery-emergency.html" "🧠 Récupération Neurones"

echo ""
echo "⚙️ TEST SYSTÈME:"
echo "================"

test_url "/documentation.html" "⚙️ Documentation"
test_url "/privacy-control.html" "⚙️ Contrôle Confidentialité"

echo ""
echo "🎓 TEST FORMATION:"
echo "=================="

test_url "/training-interface.html" "🎓 Interface Formation"
test_url "/learning-dashboard.html" "🎓 Dashboard Apprentissage"

echo ""
echo "🔬 TEST DÉVELOPPEMENT:"
echo "====================="

test_url "/agent-test-suite.html" "🔬 Suite Tests Agent"
test_url "/quick-test.html" "🔬 Test Rapide"
test_url "/mcp-test-interface.html" "🔬 Interface Test MCP"

echo ""
echo "📊 RÉSUMÉ DES TESTS:"
echo "==================="

# Compter les succès et échecs
success_count=0
total_count=0

# Relancer tous les tests pour compter
urls=(
    "/interface-spectaculaire.html"
    "/chat-cognitif-complet.html"
    "/simple-chat.html"
    "/mcp-chat-interface.html"
    "/streaming-chat.html"
    "/brain-monitoring-complete.html"
    "/brain-3d-spectacular.html"
    "/brain-visualization.html"
    "/neural-interface.html"
    "/kyber-dashboard.html"
    "/monitoring-ultra-avance.html"
    "/control-dashboard.html"
    "/qi-test-ultra-avance.html"
    "/qi-evolution-test.html"
    "/qi-manager.html"
    "/image-generator.html"
    "/virtual-code-studio.html"
    "/advanced-voice-system.html"
    "/thermal-memory-dashboard.html"
    "/thermal-paradigm-explorer.html"
    "/neuron-recovery-emergency.html"
    "/documentation.html"
    "/privacy-control.html"
    "/training-interface.html"
    "/learning-dashboard.html"
    "/agent-test-suite.html"
    "/quick-test.html"
    "/mcp-test-interface.html"
)

for url in "${urls[@]}"; do
    total_count=$((total_count + 1))
    if curl -s -f "$BASE_URL$url" > /dev/null; then
        success_count=$((success_count + 1))
    fi
done

echo "✅ Pages accessibles: $success_count"
echo "❌ Pages en erreur: $((total_count - success_count))"
echo "📊 Total testé: $total_count"

# Calculer le pourcentage
percentage=$((success_count * 100 / total_count))
echo "📈 Taux de réussite: $percentage%"

echo ""
if [ $percentage -eq 100 ]; then
    echo "🎉 PARFAIT ! Toutes les pages sont accessibles !"
    echo "✅ La navigation est entièrement fonctionnelle"
elif [ $percentage -ge 90 ]; then
    echo "🎯 EXCELLENT ! Navigation quasi-parfaite"
    echo "⚠️ Quelques pages mineures à corriger"
elif [ $percentage -ge 75 ]; then
    echo "👍 BON ! Navigation majoritairement fonctionnelle"
    echo "🔧 Quelques corrections nécessaires"
else
    echo "⚠️ ATTENTION ! Navigation nécessite des corrections"
    echo "🔧 Plusieurs pages à réparer"
fi

echo ""
echo "🧭 ================================"
echo "✅ TEST NAVIGATION TERMINÉ"
echo "🧭 ================================"
echo ""
echo "🎯 Pour tester manuellement:"
echo "• Ouvrez: $BASE_URL/interface-spectaculaire.html"
echo "• Cliquez sur chaque bouton de navigation"
echo "• Vérifiez que les pages s'ouvrent correctement"
