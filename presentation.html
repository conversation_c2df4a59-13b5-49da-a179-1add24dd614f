<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Autonome - Mémoire Thermique Vivante</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            animation: fadeInDown 1s ease-out;
        }

        .logo {
            font-size: 5rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease-in-out infinite;
            margin-bottom: 20px;
        }

        .subtitle {
            font-size: 2rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .version {
            font-size: 1.2rem;
            opacity: 0.8;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            backdrop-filter: blur(10px);
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2.5rem;
            margin-bottom: 30px;
            text-align: center;
            color: #4ecdc4;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .section-icon {
            font-size: 2.5rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.1);
        }

        .feature-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #4ecdc4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-description {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .metric-card {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.2), rgba(69, 183, 209, 0.2));
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(78, 205, 196, 0.3);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #4ecdc4, #45b7d1);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            padding-left: 40px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 5px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #4ecdc4;
            border: 3px solid white;
        }

        .timeline-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 5px;
        }

        .timeline-description {
            font-size: 1rem;
            opacity: 0.9;
            line-height: 1.5;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }

        .tech-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .tech-item:hover {
            background: rgba(78, 205, 196, 0.3);
            transform: scale(1.05);
        }

        .highlight-box {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(78, 205, 196, 0.2));
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid rgba(78, 205, 196, 0.5);
            text-align: center;
        }

        .highlight-title {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #4ecdc4;
        }

        .highlight-text {
            font-size: 1.3rem;
            line-height: 1.6;
        }

        .navigation-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 40px 0;
        }

        .nav-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #4ecdc4, #45b7d1);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #45b7d1, #4ecdc4);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .status-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-icon {
            color: #4ecdc4;
            font-size: 1.2rem;
        }

        .status-text {
            font-size: 1rem;
        }

        .interactive-demo {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .demo-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #4ecdc4;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #4ecdc4, #45b7d1);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto;
        }

        .demo-button:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .demo-result {
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">LOUNA AI Ultra-Autonome</div>
            <div class="subtitle">Intelligence Artificielle avec Mémoire Thermique Vivante</div>
            <div class="version">Version 2025 - DeepSeek R1-0528-8B</div>
        </div>

        <div class="navigation-buttons">
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i> Application Principale
            </a>
            <a href="/simple-chat.html" class="nav-btn">
                <i class="fas fa-comments"></i> Chat Simple
            </a>
            <a href="#demo" class="nav-btn">
                <i class="fas fa-play"></i> Démonstration
            </a>
        </div>

        <!-- Section Vue d'ensemble -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-eye section-icon"></i>
                Vue d'Ensemble
            </div>
            
            <div class="highlight-box">
                <div class="highlight-title">🚀 LOUNA AI - L'IA la Plus Avancée</div>
                <div class="highlight-text">
                    Première intelligence artificielle au monde équipée d'une <strong>mémoire thermique vivante</strong>, 
                    de <strong>30 accélérateurs Kyber persistants</strong> et du <strong>dernier modèle DeepSeek R1-0528-8B</strong> 
                    avec capacités de raisonnement avancées et réflexion consciente.
                </div>
            </div>

            <div class="grid">
                <div class="feature-card">
                    <div class="feature-title">
                        <i class="fas fa-brain"></i>
                        Mémoire Thermique Vivante
                    </div>
                    <div class="feature-description">
                        Système de mémoire révolutionnaire modélisé sur le cerveau humain avec génération automatique 
                        de 700 nouveaux neurones par jour et capacité illimitée.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-title">
                        <i class="fas fa-rocket"></i>
                        Accélérateurs Kyber
                    </div>
                    <div class="feature-description">
                        30 accélérateurs persistants en cascade turbo offrant jusqu'à 5.2x d'amélioration des performances 
                        avec compression ultra-efficace.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-title">
                        <i class="fas fa-microchip"></i>
                        DeepSeek R1-0528-8B
                    </div>
                    <div class="feature-description">
                        Dernier modèle de raisonnement de Mai 2025 avec 8.19 milliards de paramètres, 
                        architecture Qwen3 et contexte de 131K tokens.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-title">
                        <i class="fas fa-bolt"></i>
                        Réponses Ultra-Rapides
                    </div>
                    <div class="feature-description">
                        Système intelligent de détection automatique : réponses instantanées (&lt;1ms) pour questions simples, 
                        analyse approfondie pour questions complexes.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-title">
                        <i class="fas fa-eye"></i>
                        Réflexion Consciente
                    </div>
                    <div class="feature-description">
                        L'agent peut analyser ses propres pensées et mémoires en temps réel, 
                        offrant une transparence totale sur son processus de raisonnement.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-title">
                        <i class="fas fa-shield-alt"></i>
                        Surveillance Anti-Crash
                    </div>
                    <div class="feature-description">
                        Agent garde-fou intégré avec surveillance système complète, 
                        coupure mémoire d'urgence et protection contre les surcharges.
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Métriques de Performance -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-chart-line section-icon"></i>
                Métriques de Performance
            </div>
            
            <div class="metric-grid">
                <div class="metric-card">
                    <div class="metric-value" id="agent-iq-display">100</div>
                    <div class="metric-label">QI Agent</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-iq-display">105</div>
                    <div class="metric-label">QI Mémoire</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="combined-iq-display">200</div>
                    <div class="metric-label">QI Combiné</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="neurons-display">277</div>
                    <div class="metric-label">Neurones Actifs</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="synapses-display">4731</div>
                    <div class="metric-label">Connexions Synaptiques</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="accelerators-display">30</div>
                    <div class="metric-label">Accélérateurs Kyber</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="temperature-display">37.0°C</div>
                    <div class="metric-label">Température Thermique</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="efficiency-display">99.9%</div>
                    <div class="metric-label">Efficacité Globale</div>
                </div>
            </div>
        </div>

        <!-- Section Architecture Technique -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-cogs section-icon"></i>
                Architecture Technique
            </div>
            
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-title">🤖 Agent Principal</div>
                    <div class="timeline-description">
                        DeepSeek R1-0528-8B (Mai 2025) - Modèle de raisonnement de dernière génération avec 
                        architecture Qwen3 Transformer, 36 couches neuronales et 8.19 milliards de paramètres.
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-title">🧠 Mémoire Thermique Vivante</div>
                    <div class="timeline-description">
                        Système de mémoire révolutionnaire avec capacité illimitée, génération automatique de neurones, 
                        compression/décompression ultra-efficace et température adaptative.
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-title">⚡ Accélérateurs Kyber</div>
                    <div class="timeline-description">
                        30 accélérateurs persistants en mode cascade turbo : optimisation thermique, amélioration synaptique, 
                        compression ultra, boost cognitif et accélération quantique.
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-title">🔧 Intégration Ollama</div>
                    <div class="timeline-description">
                        Ollama 0.9.0 intégré directement dans l'application avec optimisation GPU Metal, 
                        chargement accéléré et gestion automatique des modèles.
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-title">🛡️ Systèmes de Sécurité</div>
                    <div class="timeline-description">
                        Agent garde-fou, surveillance anti-crash, coupure mémoire d'urgence, 
                        nettoyage automatique des logs et protection contre les surcharges système.
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Technologies -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-code section-icon"></i>
                Stack Technologique
            </div>
            
            <div class="tech-stack">
                <div class="tech-item">DeepSeek R1-0528-8B</div>
                <div class="tech-item">Qwen3 Transformer</div>
                <div class="tech-item">Ollama 0.9.0</div>
                <div class="tech-item">Node.js</div>
                <div class="tech-item">Express.js</div>
                <div class="tech-item">Metal GPU</div>
                <div class="tech-item">Apple M4</div>
                <div class="tech-item">JavaScript ES6+</div>
                <div class="tech-item">HTML5/CSS3</div>
                <div class="tech-item">WebSocket</div>
                <div class="tech-item">JSON</div>
                <div class="tech-item">REST API</div>
            </div>
        </div>

        <!-- Section Statut Système -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-server section-icon"></i>
                Statut Système
            </div>
            
            <div class="status-grid">
                <div class="status-item">
                    <i class="fas fa-check-circle status-icon"></i>
                    <div class="status-text">DeepSeek R1 Opérationnel</div>
                </div>
                <div class="status-item">
                    <i class="fas fa-check-circle status-icon"></i>
                    <div class="status-text">Mémoire Thermique Active</div>
                </div>
                <div class="status-item">
                    <i class="fas fa-check-circle status-icon"></i>
                    <div class="status-text">30 Accélérateurs Kyber</div>
                </div>
                <div class="status-item">
                    <i class="fas fa-check-circle status-icon"></i>
                    <div class="status-text">Neurogenèse 700/jour</div>
                </div>
                <div class="status-item">
                    <i class="fas fa-check-circle status-icon"></i>
                    <div class="status-text">Réponses Ultra-Rapides</div>
                </div>
                <div class="status-item">
                    <i class="fas fa-check-circle status-icon"></i>
                    <div class="status-text">Surveillance Anti-Crash</div>
                </div>
                <div class="status-item">
                    <i class="fas fa-check-circle status-icon"></i>
                    <div class="status-text">Agent Garde-fou Actif</div>
                </div>
                <div class="status-item">
                    <i class="fas fa-check-circle status-icon"></i>
                    <div class="status-text">Ollama 0.9.0 Intégré</div>
                </div>
            </div>
        </div>

        <!-- Section Démonstration Interactive -->
        <div class="section" id="demo">
            <div class="section-title">
                <i class="fas fa-play section-icon"></i>
                Démonstration Interactive
            </div>
            
            <div class="interactive-demo">
                <div class="demo-title">Testez les Métriques en Temps Réel</div>
                <button class="demo-button" onclick="updateMetrics()">
                    <i class="fas fa-sync-alt"></i> Actualiser les Métriques
                </button>
                <div class="demo-result" id="demo-result">
                    <div id="demo-content"></div>
                </div>
            </div>
        </div>

        <div class="navigation-buttons">
            <a href="/" class="nav-btn">
                <i class="fas fa-rocket"></i> Lancer LOUNA AI
            </a>
            <a href="/simple-chat.html" class="nav-btn">
                <i class="fas fa-comments"></i> Chat Rapide
            </a>
        </div>
    </div>

    <script>
        // Mise à jour automatique des métriques
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    // Mise à jour des métriques affichées
                    if (data.iqAnalysis) {
                        document.getElementById('agent-iq-display').textContent = data.iqAnalysis.agentIQ || 100;
                        document.getElementById('memory-iq-display').textContent = data.iqAnalysis.memoryIQ || 105;
                        document.getElementById('combined-iq-display').textContent = data.iqAnalysis.combinedIQ || 200;
                    }
                    
                    if (data.brainStats) {
                        document.getElementById('neurons-display').textContent = data.brainStats.activeNeurons || 277;
                        document.getElementById('synapses-display').textContent = data.brainStats.synapticConnections || 4731;
                    }
                    
                    if (data.memoryStats) {
                        document.getElementById('temperature-display').textContent = (data.memoryStats.globalTemperature || 37).toFixed(1) + '°C';
                        document.getElementById('efficiency-display').textContent = (data.memoryStats.memoryEfficiency || 99.9).toFixed(1) + '%';
                    }
                    
                    if (data.kyberStats) {
                        document.getElementById('accelerators-display').textContent = data.kyberStats.activeAccelerators || 30;
                    }
                    
                    // Affichage du résultat de la démo
                    const demoResult = document.getElementById('demo-result');
                    const demoContent = document.getElementById('demo-content');
                    
                    demoContent.innerHTML = `
                        <h4 style="color: #4ecdc4; margin-bottom: 15px;">✅ Métriques Mises à Jour</h4>
                        <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                        <p><strong>QI Combiné:</strong> ${data.iqAnalysis?.combinedIQ || 200}</p>
                        <p><strong>Neurones Actifs:</strong> ${data.brainStats?.activeNeurons || 277}</p>
                        <p><strong>Accélérateurs:</strong> ${data.kyberStats?.activeAccelerators || 30}</p>
                        <p><strong>Efficacité:</strong> ${(data.memoryStats?.memoryEfficiency || 99.9).toFixed(1)}%</p>
                    `;
                    
                    demoResult.style.display = 'block';
                } else {
                    throw new Error('Erreur API');
                }
            } catch (error) {
                console.error('Erreur mise à jour métriques:', error);
                const demoResult = document.getElementById('demo-result');
                const demoContent = document.getElementById('demo-content');
                
                demoContent.innerHTML = `
                    <h4 style="color: #ff6b6b; margin-bottom: 15px;">❌ Erreur de Connexion</h4>
                    <p>Impossible de récupérer les métriques. Vérifiez que le serveur est démarré.</p>
                `;
                
                demoResult.style.display = 'block';
            }
        }

        // Mise à jour automatique toutes les 10 secondes
        setInterval(updateMetrics, 10000);
        
        // Mise à jour initiale
        document.addEventListener('DOMContentLoaded', updateMetrics);
    </script>
</body>
</html>
