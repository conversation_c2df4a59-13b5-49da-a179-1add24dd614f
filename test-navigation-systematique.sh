#!/bin/bash

echo "🧪 ================================"
echo "🔍 TEST NAVIGATION SYSTÉMATIQUE"
echo "🧪 ================================"

BASE_URL="http://localhost:52796"

# Fonction pour tester une URL et vérifier le contenu
test_interface_content() {
    local url="$1"
    local name="$2"
    
    echo ""
    echo "🔍 TEST: $name"
    echo "📄 URL: $BASE_URL$url"
    
    # Télécharger le contenu de la page
    local content=$(curl -s "$BASE_URL$url" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ ! -z "$content" ]; then
        echo "✅ Page accessible"
        
        # Vérifier si la page contient des redirections vers futuristic-interface
        if echo "$content" | grep -q "futuristic-interface"; then
            echo "❌ PROBLÈME: Page contient des liens vers futuristic-interface"
            echo "🔍 Liens trouvés:"
            echo "$content" | grep -o "href=['\"][^'\"]*futuristic-interface[^'\"]*['\"]" | head -3
        else
            echo "✅ Aucun lien problématique vers futuristic-interface"
        fi
        
        # Vérifier si la page contient des boutons de retour vers l'accueil
        if echo "$content" | grep -q "interface-spectaculaire"; then
            echo "✅ Bouton de retour vers interface spectaculaire trouvé"
        else
            echo "⚠️ Aucun bouton de retour vers interface spectaculaire"
        fi
        
        # Vérifier le titre de la page
        local title=$(echo "$content" | grep -o "<title>[^<]*</title>" | sed 's/<[^>]*>//g')
        if [ ! -z "$title" ]; then
            echo "📋 Titre: $title"
            
            # Vérifier si le titre contient "futuriste"
            if echo "$title" | grep -qi "futuriste"; then
                echo "⚠️ ATTENTION: Titre contient 'futuriste'"
            fi
        fi
        
    else
        echo "❌ Page non accessible"
    fi
}

# Vérifier que le serveur fonctionne
echo "🌐 Vérification serveur..."
if ! curl -s "$BASE_URL" > /dev/null; then
    echo "❌ Serveur non accessible sur $BASE_URL"
    exit 1
fi
echo "✅ Serveur accessible"

# 1. TEST INTERFACE PRINCIPALE
test_interface_content "/interface-spectaculaire.html" "🏠 Interface Spectaculaire"

# 2. TEST INTERFACES MODERNES
test_interface_content "/chat-cognitif-ultra-optimise.html" "💬 Chat IA Ultra-Optimisé"
test_interface_content "/enhanced-interface.html" "🧠 Interface Cerveau Ultra-Avancée"
test_interface_content "/electron-optimized-interface.html" "🚀 Interface Electron Optimisée"

# 3. TEST INTERFACES SPÉCIALISÉES
test_interface_content "/brain-3d-spectacular.html" "🧠 Cerveau 3D Spectaculaire"
test_interface_content "/kyber-dashboard.html" "🔧 Kyber Dashboard"
test_interface_content "/qi-test-ultra-avance.html" "🧠 Tests QI Ultra-Avancés"
test_interface_content "/thermal-memory-dashboard.html" "🧠 Dashboard Mémoire Thermique"
test_interface_content "/monitoring-ultra-avance.html" "⚙️ Monitoring Ultra-Avancé"

# 4. VÉRIFICATION SPÉCIALE: RECHERCHE DE TOUS LES LIENS PROBLÉMATIQUES
echo ""
echo "🔍 ================================"
echo "🚨 RECHERCHE LIENS PROBLÉMATIQUES"
echo "🔍 ================================"

echo "🔍 Recherche dans tous les fichiers HTML..."

# Rechercher tous les liens vers futuristic-interface
BAD_LINKS=$(find public -name "*.html" -exec grep -l "futuristic-interface" {} \; 2>/dev/null)

if [ ! -z "$BAD_LINKS" ]; then
    echo "❌ FICHIERS AVEC LIENS PROBLÉMATIQUES:"
    for file in $BAD_LINKS; do
        echo "   📄 $file"
        # Afficher les liens problématiques dans ce fichier
        grep -n "futuristic-interface" "$file" | head -2 | while read line; do
            echo "      🔗 $line"
        done
    done
else
    echo "✅ Aucun fichier avec liens problématiques trouvé"
fi

# 5. VÉRIFICATION CONFIGURATION NAVIGATION
echo ""
echo "🧭 VÉRIFICATION CONFIGURATION NAVIGATION:"
echo "========================================"

if [ -f "public/js/navigation-config.js" ]; then
    echo "✅ Fichier navigation-config.js trouvé"
    
    # Vérifier les URLs dans la configuration
    echo "🔍 URLs configurées:"
    grep -o "'/[^']*\.html'" public/js/navigation-config.js | sort | uniq | while read url; do
        clean_url=$(echo $url | tr -d "'")
        echo "   🔗 $clean_url"
        
        # Vérifier si le fichier existe
        if [ -f "public$clean_url" ]; then
            echo "      ✅ Fichier existe"
        else
            echo "      ❌ Fichier manquant"
        fi
    done
else
    echo "❌ Fichier navigation-config.js manquant"
fi

# 6. TEST SPÉCIAL: VÉRIFIER QUE enhanced-interface.html NE REDIRIGE PAS
echo ""
echo "🧠 TEST SPÉCIAL: ENHANCED-INTERFACE"
echo "=================================="

ENHANCED_CONTENT=$(curl -s "$BASE_URL/enhanced-interface.html" 2>/dev/null)
if [ ! -z "$ENHANCED_CONTENT" ]; then
    echo "✅ enhanced-interface.html accessible"
    
    # Vérifier les scripts chargés
    echo "🔍 Scripts chargés:"
    echo "$ENHANCED_CONTENT" | grep -o 'src="[^"]*\.js"' | head -5 | while read script; do
        echo "   📜 $script"
    done
    
    # Vérifier s'il y a des redirections JavaScript
    if echo "$ENHANCED_CONTENT" | grep -q "window.location\|location.href"; then
        echo "⚠️ ATTENTION: Redirections JavaScript détectées"
        echo "$ENHANCED_CONTENT" | grep -n "window.location\|location.href" | head -3
    else
        echo "✅ Aucune redirection JavaScript détectée"
    fi
else
    echo "❌ enhanced-interface.html non accessible"
fi

# 7. RÉSUMÉ FINAL
echo ""
echo "📊 ================================"
echo "📋 RÉSUMÉ FINAL"
echo "📊 ================================"

echo "🎯 Actions à effectuer:"
echo "1. Corriger tous les liens vers futuristic-interface"
echo "2. Vérifier que enhanced-interface.html ne redirige pas"
echo "3. S'assurer que tous les boutons de retour fonctionnent"
echo "4. Tester manuellement chaque bouton dans l'application"

echo ""
echo "🧪 ================================"
echo "✅ TEST SYSTÉMATIQUE TERMINÉ"
echo "🧪 ================================"
