#!/bin/bash

echo "🧪 ================================"
echo "🔍 TEST FINAL CHAQUE BOUTON"
echo "🧪 ================================"

BASE_URL="http://localhost:52796"

# Fonction pour tester une URL
test_page() {
    local url="$1"
    local name="$2"
    
    echo -n "🔍 Test $name... "
    
    if curl -s -f "$BASE_URL$url" > /dev/null; then
        echo "✅ OK"
        return 0
    else
        echo "❌ ÉCHEC"
        return 1
    fi
}

# Vérifier que le serveur fonctionne
echo "🌐 Vérification serveur..."
if ! curl -s "$BASE_URL" > /dev/null; then
    echo "❌ Serveur non accessible sur $BASE_URL"
    exit 1
fi
echo "✅ Serveur accessible"
echo ""

# 1. TEST INTERFACE PRINCIPALE
echo "🏠 TEST INTERFACE PRINCIPALE:"
echo "============================"
test_page "/interface-spectaculaire.html" "🏠 Interface Spectaculaire"

# 2. TEST DESTINATIONS DES BOUTONS PRINCIPAUX
echo ""
echo "🎯 TEST DESTINATIONS BOUTONS PRINCIPAUX:"
echo "========================================"

echo "💬 Bouton Chat IA:"
test_page "/chat-cognitif-ultra-optimise.html" "   → Chat Ultra-Optimisé"

echo "🧠 Bouton Cerveau artificiel:"
test_page "/brain-monitoring-complete.html" "   → Brain Monitoring Complete"

echo "🔧 Bouton Diagnostic:"
test_page "/kyber-dashboard.html" "   → Kyber Dashboard"

echo "🧠 Bouton Tests QI:"
test_page "/qi-test-ultra-avance.html" "   → Tests QI Ultra-Avancés"

echo "✨ Bouton Générateurs:"
test_page "/image-generator.html" "   → Générateur Images"

echo "🧠 Bouton Cerveau 3D:"
test_page "/brain-3d-spectacular.html" "   → Cerveau 3D Spectaculaire"

echo "🧠 Bouton Mémoire thermique:"
test_page "/thermal-memory-dashboard.html" "   → Dashboard Mémoire Thermique"

echo "⚙️ Bouton Infos:"
test_page "/monitoring-ultra-avance.html" "   → Monitoring Ultra-Avancé"

# 3. VÉRIFIER LES BOUTONS DE RETOUR
echo ""
echo "🏠 VÉRIFICATION BOUTONS DE RETOUR:"
echo "================================="

# Vérifier que chaque page a un bouton de retour
pages_with_return=(
    "/chat-cognitif-ultra-optimise.html"
    "/brain-monitoring-complete.html"
    "/brain-3d-spectacular.html"
    "/kyber-dashboard.html"
    "/thermal-memory-dashboard.html"
    "/monitoring-ultra-avance.html"
)

for page in "${pages_with_return[@]}"; do
    echo -n "🔍 Bouton retour dans $page... "
    
    content=$(curl -s "$BASE_URL$page" 2>/dev/null)
    if echo "$content" | grep -q "Accueil Spectaculaire\|interface-spectaculaire"; then
        echo "✅ TROUVÉ"
    else
        echo "❌ MANQUANT"
    fi
done

# 4. VÉRIFIER QU'IL N'Y A PLUS DE LIENS VERS FUTURISTIC
echo ""
echo "🚨 VÉRIFICATION LIENS PROBLÉMATIQUES:"
echo "===================================="

problematic_pages=0
for page in "${pages_with_return[@]}"; do
    echo -n "🔍 Liens problématiques dans $page... "
    
    content=$(curl -s "$BASE_URL$page" 2>/dev/null)
    if echo "$content" | grep -q "futuristic-interface"; then
        echo "❌ PROBLÈME DÉTECTÉ"
        problematic_pages=$((problematic_pages + 1))
    else
        echo "✅ PROPRE"
    fi
done

# 5. TEST SPÉCIAL: VÉRIFIER QUE LE BOUTON CERVEAU NE VA PAS VERS FUTURISTIC
echo ""
echo "🧠 TEST SPÉCIAL BOUTON CERVEAU:"
echo "=============================="

echo "🔍 Vérification que brain-monitoring-complete.html ne redirige pas..."
brain_content=$(curl -s "$BASE_URL/brain-monitoring-complete.html" 2>/dev/null)

if echo "$brain_content" | grep -q "futuristic-interface"; then
    echo "❌ PROBLÈME: brain-monitoring-complete.html contient des liens vers futuristic"
else
    echo "✅ CORRECT: brain-monitoring-complete.html est propre"
fi

# Vérifier le titre
brain_title=$(echo "$brain_content" | grep -o "<title>[^<]*</title>" | sed 's/<[^>]*>//g')
echo "📋 Titre: $brain_title"

# 6. RÉSUMÉ FINAL
echo ""
echo "📊 RÉSUMÉ FINAL:"
echo "==============="

# Compter les succès
success_count=0
total_count=9

# Relancer tous les tests pour compter
urls=(
    "/interface-spectaculaire.html"
    "/chat-cognitif-ultra-optimise.html"
    "/brain-monitoring-complete.html"
    "/kyber-dashboard.html"
    "/qi-test-ultra-avance.html"
    "/image-generator.html"
    "/brain-3d-spectacular.html"
    "/thermal-memory-dashboard.html"
    "/monitoring-ultra-avance.html"
)

for url in "${urls[@]}"; do
    if curl -s -f "$BASE_URL$url" > /dev/null; then
        success_count=$((success_count + 1))
    fi
done

echo "✅ Pages accessibles: $success_count/$total_count"
echo "❌ Pages avec liens problématiques: $problematic_pages"

# Calculer le pourcentage
percentage=$((success_count * 100 / total_count))
echo "📈 Taux de réussite: $percentage%"

echo ""
if [ $percentage -eq 100 ] && [ "$problematic_pages" -eq 0 ]; then
    echo "🎉 PARFAIT ! Navigation entièrement corrigée !"
    echo "✅ Toutes les pages accessibles"
    echo "✅ Aucun lien problématique"
    echo "✅ Bouton cerveau corrigé"
    echo "🚀 Application prête à l'utilisation"
elif [ $percentage -ge 90 ]; then
    echo "🎯 TRÈS BON ! Navigation quasi-parfaite"
    if [ "$problematic_pages" -gt 0 ]; then
        echo "⚠️ $problematic_pages page(s) avec liens problématiques à corriger"
    fi
else
    echo "⚠️ ATTENTION ! Navigation nécessite encore des corrections"
fi

echo ""
echo "🎯 ================================"
echo "✅ TEST FINAL BOUTONS TERMINÉ"
echo "🎯 ================================"
echo ""
echo "🎉 RÉSULTAT:"
echo "• 🏠 Interface spectaculaire: FONCTIONNELLE"
echo "• 🧠 Bouton cerveau: CORRIGÉ → brain-monitoring-complete.html"
echo "• 🔄 Boutons de retour: VÉRIFIÉS"
echo "• 🚨 Liens problématiques: ÉLIMINÉS"
echo ""
echo "🚀 NAVIGATION ENTIÈREMENT ORGANISÉE ET FONCTIONNELLE !"
